.cm-s-material.CodeMirror {
    background-color: #2d3c46;
    color: rgba(233, 237, 237, 1);
    line-height: 1.8;
    font-family: 'Source Code Pro', monospace;
}
.cm-s-material .CodeMirror-gutters {
    background: #2d3c46;
    color: #535d7d;
    border-right: 10px solid #2d3c46;
}
.cm-s-material .CodeMirror-guttermarker,
.cm-s-material .CodeMirror-guttermarker-subtle,
.cm-s-material .CodeMirror-linenumber {
    color: #ced5db;
}
.cm-s-material .CodeMirror-cursor {
    border-left: 1px solid #f8f8f0;
}
.cm-s-material div.CodeMirror-selected {
    background: rgba(255, 255, 255, 0.15);
}
.cm-s-material.CodeMirror-focused div.CodeMirror-selected {
    background: rgba(255, 255, 255, 0.10);
}
.cm-s-material .CodeMirror-line::selection,
.cm-s-material .CodeMirror-line > span::selection,
.cm-s-material .CodeMirror-line > span > span::selection {
    background: rgba(255, 255, 255, 0.10);
}
.cm-s-material .CodeMirror-line::-moz-selection,
.cm-s-material .CodeMirror-line > span::-moz-selection,
.cm-s-material .CodeMirror-line > span > span::-moz-selection {
    background: rgba(255, 255, 255, 0.10);
}
.cm-s-material .CodeMirror-activeline-background {
    background: rgba(0, 0, 0, 0);
}
.cm-s-material .cm-keyword {
    color: rgba(199, 146, 234, 1);
}
.cm-s-material .cm-operator {
    color: rgba(233, 237, 237, 1);
}
.cm-s-material .cm-variable-2 {
    color: #80CBC4;
}
.cm-s-material .cm-variable-3 {
    color: #82B1FF;
}
.cm-s-material .cm-builtin {
    color: #ecdb82;
}
.cm-s-material .cm-atom {
    color: #ce7369;
}
.cm-s-material .cm-number {
    color: #F77669;
}
.cm-s-material .cm-def {
    color: rgba(233, 237, 237, 1);
}
.cm-s-material .cm-string {
    color: #aed479;
}
.cm-s-material .cm-string-2 {
    color: #80CBC4;
}
.cm-s-material .cm-comment {
    color: #6f8a96;
}
.cm-s-material .cm-variable {
    color: #8dacde;
}
.cm-s-material .cm-meta {
    color: #80CBC4;
}
.cm-s-material .cm-attribute {
    color: #e0bc78;
}
.cm-s-material .cm-property {
    color: #ffffff;
}
.cm-s-material .cm-qualifier {
    color: #DECB6B;
}
.cm-s-material .cm-variable-3 {
    color: #DECB6B;
}
.cm-s-material .cm-error {
    color: rgba(255, 255, 255, 1.0);
    background-color: #EC5F67;
}
.cm-s-material .cm-tag {
    color: rgb(255, 126, 148);
    background: none;
}
.cm-s-material .CodeMirror-matchingbracket {
    text-decoration: underline;
    color: white !important;
}
.cm-s-material .cm-searching {
    background-color: rgba(255, 249, 0, 0.9);
    color: #4a4a4a;
}
.cm-s-material .currentHighlighted {
	background-color: #ff8400;
}
