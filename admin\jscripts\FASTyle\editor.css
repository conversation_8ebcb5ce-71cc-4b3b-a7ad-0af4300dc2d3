.jGrowl-notification:before,
li.header:not(.search):before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    line-height: 1;
}
.jGrowl-notification.ui-state-error:before {
    content: "\f00d"
}
.jGrowl-notification.ui-state-highlight:before {
    content: "\f00c"
}
li.header:not(.search):before {
    content: "\f054"
}
li.expanded.header:not(.search):before {
    content: "\f078"
}
li.header:before {
    margin-right: 10px;
    font-size: 8px;
    width: 10px;
    text-align: center;
}
.CodeMirror {
    height: 500px!important;
    width: 100%!important
}
.fastyle {
    position: relative;
    background: #3d4c56;
    -webkit-border-radius: 10px;
            border-radius: 10px;
}
.fastyle > .content {
    width: 80%;
    float: left
}
.fastyle.full {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-border-radius: 0;
            border-radius: 0
}
.fastyle.full .sidebar {
    height: 100vh
}
.fastyle.full .CodeMirror {
    height: -webkit-calc(100vh - 85px)!important;
    height: calc(100vh - 85px)!important
}
.fastyle.full .CodeMirror-merge,
.fastyle.full .CodeMirror-merge .CodeMirror {
    height: -webkit-calc(100vh - 115px)!important;
    height: calc(100vh - 115px)!important;
}
.fastyle .form_row {
    margin: 0;
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #2d3b46;
}
.fastyle ul,
.fastyle li {
    width: 100%;
    padding: 0;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    list-style-type: none
}
/* Editor */

.fastyle .sidebar {
    -webkit-flex-basis: 20%;
    -ms-flex-preferred-size: 20%;
    flex-basis: 20%;
    float: left;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    overflow-y: auto;
    width: 20%;
    height: 585px;
    -webkit-border-radius: 10px;
            border-radius: 10px;
}
.fastyle.full .sidebar {
    -webkit-border-radius: 0;
            border-radius: 0
}
.fastyle .sidebar .simplebar-content {
    overflow-y: scroll!important
}
.fastyle .sidebar ul.search + ul {
    margin-top: 50px;
}
.fastyle .sidebar ul li {
    margin: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    cursor: pointer;
    padding: 10px 25px 10px 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #a5afb5;
    font-size: 11px;
    position: relative;
    width: auto;
    font-weight: 500;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
}
.fastyle .sidebar ul li.tier-0 {
    margin-left: -15px
}
.fastyle .sidebar ul li.tier-1 {
    margin-left: 0px
}
.fastyle .sidebar ul li.tier-2 {
    margin-left: 20px
}
.fastyle .sidebar ul li.tier-3 {
    margin-left: 40px
}
.fastyle .sidebar ul li.tier-4 {
    margin-left: 60px
}
.fastyle .sidebar ul li.active {
    color: #ffffff;
    font-weight: bold;
    background: #2d3c46;
    -webkit-border-radius: 6px 0 0 6px;
            border-radius: 6px 0 0 6px;
    margin-left: 15px;
    padding-left: 15px
}
.fastyle .sidebar ul li.active.tier-0 {
    margin-left: 0
}
.fastyle .sidebar ul li.active.tier-2 {
    margin-left: 35px
}
.fastyle .sidebar ul li.active.tier-3 {
    margin-left: 55px
}
.fastyle .sidebar ul li.active.tier-4 {
    margin-left: 75px
}
.fastyle .sidebar .subgroup {
    text-transform: uppercase;
    font-weight: 700;
    opacity: 0.8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    padding-left: 25px;
    font-size: 9px;
}
.fastyle .sidebar ul li i {
    padding-right: 10px;
    color: #707f8a;
}
.fastyle .switcher .content > div > div.not-saved,
.fastyle .sidebar ul li.not-saved {
    font-style: italic
}
.fastyle .sidebar ul li.not-saved:after, .fastyle .switcher .content > div > div.not-saved a:after {
    content: '';
    width: 7px;
    height: 7px;
    background: #ffb920;
    display: inline-block;
    -webkit-border-radius: 999em;
    border-radius: 999em;
    position: absolute;
    top: 13px;
    right: 10px;
}
.fastyle .switcher .content > div > div.not-saved {
    padding-right: 25px
}
.fastyle .sidebar ul li.not-saved.active:after,
.fastyle .switcher .content > div > div.not-saved:after {
    right: 10px
}
.fastyle .sidebar ul li[data-status="modified"],
.fastyle .sidebar ul li[status="modified"] {
    color: #c3de82;
}
.fastyle .sidebar ul li[data-status="original"],
.fastyle .sidebar ul li[status="original"] {
    color: #75b9e8;
}
.fastyle .sidebar ul li .fa-exclamation-triangle {
    color: #fff230;
    float: right;
    font-size: 11px;
    margin: 0 0 0 5px
}
.fastyle .sidebar ul li.active .fa-exclamation-triangle {
    color: #fff077
}
.fastyle .sidebar ul li .action {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px
}
.fastyle .sidebar li.header {
    padding: 10px;
    margin: 0
}
.fastyle .sidebar li.header+ul {
    display: none!important
}
.fastyle .sidebar li.header.expanded+ul {
    display: block!important
}
.fastyle .sidebar li+ul .header {
    padding-left: 15px
}
.fastyle .sidebar li+ul ul {
    padding-left: 20px;
}
.fastyle:after {
    content: "";
    display: block;
    clear: both
}
/* Bar */

.fastyle .bar {
    background: #2d3b46;
    color: #fff;
    width: 100%;
    padding: 12px 15px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}
.fastyle .bar > * {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
}
.fastyle .bar .sidebar {
    height: auto;
    float: none;
    display: none;
    background: none;
}
.fastyle .bar .label {
    font-size: 15px;
}
.fastyle .bar .label .meta {
    font-size: 11px;
    margin-left: 10px;
    color: #99a0a7
}
.fastyle .bar .actions {
    padding: 0;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
}
.fastyle .bar.top {
    min-height: 50px;
    -webkit-border-radius: 10px 10px 0 0;
            border-radius: 10px 10px 0 0;
}
.fastyle .bar.bottom {
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 0;
}
.fastyle .bar.bottom .actions {
    padding: 15px 25px;
}
.fastyle .button {
    padding: 5px 8px;
    background: #ced5db;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    margin: 0 4px;
    display: inline-block;
    cursor: pointer;
    color: #2d3b46;
    border: none;
    font-size: 12px;
    outline: none;
    line-height: 1;
}
.fastyle .bar .actions > i {
    margin: 0 0 0 20px;
    cursor: pointer
}
.fastyle .button.current {
    background: #60a8d2;
}
.fastyle .button.original {
    background: #ab5795;
}
.fastyle .sidebar .nothing-found,
.fastyle .bar .actions .button:not(.visible) {
    display: none
}
.fastyle .bar[data-status="modified"] .actions .revert,
.fastyle .bar[status="modified"] .actions .revert,
.fastyle .bar[data-status="modified"] .actions .diff,
.fastyle .bar[status="modified"] .actions .diff {
    display: -webkit-box;
}
.fastyle .bar .actions .diff.active {
    background: #a981d5;
    color: #fff;
}
.fastyle .bar[data-status="original"] .actions .delete,
.fastyle .bar[status="original"] .actions .delete {
    color: #fff;
    background: #b75c5c;
    display: inline-block
}
.fastyle .bar .actions .add {
    background: #aed581
}
.fastyle .bar .actions .button.quickmode.enabled {
    background: #d5ca81
}
/* Switcher */

.fastyle .switcher {
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #3d4c56;
    position: relative;
    height: 45px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
        -ms-flex-align: end;
            align-items: flex-end;
    -webkit-border-radius: 10px;
            border-radius: 10px;
}
.fastyle.full .switcher {
    -webkit-border-radius: 0;
            border-radius: 0
}
.fastyle .sidebar .search {
    position: absolute;
    width: 100%;
    background: #3d4c56;
    z-index: 999;
    height: 45px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
        -ms-flex-align: center;
            align-items: center;
    top: 0;
    left: 0;
}
.fastyle.full .sidebar .search {
    position: fixed;
    width: 20%
}
.fastyle .sidebar .search li {
    width: 100%;
    padding: 0 15px;
}
.fastyle .sidebar .search li input[type="textbox"] {
    background: #2d3c46;
}
.fastyle .bar .swiper-button-prev,
.fastyle .bar .swiper-button-next {
    background: #3d4d56;
    top: 50%;
}
.fastyle .bar .swiper-button-prev {
    -webkit-box-shadow: 15px 0 15px -5px #3d4d56, -10px 0 0 #3d4d56;
            box-shadow: 15px 0 15px -5px #3d4d56, -10px 0 0 #3d4d56;
}
.fastyle .bar .swiper-button-next {
    right: 0;
    -webkit-box-shadow: -15px 0 15px -5px #3d4d56;
            box-shadow: -15px 0 15px -5px #3d4d56;
    -webkit-border-radius: 0 10px 0 0;
            border-radius: 0 10px 0 0;
}
.fastyle .switcher .content {
    overflow: hidden;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    white-space: nowrap;
    padding: 0 50px;
}
.fastyle .switcher .swiper-wrapper {
    height: auto;
}
.fastyle .switcher .content > div > div {
    padding: 10px 10px 10px 25px;
    color: #a5afb5;
    cursor: pointer;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-overflow: ellipsis;
}
.fastyle .switcher .content > div > div.active {
    background: #2d3b46;
    color: #fff;
    -webkit-border-radius: 10px 10px 0 0;
            border-radius: 10px 10px 0 0;
}
.fastyle .switcher .content > div > div a {
    max-width: 100%;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: inherit;

}
.fastyle .switcher .content > div > div.active:before,
.fastyle .switcher .content > div > div.active a:before,
.fastyle .switcher .content > div > div.active:after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0
}
.fastyle .switcher .content > div > div.active:before,
.fastyle .switcher .content > div > div.active a:before {
    width: 20px;
    height: 20px;
    -webkit-border-radius: 10px;
            border-radius: 10px;
    background: #3d4c56;
    left: -20px;
    z-index: -1;
}
.fastyle .switcher .content > div > div.active a:before {
    left: 100%
}
.fastyle .switcher .content > div > div.active:after {
    background: #2d3b46;
    width: -webkit-calc(100% + 16px);
    width: calc(100% + 16px);
    height: 10px;
    left: -8px;
    z-index: -2
}
.fastyle .switcher .delete {
    position: absolute;
    top: 13px;
    left: 10px;
    font-size: 10px;
}
.fastyle .switcher .content > div > div:only-child {
    padding-left: 10px;
}
.fastyle .switcher .content > div > div:only-child .delete {
    display: none
}
/* Inputs */

.fastyle input[type="textbox"] {
    padding: 6px 12px;
    margin: 0;
    -webkit-appearance: none;
    border: none;
    background: rgba(0, 0, 0, .2);
    color: #fff;
    outline: none;
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-border-radius: 6px;
            border-radius: 6px;
}
.fastyle .actions input[type="textbox"] {
    width: auto;
    padding: 4px 8px 3px;
    -webkit-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
    margin-left: 5px;
    line-height: 1;
}
.fastyle .actions input[type="textbox"] + span {
    margin-left: 0!important;
    -webkit-border-radius: 0 6px 6px 0!important;
    border-radius: 0 6px 6px 0!important
}
/* CodeMirror overlay */

.CodeMirror .overlay {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .15);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    -webkit-border-radius: 6px;
    border-radius: 6px
}
/* CodeMirror search dialog */

.fastyle .CodeMirror-dialog .button {
    height: 17px;
    vertical-align: top;
    background: #d6d6d6;
    margin: 0;
    -webkit-border-radius: 6px 0 0 6px;
    border-radius: 6px 0 0 6px;
    color: #515d6b;
    font-family: 'Lucida Grande', Tahoma, Verdana, Arial, sans-serif;
    font-size: 11px;
    line-height: 19px;
}
.fastyle .CodeMirror-dialog .button:last-of-type {
    -webkit-border-radius: 0 6px 6px 0;
    border-radius: 0 6px 6px 0;
    border-left: 1px solid #7f8b9a;
}
.fastyle .CodeMirror-dialog div:last-child {
    margin-top: 10px
}
.fastyle .button.prev:before,
.fastyle .button.next:before {
    line-height: 20px;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
}
/* Misc */

.fastyle .delete {
    color: #e06971
}
.fastyle textarea {
    -webkit-border-radius: 0;
    border-radius: 0;
    border: none;
    -webkit-appearance: none;
    background: transparent;
    color: #fff;
    font-family: 'Source Code Pro', monospace;
}
/* jGrowl */

.jGrowl {
    position: absolute;
    display: block
}
.jGrowl-notification.ui-state-highlight,
.jGrowl-notification.ui-state-error {
    border: none;
    background: hsla(0, 0%, 100%, 0.95);
    font-weight: 400;
    color: #676767;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    -webkit-border-radius: 3px;
    border-radius: 3px;
    padding-left: 55px;
    position: relative;
    line-height: 1.6
}
.jGrowl-notification .jGrowl-close {
    border: none;
    padding: 0;
    margin: 0;
    background: none;
    color: inherit
}
.jGrowl-notification.ui-state-highlight:before,
.jGrowl-notification.ui-state-error:before {
    display: block;
    text-align: center;
    background: #71c341;
    color: #fff;
    width: 30px;
    height: 30px;
    -webkit-border-radius: 999em;
    border-radius: 999em;
    line-height: 30px;
    font-size: 16px;
    float: left;
    margin: 0 10px 0 0;
    position: absolute;
    top: 15px;
    left: 10px
}
.jGrowl-notification.ui-state-error:before {
    background: #c34156
}
