import colorsys
import re

fromcolor = "#CC0101"
tocolor = "#ff27fb"
if fromcolor.startswith('#'):
	fromcolor = fromcolor[1:]

if tocolor.startswith('#'):
	tocolor = tocolor[1:]

from_r, from_g, from_b = bytes.fromhex(fromcolor)
from_h, from_s, from_v = colorsys.rgb_to_hsv(from_r, from_g, from_b)
print(from_h, from_s, from_v)

to_r, to_g, to_b = bytes.fromhex(tocolor)
to_h, to_s, to_v = colorsys.rgb_to_hsv(to_r, to_g, to_b)
print(to_h, to_s, to_v)


with (open("logo_development.svg", 'r') as inp):
	with open("logo_mgmt.svg", 'w') as oup:
		for line in inp:
			match = re.search(r"\.cls-(\d+){fill:#([\dA-z]+);}", line)
			if match is not None:
				cur_h, cur_s, cur_v = colorsys.rgb_to_hsv(*bytes.fromhex(match.group(2)))
				offset_h = cur_h - from_h
				if from_s == 0:
					offset_s = 0
				else:
					offset_s = cur_s / from_s
				if from_v == 0:
					from_v = 0
				else:
					offset_v = cur_v / from_v

				h = (to_h + offset_h)
				s = (to_s * offset_s)
				v = (to_v * offset_v)

				h = abs(h) % 1
				s = min(max(abs(s), 0), 1)
				v = min(max(abs(v), 0), 255)

				out = colorsys.hsv_to_rgb(h, s, v)
				out = [int(i) for i in out]
				out = bytearray(out)
				oup.write("\t\t.cls-" + match.group(1) + "{fill:#" + out.hex() + ';}\n')
			else:
				oup.write(line)


