<style>
	.new, .fix, .delete, .change, .rules,
	.new:hover, .fix:hover, .delete:hover, .change:hover, .rules:hover {
		color: white;
	}

	.new{
		background-color:#5cd65c;
		border-color:#5cd65c !important;
	}

	.fix{
		background-color:#4d94ff;
		border-color:#4d94ff !important;
	}

	.delete{
		background-color:#ff6666;
		border-color:#ff6666 !important;
	}

	.change{
		background-color:#ffd480;
		border-color:#ffd480 !important;
	}

	.rules{
		background-color:#c266ff;
		border-color:#c266ff !important;
	}
</style>

<div class="d-md-none mb-3 btn-group btn-group-sm" role="group" aria-label="Basic example">
  <a href="?<?=http_query($query, ['type' => 'new']); ?>" class="btn new">New</a>
  <a href="?<?=http_query($query, ['type' => 'delete']); ?>" class="btn delete">Deleted</a>
  <a href="?<?=http_query($query, ['type' => 'fix']); ?>" class="btn fix">Fixed</a>
  <a href="?<?=http_query($query, ['type' => 'change']); ?>" class="btn change">Changed</a>
  <a href="?<?=http_query($query, ['type' => 'rules']); ?>" class="btn rules">Rules</a>
</div>

<div class="card">
	<div class="card-header d-flex justify-content-between">
		<h5 class="card-title mb-0">Changelog</h5>
		<div class="d-none d-md-block btn-group btn-group-sm mb-n1" role="group" aria-label="Basic example">
		  <a href="?<?=http_query($query, ['type' => 'new']); ?>" class="btn new">New</a>
		  <a href="?<?=http_query($query, ['type' => 'delete']); ?>" class="btn delete">Deleted</a>
		  <a href="?<?=http_query($query, ['type' => 'fix']); ?>" class="btn fix">Fixed</a>
		  <a href="?<?=http_query($query, ['type' => 'change']); ?>" class="btn change">Changed</a>
		  <a href="?<?=http_query($query, ['type' => 'rules']); ?>" class="btn rules">Rules</a>
		</div>
	</div>
	<div class="table-responsive">
		<table class="table table-striped mb-0">
			<thead class="table-secondary">
				<th style="width: 1px"></th>
				<th style="width: 4px">Author</th>
				<th style="width: 50%">Update</th>
				<th style="width: 1px">Platform</th>
				<th class="text-nowrap" style="width: 1px;text-align: right;padding-right: 10px">Date</th>
			</thead>

			<tbody>
				<?php foreach($changelogs as $changelog) : ?>
					<tr class="align-middle">
						<td class="<?=$changelog['type']; ?> border-top-0" width="1"></td>
						<td width="1">
							<a href="https://www.fearlessrp.net/member.php?action=profile&uid=<?=$users[$changelog['userID']]['uid'];?>" target="_blank">
								<div class="avatar-overlay" title="<?=$users[$changelog['userID']]['username']; ?>">
									<img src="<?=$users[$changelog['userID']]['avatar']; ?>">
									<div class="text">#<?=$changelog['id']; ?></div>
								</div>
							</a>
						</td>
						<td class="align-middle">
							<?php if (!empty($changelog['longer_desc']) || count($contributors[$changelog['id']]) > 0 ) : ?>
								<a class=" text-decoration-none"><?=$changelog['description']; ?> <span class="badge badge-primary">?</span></a>
							<?php else : ?>
								<?=$changelog['description']; ?>
							<?php endif; ?>

							<?php if ($changelog['tid'] != 0) : ?>
								<a class="badge badge-primary" href="https://www.fearlessrp.net/showthread.php?tid=<?=$changelog['tid']; ?>">Thread</a>
							<?php endif; ?>

							<?php if ($changelog['hidden'] == 2) : ?>
								<span class="badge badge-danger">Developer</span>
							<?php elseif ($changelog['hidden'] == 1) : ?>
								<span class="badge badge-info">Hidden</span>
							<?php endif; ?>
						</td>
						<td class="align-middle"><?=$changelog['platform']; ?></td>
						<td class="align-middle" style="text-align: right; padding: 10px"><?=date("j M Y <\b\\r> h:ia", strtotime($changelog['timestamp'])); ?></td>
					</tr>
					
				<?php endforeach; ?>
			</tbody>
		</table>
	</div>
</div>

<?php if ($pages > 1) : ?>
	<ul class="pagination mt-3">
		<?php for ($i = ($curPage - 10); $i < $curPage; $i++) : ?>
			<?php if ($i > 0) : ?>
				<li class="page-item"><a class="page-link" href="?<?=http_query($query, ['page' => $i]); ?>"><?=$i; ?></a></li>
			<?php endif; ?>
		<?php endfor; ?>
		<li class="page-item active"><span class="page-link"><?=$curPage; ?></span></li>
		<?php for ($i = 1; $i <= 10; $i++) : $pagId = ($curPage + $i); ?>
			<?php if ($pagId > 0 && $pagId <= $pages) : ?>
				<li class="page-item"><a class="page-link" href="?<?=http_query($query, ['page' => $pagId]); ?>"><?=$pagId; ?></a></li>
			<?php endif; ?>
		<?php endfor; ?>
		<?php if (($curPage + 11) < $pages) : ?>
			<li class="page-item"><span class="page-link">...</span></li>
			<li class="page-item"><a class="page-link" href="?<?=http_query($query, ['page' => $pages]); ?>"><?=$pages; ?></a></li>
		<?php endif; ?>
	</ul>
<?php endif; ?>

<?php eval("\$page= \"".$templates->get('footer')."\";"); echo $page; ?>