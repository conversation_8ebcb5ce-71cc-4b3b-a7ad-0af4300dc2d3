<?php

if (!defined('IN_MYBB')) {
	die('Direct access not permitted');
}

use League\Flysystem\Filesystem;
    use League\Flysystem\PhpseclibV2\SftpAdapter;
    use League\Flysystem\PhpseclibV2\SftpConnectionProvider;
    use League\Flysystem\UnixVisibility\PortableVisibilityConverter;

    global $env, $plugins;

    require('advdup/class.dupe.php');
	require('advdup/class.repository.php');

	$servers = [
		'v5p',
		'v4b1',
		'build',
		'srfl',
        'v2d'
	];

	$ftp = new Filesystem(new SftpAdapter(
	    new SftpConnectionProvider(
	        $env['FTP_HOST'],
            $env['FTP_USER'],
            null,
            $env['FTP_KEY'],
            null,
	    22
        ),
        '/',
        PortableVisibilityConverter::fromArray([
            'file' => [
                'public' => 0755,
                'private' => 0604,
            ],
            'dir' => [
                'public' => 0755,
                'private' => 0604,
            ],
        ])
    ));

	foreach($servers as $server) {
		$repositories[$server] = new Repository($ftp, $steamid, $server);
	}

	if (isset($_POST['method'])) {
		$files = is_array($_POST['file']) ? $_POST['file'] : [$_POST['file']];

		$files = array_map(function($item) use ($repositories) {
		    if ($file = decrypt($item)) {
		        $target = explode('/', $file);

		        try {
                    return $repositories[$target[0]]->find(
                        implode('/', array_slice($target, 2))
                    );
                } catch (\TypeError $e) {
		            return null;
                }
			}

			return null;
		}, $files);

		$files = array_filter($files);

		foreach($files as $file) {
			switch($_POST['method']) {
				case 'get':
					if (count($files) > 1) {
						exit('Can\'t download multiple files.');
					}

					header('Content-Description: File Transfer');
					header('Content-Type: application/octet-stream');
					header('Content-Disposition: attachment; filename=' .$file->filename());
					header('Content-Transfer-Encoding: binary');
					header('Content-Length: '.strlen($file->contents()));

                    ob_end_flush();

                    exit($file->contents());
				case 'put':
				    if (!in_array($_POST['server'], $servers)) {
						break;
					}

					$file->copy($repositories[$_POST['server']]);

					break;
				case 'patch':
					if (count($files) > 1) {
						exit('Can\'t rename multiple files.');
					}

					$file->rename($_POST['name']);

					break;
				case 'delete':
					$file->delete();

					break;
			}
		}

		echo '<script>window.location = "character.php?area=advdup"</script>';

        // Can't use because some dipshit programmed that character page like a zombie
        //header('Location: '.$mybb->settings['bburl'].'/character.php?area=advdup');
		exit;
	} else {
        // $plugins->run_hooks("character_start");
        // eval("\$page= \"".$templates->get('headerinclude')."\";");
        // echo $page;
        // eval("\$page= \"".$templates->get('header')."\";");
        // echo $page;

		include('advdup/index.php');
	}
	exit;
