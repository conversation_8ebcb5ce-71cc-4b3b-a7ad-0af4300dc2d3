<?php

use \League\Flysystem\Filesystem;
use \League\Flysystem\DirectoryAttributes;
use \League\Flysystem\FileAttributes;

class Repository
{
	private ?array $dupes = [];
	public Filesystem $ftp;
	private string $steamid;
	public string $server;

	public function __construct($ftp, $steamid, $server)
	{
		$this->ftp = $ftp;
		$this->steamid = str_replace(':', '_', strtolower($steamid));
		$this->server = $server;
	}

	public function get()
	{
		if (empty($this->dupes)) {
			$files = $this->list($this->directory());

			$files = array_flatten($files);

			foreach($files as $file) {
			    $this->dupes[] = new Dupe($file, $this);
			}
		}

		return $this->dupes;
	}

	private function list($dir)
    {
        $contents = $this->ftp->listContents($dir)->toArray();

        return array_map(function ($item) {
            if ($item instanceof DirectoryAttributes) {
                return $this->list($item->path());
            }

            return $item;
        }, $contents);
    }

	public function find($filename)
	{
	    $list = array_flatten($this->list($this->directory()));

	    $file = end(
	        @array_filter($list, function($file) use ($filename) {
	            return $file->path() == $this->directory($filename);
            })
        );

		$dupe = new Dupe($file, $this);

		$dupe->contents();

		return $dupe;
	}

	public function directory($append = '')
	{
        return $this->server.'/'.
            $this->steamid.'/'.
            $append;
	}

	public function deleteEmptyDirectories($dir)
    {
        $list = $this->ftp->listContents($dir)->toArray();

        if (count($list) == 0) {
            $this->ftp->deleteDirectory($dir);
        } else {
            return;
        }

        $tree = explode('/', $dir);
        array_pop($tree);

        $this->deleteEmptyDirectories(
            implode('/', $tree)
        );
    }
}
