<?php
	$props = file( "character/proplist.txt" );
	$count = count( $props );
	
	if (!defined('IN_MYBB'))
	{
		die( 'Hacking attempt' );
	}
?>
<html>
<head>
	<title>Fearless - Banned props list</title>
</head>
<body>
	<p>
		<a href="character.php"><strong>Go back</strong></a>
	</p>
	
	<p>
		<strong>Current list of banned props in CityRP</strong><br />
		This is the complete list of all props currently banned from spawning in the gamemode.<br />
		For builders this list is an easy tool to consult to make sure you don't need any banned props.<br /><br />
		
		<strong>Currently: <?php echo $count; ?> banned props</strong>
	</p>
	
	<?php		
		// loop all props
		foreach( $props as $p )
		{
			// strip the quotes and comma
			$p = str_replace( ",", "", $p );
			$p = str_replace( '"', "", $p );

			echo $p ."<br />";
		}
	?>
</body>
</html>