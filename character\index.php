<?php

if (!defined('IN_MYBB')) {
	die('Direct access not permitted');
}

$uid = $mybb->user['uid'];
$steamid = $db->newQuery("SELECT steamid FROM #forum#.fearless_steamids WHERE id = ".$uid)->fetch_row();
$steamid = $steamid[0];

if (empty($steamid))
{
	eval("\$page= \"".$templates->get('steam_login')."\";");
	$page = str_replace('%ref_url%', 'https://'.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'], $page);

	error($page);
}

$plugins->run_hooks("character_start");
eval("\$page= \"".$templates->get('headerinclude')."\";");
echo $page;
eval("\$page= \"".$templates->get('header')."\";");
echo $page;
echo "<link type='text/css' rel='stylesheet' href='character/css/style.css'/>";

$areas = [
	'advdup',
	'points',
	'bannedprops',
	'jobplaytime',
	'invman'
];

$area = null;
foreach ($areas as $a){
	if($_GET['area'] == $a){
		$area = $a;
		break;
	}
}

if (!is_null($area)){
	require($area . '.php');
	exit;
}
?>

<script>
	let myLabels = document.querySelectorAll('.lbl-toggle');
	Array.from(myLabels).forEach(label => {
		label.addEventListener('keydown', e => {
	    	// 32 === spacebar
	    	// 13 === enter
		    if (e.which === 32 || e.which === 13) {
	      		e.preventDefault();
	      		label.click();
	    	};
		});
	});
</script>

<div class='character_title_box'>
	<p class="character_title"> Character Page
    <p class="character_sub_title"> Everything you need to know about your character
</div>
<div class='character_nav'>
	<a href="character.php?area=advdup" target="_parent"><button class="top_block">Manage Advanced Dupes</button></a>
	<a href="/signatures.php" target="_parent"><button class="top_block">Personalized Forum Signature</button></a>
	<a href="flstats.php" target="_parent"><button class="top_block">Global Statistics</button></a>
	<a href="character.php?area=bannedprops" target="_parent"><button class="top_block">Banned Props List</button></a>
	<a href="../GuidetoRP.pdf" target="_parent"><button class="top_block">Roleplay Guide</button></a>
	<a href="character.php?area=jobplaytime" target="_parent"><button class="top_block">Job Play Time</button></a>
</div><br/>
<div id="container">
	<?php

	function convert_steamid($sid){
		if (preg_match('/^STEAM_/', $sid)) {
	   		$parts = explode(':', $sid);
	   		return bcadd(bcadd(bcmul($parts[2], '2'), '76561197960265728'), $parts[1]);
		}
		else {
			$sid = bcsub($sid, '76561197960265728');

			return 'STEAM_0:'.bcmod($sid, '2').':'.bcdiv($sid, 2);
		}
	}

	$steamid64 = convert_steamid($steamid);
	$activePackages = $db->newQuery(
		"SELECT package, steamid, created_at, expires_at, active
		FROM #cityrp#.tebex_packages
		WHERE active = 1 AND steamid = '".$steamid64."' ")->fetch_row();

	if (!empty($activePackages)){
		$package = $activePackages[0];
		$steamid64 = $activePackages[1];
		$packageCreated = $activePackages[2];
		$packageExpires = $activePackages[3];
		$packageIsActive = $activePackages[4];

		$expiry_date = date_create($packageExpires);

		if ($package == '5141486') { $package = 'Supporter'; }
		if ($package == '5141487') { $package = 'Supporter+'; }
	}

	$player = $db->newQuery(
		"SELECT _Igname, _Description, _Clan, _Money, _Inventory,
				_TimePlayed, _Points, _Plate, _Online, _Server,
				_svrank, _Lastonline, _Invvalue, _Gender, _VeteranType
		FROM #cityrp#.players
		WHERE _SteamID = '".$steamid."' ")->fetch_row();

	$igName = $player[0];
	$description = $player[1];
	$clan = $player[2];
	$money = $player[3];
	$inventory = $player[4];
	$timePlayed = $player[5];
	$points = $player[6];
	$plate = $player[7];
	$isOnline = $player[8];
	$server = $player[9];
	$rank = $player[10];
	$lastOnline = $player[11];
	$invValue = $player[12];
	$gender = $player[13];
	$veteranRankType = $player[14];

	$avatarPath = 'character/images/';
	if ($rank == "superdeveloper" || $rank == "superadmin" || $rank == "admin" || $rank == "owner")
		$avatar = $avatarPath.'admin.png';
	elseif ($rank == "developer" || $rank == "developeradmin" || $rank == "developertrialadmin")
		$avatar = $avatarPath.'dev.png';
	elseif (!empty($activePackages)){
		if ($gender == "Male") $avatar = $avatarPath.'male_suit.png';
		else $avatar = $avatarPath.'female_dress.png';
	}
	elseif (empty($activePackages)){
		if ($gender == "Male") $avatar = $avatarPath.'male_civ.png';
		else $avatar = $avatarPath.'female_civ.png';
	}
	else $avatar = $avatarPath.'default_civ.png';

	echo '<div id="menu">';
		echo '<img class="profile_img" src="'.$avatar.'">';

		echo "<h2 align='left'>Your Character</h2>";
		// Get Ingame Name
		echo "<strong><span id='tbl_head'>Name:</span></strong> ".$igName;
		// Get Gender
		echo "<br /><strong><span id='tbl_head'>Gender:</span></strong> ".$gender;
		// Get Description
		if($description == "") $description = "<i>None</i>";
		echo "<br /><strong><span id='tbl_head'>Description:</span></strong> ".$description;
		// Get Clan
		//if($clan == "") { $clan = "<i>None</i>"; };
		//echo "<br /><strong><span id='tbl_head'>Clan:</span></strong> (Coming soon...)";//.$clan;
		// Get RP Points
		echo "\n<br><strong><span id='tbl_head'>RP Points:</span></strong> ".$points." points earned";
		// Get License Plate
		echo "\n<br><strong><span id='tbl_head'>License plate:</span></strong> ".$plate;
	echo '</div>';

	$badgePath = '../images/badges/';
	$additionalRankBadge = null;
	if($rank == "owner") $rankBadge = $badgePath.'owner.svg';
	elseif($rank == "superadmin" || $rank == "superdeveloper") $rankBadge = $badgePath.'management.svg';
	elseif($rank == "developer") $rankBadge = $badgePath.'developer.svg';
	elseif($rank == "developeradmin"){
		$rankBadge = $badgePath.'developer.svg';
		$additionalRankBadge = $badgePath.'admin.svg';
	}
	elseif($rank == "developertrialadmin"){
		$rankBadge = $badgePath.'developer.svg';
		$additionalRankBadge = $badgePath.'trialadmin.svg';
	}
	elseif($rank == "admin") $rankBadge = $badgePath.'admin.svg';
	elseif($rank == "eventmanager") $rankBadge = $badgePath.'event_manager.svg';
	elseif($rank == "trialadmin") $rankBadge = $badgePath.'trialadmin.svg';
	elseif($rank == "contributor") $rankBadge = $badgePath.'contributor_new.svg';
	elseif($rank == "eventcoordinator") $rankBadge = $badgePath.'event_coordinator_blue.svg';
	elseif($rank == "user") $rankBadge = $badgePath.'registered.svg';
	elseif($rank == "veteran"){
		if ($veteranRankType == 'superadmin')
			$rank = $badgePath.'veteran-superadmin.svg';
		elseif($veteranRankType == 'developer')
			$rankBadge = $badgePath.'veteran_developer.svg';
		else
			$rankBadge = $badgePath.'veteran_admin.svg';
	}
	elseif(!empty($activePackages) && $rank == "user"){
		if ($package == "Supporter") $rankBadge = $badgePath.'supporter.svg';
		elseif ($package == "Supporter+") $rankBadge = $badgePath.'supporter_plus.svg';
	}

	echo '<div id="menu3">';
		echo "<h2 align='left'>OOC Information</h2>";

		echo "<strong><span id='tbl_head'>Rank:</span></strong><br />";
		echo "<img style='width: 100px' class='rank_badge' src=".$rankBadge." /><br />";
		if (!is_null($additionalRankBadge))
			echo "<img style='width: 100px' class='rank_badge' src=".$additionalRankBadge." /><br />";
		// Check if player has purchased a package and change text accordingly
		$rankHasSupporterPlus = [
			"owner",
			"superadmin",
			"superdeveloper",
			"admin",
			"developer",
			"developertrialadmin",
			"developeradmin",
			"eventmanager"
		];

		$rankHasSupporter = [
			"trialadmin",
			"eventcoordinator",
			"contributor"
		];

		$exempt = false;
		foreach ($rankHasSupporterPlus as $r){
			if ($rank == $r) {
				echo "<br /><strong><span id='tbl_head'>Supporter:</span></strong> You have Supporter+ perks. Thank you for your hard work!";
				$exempt = true;
				break;
			}
		}

		foreach ($rankHasSupporter as $r){
			if ($rank == $r) {
				echo "<br /><strong><span id='tbl_head'>Supporter:</span></strong> You have Supporter perks. Thank you for your hard work!";
				$exempt = true;
				break;
			}
		}

		if(!$exempt){
			if(!empty($activePackages))
				echo "<br /><strong><span id='tbl_head'>Supporter:</span>  Expires | </strong> ".date_format($expiry_date, 'd-M-Y H:i');
			else
				echo "<br /><strong><span id='tbl_head'>Supporter:</span></strong> No active subscription. <a href='https://store.fearlessrp.net/'>Click here</a> to buy.";
		}

		// Get time played
		$timePlayed = floor($timePlayed / 3600);
		echo "\n<br><strong><span id='tbl_head'>Time played:</span></strong> <span class='counter'>".$timePlayed."</span> Hours";

		// Get if player is online on server
		if($isOnline == 0)
			$isOnline = "No";
		elseif($isOnline == 1)
			$isOnline = "Yes - ".strtoupper($server)."";
		echo "<br /><strong><span id='tbl_head'>Online on server:</span></strong> ".$isOnline;
		// Get when was the player last online
		echo "<br /><strong><span id='tbl_head'>Last Online:</span></strong> ".date('d-M-Y H:i', $lastOnline);
	echo '</div>';

	echo '<div id="menu2">';
		echo "<h2 align='left'>Your Financial Information</h2>";
		// Net Worth
		echo "<strong><span id='tbl_head'>Total Net Worth:</span></strong> $<span class='counter'>".number_format((float)$money+$invValue, 0, '.', ',')."</span>";
		// Get Money
		echo "<br /><strong><span id='tbl_head'>Wallet Value:</span></strong> $<span class='counter'>".number_format((float)$money, 0, '.', ',')."</span>";
		// Get Inventory Value
		echo "<br /><strong><span id='tbl_head'>Inventory Value:</span></strong> $<span class='counter'>".number_format((float)$invValue, 0, '.', ',')."</span>";
		// Get Inventory Capacity
		$std_cap = 50; //default, with no additional pockets - 50
		$add_cap = 350; //additional space from pockets
		$inv_cap_total = $std_cap + $add_cap;//$std_cap + $add_cap; //Calculate maximum inventory capacity based on the number of pockets

		$inv_curr = 200; //Calculate current size of inventory based on number of items and their size
		//echo "<br /><strong><span id='tbl_head'>Inventory Capacity:</span> </strong> ? / ? (Coming soon...)<br /><progress id='inv_cap' value='".$inv_curr."' max='".$inv_cap_total."'></progress>";
		// Get Inventory
		?>
		<input id="toggle" type="checkbox" unchecked>
		<label for="toggle">View Inventory</label>
		<div id="expand">
			<section>
				<?php
				// Inventory script starts
				include(MYBB_ROOT.'/inc/plugins/flplug/items.php');
				echo "<table id='inv_tbl'>";
					$inv = explode("; ", $inventory);
					foreach($inv as $invitem)
					{
						$invitem = explode(": ", $invitem, 2);
						$invlist[$invitem[0]] += $invitem[1];
					}
					$cat = "";
					foreach($items as $i => $v)
					{
						if($v[0] != $cat && $invlist[$i] != 0)
						{
							$cat = $v[0];
							// Insert blank spacer
							echo "<tr><td colspan=2>&nbsp;</td></tr>";
							// Row for categories and quantity
							echo "<tr><td><span id='tbl_head'><b>".$cat."</b></span></td><td><b>Qt.</b></td></tr>";
						}
						if(!isset($invlist[$i]))
							$invlist[$i] = 0;
							if($invlist[$i] != 0)
							// Row for Item
							echo '<tr><td>'.$v[1].'</td><td>x '.$invlist[$i].'</td>';
					}
				?>
				</table>
			</section>
		</div>
	</div>
	<div id="menu4">
		<?php
		echo "<h2 align='left'>Your Roleplay Points: <span id='tbl_head_large'><span class='counter'>".$points."</span></span></h2>";
		$sql = $db->newQuery( "SELECT admin, reason FROM #cityrp#.rppoints WHERE target_steam = '".$steamid."'" )->fetch_all();
		if( !empty( $sql ) )
		{
			?>
			<div id="scrollpanel"><table style="width:100%;" cellpadding="3">
				<tr style="color:#00808a;font-size:20px;">
					<td><strong>Received from</strong></td>
					<td><strong>Reason</strong></td>
				</tr>
			<?php
			foreach( $sql as $result )
			{
				echo '<tr style="font-size: 15px;">';
				echo '<td>'. $result[0] .'</td>';
				echo '<td>'. $result[1] .'</td>';
				echo '</tr>';
			}
		}else{
			echo '<td style="font-size: 15px;">It seems that you don\'t have any RP points yet!<br />
				If you do, please note points given before this system was implemented are not shown.';
		}
			?>
		</table></div>
	</div>
</div>

<footer>
	<div class="copyright">
		<p>&copy; 2008-<?php echo date("Y"); ?> Fearless Community</p>
	</div>
</footer>
