<?php

define('IN_MYBB', true);
Require('global.php');

global $mybb, $templates;

if (!$mybb->user['uid']) {
	// Redirect unauthenticated users to login page
	header("Location: ../member.php?action=login&referrer=" . urlencode($_SERVER['REQUEST_URI']));
	exit();
}

$plugins->run_hooks("character_start");
eval("\$page= \"".$templates->get('headerinclude')."\";");
echo $page;
/*
eval("\$page= \"".$templates->get('header')."\";");
echo $page;
*/
?>

<head>
<style>
<?php
include('css/style.php');
?>
</style>
<title>Fearless - Character Page</title>
</head>
<body>
		<div id="container2">
		<div id="menu">
		<div class='character_menu_header'>
		Character Menu
		</div>
		<table cellspacing='0' cellpadding='0' border=1 class="character_menu_table_nav">
		<tr>
		<td class="character_menu_table_nav">
			<div class="character_menu_nav_active"></div><a href="character.php?area=advdup"><p class="character_menu_table_text">Manage ADV dupes</p></a>
		</td>
		</tr>
		<tr class="character_menu_table_nav" >
		<td class="character_menu_table_nav">
		<div class="character_menu_nav_active"></div><a href="/sigs"><p class="character_menu_table_text">Personalized Forum Signature</p></a>
		</td>
		</tr>
		<tr  class="character_menu_table_nav" >
		<td class="character_menu_table_nav">
		<div class="character_menu_nav_active"></div><a href="character.php?area=points"><p class="character_menu_table_text">Roleplay Points</p></a>
		</td>
		</tr>
		<tr class="character_menu_table_nav" >
		<td class="character_menu_table_nav">
		<div class="character_menu_nav_active"></div><a href="character.php?area=bannedprops"><p class="character_menu_table_text">Banned Props</p></a>
		</td>
		</tr>

		</table>
		</div>
	</body>