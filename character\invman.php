<?php
if (!defined('IN_MYBB'))
{
	die( 'Hacking attempt' );
}

	?>
<head>
   <!-- Page Info -->
   <title>Inventory Management</title>
   <link type="text/css" rel="stylesheet" href="../48bgt3r_test/css/style.css?<?php echo time(); ?>" /> 
	<script src="https://use.fontawesome.com/1c797fefd5.js"></script>
	<link rel="icon" href="images/logo.png">
   <!-- Various --><script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
  <script src="../48bgt3r_test/javascript/timeago.js" type="text/javascript"></script>
   <script type="text/javascript">
jQuery(document).ready(function() {
  jQuery("abbr.timeago").timeago();
});</script>
<script>

$(document).ready(function(){
	$(".open-bext").click(function(){
    	$(this).parents("tr").next("tr").toggle();
    });
	
})

</script>
</head>
			<link type="text/css" rel="stylesheet" href="../48bgt3r_test/css/style.css?<?php echo time(); ?>" /> 
	
			<div class='changelog_title_box'>
			<p class="changelog_title">
				Inventory Management
			</p>
			<p class="changelog_sub_title">
			Manage your inventory
		
			</p>

						<?php  
			$ip = $_SERVER["HTTP_CF_CONNECTING_IP"];
			$uid = $mybb->user['uid'];

			if($mybb->usergroup['cancp']) 
			{
				$_SESSION["rights"] = true;
			}

			
			if($_SESSION["rights"]) 
			{
			?>
			<div class="changelog_input_box">
<?php
	// Select RP Points using temar's SQL class.
	Require_once('shared/sql.php');
	$temardb->sqlmakefunction();
	connect( "cityrp" );
	$sql = select( "SELECT admin, reason FROM rppoints WHERE target_steam = '".$steamid."'", "cityrp" );

	
	disconnect( "cityrp" );
?>