<?php
	if (!defined('IN_MYBB'))
	{
		die( 'Hacking attempt' );
	}
	$uid = $mybb->user['uid'];

	$query = $db->query("SELECT steamid FROM fearless_steamids WHERE id = ".$uid);
	while($forum = $db->fetch_array($query))
	{
		$steamid = $forum['steamid'];
		Break;
	}

?>
<html>
<link type="text/css" rel="stylesheet" href="arny/marketplace/css/style.css?<?php echo time(); ?>" />
<script src="https://use.fontawesome.com/1c797fefd5.js"></script>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.2/css/font-awesome.min.css">
<link rel="icon" href="images/logo.png">
<!-- Various --><script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
<script src="arny/marketplace/javascript/timeago.js" type="text/javascript"></script>
<script type="text/javascript">
$(document).ready(function(){
	$(".open-bext").click(function(){
    	$(this).parents("tr").next("tr").toggle();
    });

})

</script>
<head>
	<title>Fearless - Job Playtime</title>
</head>
<body>
  <?php
		$player = $db->newQuery("SELECT _PoliceHours, _PresidentHours, _SRUHours, _CorleoneHours, _RebelHours, _DeliveryHours, _ChauffeurHours, _SecurityHours, _ChefHours, _GunDealerHours, _BMDHours, _DoctorHours, _DeskSecretaryHours, _ParamedicHours, _FiremanHours, _MechanicHours FROM #cityrp#.players WHERE _SteamID = '".$steamid."'")->fetch_row();

		$police = floor($player[0] / 3600);
		$pres = floor($player[1] / 3600);
		$sru = floor($player[2] / 3600);
		$corleone = floor($player[3] / 3600);
		$rebel = floor($player[4] / 3600);
		$delivery = floor($player[5] / 3600);
		$chauffeur = floor($player[6] / 3600);
		$security = floor($player[7] / 3600);
		$chef = floor($player[8] / 3600);
		$gundealer = floor($player[9] / 3600);
		$bmd = floor($player[10] / 3600);
		$doctor = floor($player[11] / 3600);
		$desk = floor($player[12] / 3600);
		$paramedic = floor($player[13] / 3600);
		$fireman = floor($player[14] / 3600);
		$mechanic = floor($player[15] / 3600);

		echo '<div id="menu10">';
		echo '<h1>Job Statistics</h1>';
		echo '<h3>Playtime</h3>';
		echo "<strong><span id='tbl_head'>(Vice) President:</span> ".$pres." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>SRU:</span> ".$sru." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Police Officer:</span> ".$police." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Nexus Desk Secretary:</span> ".$desk." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Paramedic:</span> ".$paramedic." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Fireman:</span> ".$fireman." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Chef:</span> ".$chef." hour(s</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Doctor:</span> ".$doctor." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Gun Dealer:</span> ".$gundealer." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Mechanic:</span> ".$mechanic." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Chauffeur:</span> ".$chauffeur." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Delivery Driver:</span> ".$delivery." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Security:</span> ".$security." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Rebel:</span> ".$rebel." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Black Market Dealer:</span> ".$bmd." hour(s)</strong><br>";
		echo "<br /><strong><span id='tbl_head'>Corleone:</span> ".$corleone." hour(s)</strong><br>";
		echo '</div>';

		echo '<div id="menu10">';
		echo '<h1>Other Job Statistics</h1>';
		$govTotal = $police + $pres + $sru + $desk + $paramedic + $fireman;
		echo "<strong><span id='tbl_head'>Total time as Government:</span> ".$govTotal." hour(s)</strong><br>";
		echo '</div>';
  ?>
</body>
</html>
