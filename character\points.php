<?php
if (!defined('IN_MYBB'))
{
	die( 'Hacking attempt' );
}

echo "<title>RP Points Overview</title>
	<style type='text/css'>
	/* For cross-browser compatibility */
	@font-face {
		font-family: 'harabararegular';
		src: url('http://www.fearlessrp.net/character/font/harabara-webfont.eot');
		src: url('http://www.fearlessrp.net/character/font/harabara-webfont.eot?#iefix') format('embedded-opentype'),
			 url('http://www.fearlessrp.net/character/font/harabara-webfont.woff2') format('woff2'),
			 url('http://www.fearlessrp.net/character/font/harabara-webfont.woff') format('woff'),
			 url('http://www.fearlessrp.net/character/font/harabara-webfont.ttf') format('truetype'),
			 url('http://www.fearlessrp.net/character/font/harabara-webfont.svg#harabararegular') format('svg');
		font-weight: normal;
		font-style: normal;
	}
	</style>";
	
	echo '<center>
		<p><a href="character.php">Go back</a></p>
		<table style="font-family: \'harabararegular\';width:900px;border:1px solid;border-color:#CCCCCC;" cellpadding="3">
		<tr style="color:#2ED3DD;font-size:20px;">
			<td><strong>Received from</strong></td>
			<td><strong>Reason</strong></td>
		</tr>';

	// Select RP Points using temar's SQL class.
	Require_once('shared/sql.php');
	$temardb->sqlmakefunction();
	connect( "cityrp" );
	$sql = select( "SELECT admin, reason FROM rppoints WHERE target_steam = '".$steamid."'", "cityrp" );
	if( !empty( $sql ) )
	{
		foreach( $sql as $result )
		{
			echo '<tr style="font-size: 15px;color:#535353;">';
			echo '<td>'. $result[0] .'</td>';
			echo '<td>'. $result[1] .'</td>';
			echo '</tr>';
		}
	}else{
		echo '<td style="font-size: 15px;color: #535353;">It seems that you don\'t have any RP points yet!<br />
			If you do, please note points given before this system was implemented are not shown.';
	}
	echo '</table>';
	
	disconnect( "cityrp" );
?>