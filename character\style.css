body {
    color: white;
	background-color: #101010;
    background: url(../../clan/images/bg_1.jpg) no-repeat center center fixed;
    background-color: #434343;
    font-family: Ta<PERSON>a,Verdana,Arial,Sans-Serif;
    font-size: 13px;
 }
 		#container2{
		background: white;
		height: 400px;
		}
		#menu {´
		 border-color: #30B8D6;
		 border-style: solid;
		 border-width: 2px;
		 background-color: #ffffff;
		}

		.character_menu_header{
				color: white;
	background: #313232;
	border-bottom: 2px solid #00929b;
	font-size: 14px;
	padding: 8px 10px;
	text-align: center;
	font-weight: bold;
	text-transform: uppercase;
	width: 98.8%;
	margin: auto auto;
		}
		.character_menu_table_nav{
			width: 100%;
		}

		td.character_menu_table_nav{
			padding: 0;
			margin: 0;
		}

		.character_menu_nav_active{
			width: 3px;
			height: 40px;
			margin: 0;
			padding: 0;
			background: #00929b;
			float:left;
		}

		p.character_menu_table_text{
			padding: 12px;
			margin: 0;
		}
        #container2{
            background: black;
        height: 400px;
        }

        p{
            color: white;
        }


        #menu {
            width: 65%;
            border-color: #262626;
            border-style: solid;
            border-width: 1px;
            background-color: #333;
            color: white;
            padding: 10px;
            margin: 10px;
            margin-right: 0px;
            font-family: SansProLight;
            font-size: 13pt;
            float: left;
            line-height: 1.5;
        }

        #menu2 {
            width: 39%;
            border-color: #262626;
            border-style: solid;
            border-width: 1px;
            background-color: #333;
            color: white;
            padding: 10px;
            margin: 10px;
            margin-right: 0px;
            font-family: SansProLight;
            font-size: 13pt;
            float: left;
            line-height: 1.5;
        }

        #menu2_content {
            margin: 0;
            padding: 0;
            width: 100%
        }

        #menu3 {
            width: 28%;
            border-color: #262626;
            border-style: solid;
            border-width: 1px;
            background-color: #333;
            color: white;
            padding: 10px;
            margin: 10px;
            margin-left: 0px;
            font-family: SansProLight;
            font-size: 13pt;
            float: right;
            line-height: 1.5;
        }

        #menu4 {
            width: 54%;
            border-color: #262626;
            border-style: solid;
            border-width: 1px;
            background-color: #333;
            color: white;
            padding: 10px;
            margin: 10px;
            margin-left: 0px;
            font-family: SansProLight;
            font-size: 13pt;
            float: right;
            max-height: 440px;
        }

        #scrollpanel {
            overflow-y: scroll;
            max-height: 340px;
        }

        @font-face {
        font-family: SansProLight;
        src: url(../../flood/fonts/SourceSansPro-Light.woff);
        }


        #gametracker{
        display: none;
        }

        #container{
            padding: 0;
            display: block;
            overflow: auto;
            /*min-height: 800px;*/
        }


        #panel_2017{
                padding-left: 0px;
            padding-right: 0px;
        }

        #menuspacer_2017{
               padding-left: 0px;
            padding-right: 0px;
        }

        #usuariomenu{
            padding-left: 5px;
        }

        .changelog_title_box{
            background: #2b2f30;
            text-transform: capitalize;
            margin: 0 auto;
            padding: 17px;
            /*border-bottom: #00808a 20px solid;*/
        }

        p.changelog_title{
            color: white;
            margin: 0;
            font-size: 18pt;
            text-transform: uppercase;
            text-align: center;

                font-family: SansProLight;
        }

        p.changelog_sub_title{
            color: white;
            text-transform: lowercase;
            font-size: 10pt;
            text-align: center;
            margin: 0;
            opacity: 0.8;
            font-family: SansProLight;
        }

        #section_header{
            background: #2b2f30;
            border-bottom: #00808a 4px solid;
            width: 100%;
            height: 40px;
            margin: 0;
        }

        h2 {
            font-family: SansProLight;
            text-transform: uppercase;
            /*color: white;*/
        }

        .highlight_3{
          color:#2ed3dd;
        }

        .wip{
          font-family: SansProLight;
          font-size: 12pt;
          text-align: center;
          background-color: red;
          color: white;
          font-weight: bold;
          margin: 0;
        }

        .top_block {
          height: 30px;
          width: 16.663%;
          background: #00808a;
          color: white;
          border-color: #2b2f30;
          font-size: 14px;
          cursor: pointer;
          text-align: center;
          font-family: SansProLight;
          border-radius: 0px;
        }

        .top_block:hover {
          background: #20bdc5;
          border-color: #20bdc5;
        }

        .rank_badge {
            width: 250px;
            min-width: 75%;
            max-width: 100%;
            margin-top: 5px;
        }

        .profile_img {
            width: 28%;
            float: left;
            margin-right: 15px;
            /*border-right: 3px dashed #00808a;
            border-left: 3px dashed #00808a;*/
        }

        #inv_tbl {
            font-size: 12pt;
        }

        #tbl_head {
            background: #00808a;
            color: white;
            padding-left: 5px;
            padding-right: 5px;
            padding-top: 3px;
            padding-bottom: 3px;
            font-size: 10pt;
            border-radius: 5px 5px 5px 5px;
        }

        #tbl_head_large {
            background: #00808a;
            color: white;
            padding-left: 5px;
            padding-right: 5px;
            padding-top: 3px;
            padding-bottom: 3px;
            font-size: 16pt;
            border-radius: 5px 5px 5px 5px;
        }

        #inv_cap {
            width: 100%;
            margin-top: 10px;
            -webkit-appearance: none;
            border: none;
        }

        #inv_cap::-webkit-progress-bar {
            background: #ffffff;
            border-radius: 5px;
        }

        /* Now the value part */
        #inv_cap::-webkit-progress-value {
            opacity: 0.6;
            background: #00d6e6;
            border-radius: 5px;

        }

        table{
            color: white;
        }

        footer .copyright{
          height: 22px;
          padding-top : 0px;
          padding-bottom: 0px;
          margin-top: 20px;
          color: #ffffff;
          background-color: #00808a;
          text-align: center;
        }

        .character_menu_header{
            color: white;
            background: #313232;
            border-bottom: 2px solid #00808a;
            font-size: 14px;
            padding: 8px 10px;
            text-align: center;
            font-weight: bold;
            text-transform: uppercase;
            width: 100%;
            /*margin: auto auto;*/
        }
        .character_menu_table_nav{
            width: 100%;
        }

        td.character_menu_table_nav{
            padding: 0;
            margin: 0;
        }

        .character_menu_nav_active{
            width: 3px;
            height: 40px;
            margin: 0;
            padding: 0;
            /*background: #00929b;*/
            float:left;
        }

        p.character_menu_table_text{
            padding: 12px;
            margin: 0;
        }
        input {
          display: none;
          visibility: hidden;
        }
        label {
          display: block;
          padding: 0.5em;
        }
        label:hover {
          color: #d9d9d9;
        }
        label::before {
          font-weight: bold;
          font-size: 15px;
          content: "↓";
          padding-left: 10px;
          display: inline-block;
          width: 20px;
          height: 20px;
          background: radial-gradient(circle at center, #00808a 50%, transparent 50%);
        }
        #expand {
          height: 0px;
          overflow: auto;
          transition: height 0.5s;
          color: #00808a;
        }
        section {
          padding: 0 20px;
        }
        #toggle:checked ~ #expand {
          height: 230px;
        }
        #toggle:checked ~ label::before {
          content: "↑";
          padding-left: 10px;
        }

        @media(max-width: 1100px){
          #menu,
          #menu2,
          #menu3,
          #menu4{
            float: none;
            text-align: left;
            width: 97%;
            margin-left: 0;
            margin-right: 0;
          }
          .rank_badge {
            width: 60%;
          }

        }

        @media(max-width: 900px){
          .top_block{
            overflow: visible;
          }

        }
