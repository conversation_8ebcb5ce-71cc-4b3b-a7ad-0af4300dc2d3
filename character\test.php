	<?php
	// This is a test file - access restricted
	die('Direct access not permitted - test file');

	$steamid = "STEAM_0:1:15472195";
			Require_once('../shared/sql.php');
		$temardb -> sqlmakefunction();
		connect('cityrp');
		$player = select("SELECT _Igname, _Description, _Clan, _Money, _Donator, _Inventory, _TimePlayed, _Points, _Plate, _Online, _Server, _svrank, _Lastonline,_Invvalue, _Gender, _SteamID FROM players WHERE _SteamID = '".$steamid."'", 'cityrp');
$player = $player[0];


	include('../shared/items.php');
			echo "<table>";
			$inv = explode("; ", $player[5]);
			foreach($inv as $invitem)
			{
				$invitem = explode(": ", $invitem, 2);
				$invlist[$invitem[0]] += $invitem[1];
			}
			$cat = "";
			foreach($items as $i => $v)
			{
				if($v[0] != $cat)
				{
					$cat = $v[0];
					echo "<tr><td colspan=2>&nbsp;</td></tr>";
					echo "<tr><td><b>".$v[0]."</b></td><td><b>Qt.</b></td></tr>";
				}
				if(!isset($invlist[$i]))
					$invlist[$i] = 0;
				if($invlist[$i] != 0)
				echo '<tr><td>'.$v[1].'</td><td>'.$invlist[$i].'</td>';
			}
			echo "</table>";
			?>