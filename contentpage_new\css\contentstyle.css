#gametracker{
	
display: none;
}

@font-face {
		font-family: SansProLight;
		src: url(../../flood/fonts/SourceSansPro-Light.woff);
		} 
		
		#container{
	padding: 0;
}


#panel_2017{
	    padding-left: 0px;
    padding-right: 0px;
}

#menuspacer_2017{
	   padding-left: 0px;
    padding-right: 0px;
}

#usuariomenu{
	padding-left: 5px;
}

a:hover{
cursor: pointer; }
.changelog_title_box{
	background: #2b2f30;
	text-transform: capitalize;
	margin: 0 auto;
	padding: 17px;
}

p.changelog_title{
	color: white;
	margin: 0;
	font-size: 18pt;
	text-transform: uppercase;
	text-align: center;
		
		font-family: SansProLight;
}

p.changelog_sub_title{
	color: white;
	text-transform: lowercase;
	font-size: 10pt;
	text-align: center;
	margin: 0;
	opacity: 0.8;
  font-family: SansProLight;
}

thead.changelog_main_table{
	background: #00808a;
	color: white;
	font-size: 8pt;
	
}
tbody.changelog_main_table{
	width: 100%;	border:2px solid;
	border-color: #ddd ;
	border-top: none;
	
}

.thead_style{
	padding:5px;
}

img.changelog_table_avatar{
	width: 40px;
	height: 40px;
	padding: 10px;
	border-radius: 15px;
}

.changelog_table_avatar_wrapper{
	position: relative;
}

.changelog_table_avatar_wrapper:hover{
	cursor: pointer;
	
	opacity: 0.8;
}

.changelog_table_overlay{
	width: 40px;
	height: 40px;
	background: black;
	border-radius: 6px;
	position: absolute;
	margin-top: -50px;
	margin-left: 10px;
	opacity: 0.4;
}


.changelog_table_rank {
  position: absolute;
  padding: 0;
  margin: 0;
  color: white;
  width: 40px;
  margin-top: -38px;
  margin-left: 10px;
  text-align: center;
	font-size: 9pt;
}

tr.changelog_table_tr{
	font-size: 10pt;
  color: black;
  height: 50px;
  text-align: left;
  font-family: SansProLight;
  background: #f2f2f2;
}

tr:nth-child(even) {background: #e6e6e6}

.new{
  /*background-color:#c4ffbc;*/
  background-color:#5cd65c;
}

.fix{
  /*background-color:#bcf9ff;*/
  background-color:#4d94ff;
}

.delete{
  /*background-color:#ffc9bc;*/
  background-color:#ff6666;
}

.change{
  /*background-color:#ffdca0;*/
  background-color:#ffd480;
}

.rules{
  /*background-color:#d1ccff;*/
  background-color:#c266ff;
}

p.changelog_extended_text{
	padding-top: 10px;
	padding-bottom: 10px;
}

body{
  background-color: #f4f4f4;
}

.wip{
  text-align: center;
  background-color: red;
  color: white;
  font-weight: bold;
  margin: 0;
}

.container{
  width: 95%;
  margin: auto;
  overflow: hidden;
}

.secondary{
  width:100%;
  margin:auto;
  overflow:hidden;
  border-top: #000000 3px solid;
  text-align:center;
}

ul#services{
  margin:0;
  padding:0;
}


.button_1_long{
  height: 35px;
  width: 100%;
  background: #00808a;
  border:0;
  margin-top: 5px;
  margin-right: 0px;
  font-family: SansProLight;
  color: #ffffff;
}

.button_1{
  height: 25px;
  width: 50%;
  background: #00808a;
  border:0;
  margin-top: 5px;
  margin-right: 0px;
  font-family: SansProLight;
  color: #ffffff;
}

.button_1_thin{
  height: 25px;
  width: 100%;
  background: #00808a;
  border:0;
  margin-top: 5px;
  margin-right: 0px;
  font-family: SansProLight;
  color: #ffffff;
}

.button_1:hover, .button_2:hover, .button_1_long:hover, .button_1_thin:hover{
  background-color: #008f99;
  cursor: pointer;
  font-weight: bold;
}

.button_2{
  height: 38px;
  background: #00808a;
  border:0;
  padding-left: 20px;
  padding-right: 20px;
  color: #ffffff;
}

.button_3{
  height: 38px;
  background: #00808a;
  border:0;
  padding-left: 10px;
  padding-right: 10px;
  color: #ffffff;
}

.dark{
  padding: 15px;
  background: #2b2f30;
  color: #ffffff;
  margin-top: 0px;
  margin-bottom: 10px;
}

.dark2{
  padding-left: 15px;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-right: 15px;
  background: #2b2f30;
  color: #ffffff;
  margin-top: 0px;
  margin-bottom: 10px;
}

.heading{
  font-weight: bold;
  text-transform: uppercase;
}

.heading2{
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 5px;
  margin-top: 10px;
}

.introbar{
  width: 100%;
  margin-bottom: 30px;
}

.introbar h1{
  font-size: 20px;
}

.describe{
  font-style: italic;
  margin-bottom: 20px;
}

.page-title{
  font-family: SansProLight;
  font-weight: bold;
}

.credit_content{
  overflow-y: scroll;
  height: 516px;
  padding: 5px;
  border: 2px solid #00808a;
  background-color: #303536;
}

.map_showcase{
	width: 100%;
  margin-top: 10px;
	margin-bottom: 10px;
}

.changelog_title_box{
  background: #2b2f30;
  text-transform: capitalize;
  margin: 0 auto;
  padding: 17px;
  border-bottom: #00808a 20px solid;
}

p.changelog_title{
  color: white;
  margin: 0;
  font-size: 18pt;
  text-transform: uppercase;
  text-align: center;
    
    font-family: SansProLight;
}

p.changelog_sub_title{
  color: white;
  text-transform: lowercase;
  font-size: 10pt;
  text-align: center;
  margin: 0;
  opacity: 0.8;
}

h3{
  text-transform: uppercase;
  font-family: SansProLight;
  font-size: 18px;
  line-height: 1.2;
  padding: 0;
  margin: 0;
}

.darkh3{
  color: black;
}

p{
	font-family: SansProLight;
  font-size: 15px;
  line-height: 1.2;
}

header{
  background: #2b2f30;
  color: #ffffff;
  padding-top: 30px;
  min-height: 70px;
  border-bottom: #00808a 12px solid;
}

header a{
  color: #ffffff;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 16px;
}

header li{
  float:left;
  display: inline;
  padding:0 20px 0 20px;
}

header #branding{
  float: left;
}

header #branding img{
  width: 500px;
}

header #branding a{
  font-size: 30px;
  font-weight: bold;
  text-transform: capitalize;
}

header #branding h1{
  margin:0;
  transform: inherit;
  transition: all 0.4s;
}

header #branding h1:hover{
  transform: scale(1.02);
  margin-left: 3px;
}

header #branding img{
  width: 300px;
  float: left;
}

header nav{
  float:right;
  margin-top: 10px;
}

header .highlight, header .current a{
  color:#00808a;
  font-weight: bold;
}

header nav a:hover{
  color:#cccccc;
  font-style: italic;
}

#showcase{
  min-height: 375px;
  background: url("../img/showcase.jpg") repeat;
  text-align: center;
  color: #ffffff;
}

#showcase img{
  height: 250px;
  margin-top: 20px;
  transform: inherit;
  transition: all 0.4s;
  width: 250px;
}

#showcase img:hover {
    transform: rotateZ(360deg) scale(1.2);
}

#showcase h1{
  margin-top: 100px;
  font-size: 55px;
  margin-bottom: 10px;
}

#showcase p{
  font-size: 25px;
  font-family: Times;
  font-style: italic;
}

#newsletter{
  padding:15px;
  color:#ffffff;
  background:#000000;
}

#newsletter h1{
  float:left;
}

#newsletter form{
  float:right;
  margin-top: 15px;
  text-align: center;
}

#boxes{
  margin-top: 20px;
  margin-left: 0px;
}

#boxes .box{
  float:left;
  text-align: center;
  width:30%;
  padding: 10px;
}

#boxes .box img{
  width: 90px;
  height: 90px;
  transform: inherit;
  transition: all 0.4s;
}

#boxes .box img:hover{
  transform: scale(1.05);
}

aside#sidebar2{
  float: left;
  width: 37%;
  margin-top: 0px;
  margin-left: 0px;
  margin-right: 18px;
}

aside#sidebar{
  float: left;
  width: 49%;
  margin-top: 0px;
  margin-left: 0px;
  margin-right: 0px;
}

aside#sidebar img{
  filter: grayscale(100%);
  border: 2px solid #00808a;
  width: 99%;
}

aside#sidebar img:hover{
  filter: grayscale(0%);
}

aside#sidebar .quote{
  float: none;
  width: 100%;
  margin-top: 10px;
}

.highlight_1{
  color:#2ed3dd;
  font-weight: bold;
}

.highlight_2{
  color:#00808a;
  font-weight: bold;
}

.highlight_3{
  color:#2ed3dd;
}

aside#sidebar .quote input, aside#sidebar .quote textarea{
  width: 90%;
  padding: 5px;
  margin-bottom: 10px;
}

article#main-col{
  float: right;
  width: 30%;
  margin-left: 1%;
}

article#main-col2{
  float:right;
  width:50%;
}

ul#services li{
  list-style: none;
  padding: 10px;
  border: #cccccc solid 1px;
  margin-bottom: 15px;
  background: #e6e6e6;
}

ul#services li a{
  color: black;
  text-decoration: none;
}

ul#services li p{
  color: black;
}

ul#services li a:hover{
  font-weight: bold;
}

ul#services li img#leftImg{
  width:50%;
  float: none;
  transform: inherit;
  transition: all 0.4s;
}

ul#services li img#rightImg{
  width:50%;
  float: none;
  transform: inherit;
  transition: all 0.4s;
}

ul#services li img#leftImg:hover{
  transform: scale(1.5) translateX(75px);
}

ul#services li img#rightImg:hover{
  transform: scale(1.5) translateX(-75px);
}


footer .copyright{
  height: 22px;
  margin-top: 20px;
  color: #ffffff;
  background-color: #00808a;
  text-align: center;
}

footer .credits{
  height: 40px;
  padding: 10px;
  margin-top: 0;
  color: #ffffff;
  background-color: #000000;
  text-decoration: none;
}

footer .credits p{
  text-align: left;
  float: left;
  margin-top: 5px;
  font-size: 18px;
  transform: inherit;
  transition: all 0.3s;
}

footer .credits p:hover{
  transform: scale(1.1) translateX(5px);
  font-weight: bold;
}

footer .credits img{
  width: 20px;
  float: right;
  margin-top: 10px;
  margin-right: 20px;
  transform: inherit;
  transition: all 0.3s;
  width: 20px;
}

footer .credits img:hover{
  transform: rotateZ(360deg);
}

@media(max-width: 768px){
  header #branding,
  header nav,
  header nav li,
  #newsletter h1,
  #newsletter form,
  #boxes .box,
  article#main-col,
  article#main-col2,
  .introbar,
  aside#sidebar,
  aside#sidebar2{
    float:none;
    text-align: center;
    width: 100%;
  }

  #newsletter form .button_1{
    padding-left: 50px;
    padding-right: 50px;
  }

  #boxes .box{
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
  }

  aside#sidebar{
    margin-left: 0;
    margin-right: 0;
  }

  header{
    padding-bottom: 20px;
  }

  #showcase h1{
    margin-top: 40px;
  }

  #newsletter button, .quote button{
    display: block;
    width: 100%;
  }

  #newsletter form input[type="email"], .quote input, .quote textarea{
    width:100%;
    margin-bottom: 5px;
  }

  ul#services li img#leftImg, ul#services li img#rightImg {
    width:100%;
    float: none;
  }

  ul#services li img#leftImg:hover, ul#services li img#rightImg:hover{
    transform: scale(1.1);
  }
}