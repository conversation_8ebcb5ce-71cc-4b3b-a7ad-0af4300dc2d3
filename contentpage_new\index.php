<?php
$contentStylesheet;
$darktheme = 38;
$currenttheme = $mybb->user['style'];
if ($darktheme == $currenttheme) {
  $contentStylesheet = "contentstyle_dark.css";
} else {
  $contentStylesheet = "contentstyle.css";
}
?>

<head>
  <!-- Page Info -->
  <title>Fearless Content</title>
  <link type="text/css" rel="stylesheet" href="../contentpage_new/css/<?= $contentStylesheet . '?' . time(); ?>.css" />
  <script src="https://use.fontawesome.com/1c797fefd5.js"></script>
  <link rel="icon" href="images/logo.png">
  <meta name="description" content="All the content required for FL CityRP servers">

</head>

<body>
  <div class='changelog_title_box'>
    <p class="changelog_title">
      Content
    <p class="changelog_sub_title">
      A page detailing all the content needed for our servers
    </p>
  </div>

  <section id="main">
    <div class="container">
      <div class="introbar">
        <h1 class="page-title">Welcome to our content page...</h1>
        <p>This page contains all the items needed to play on our servers, with some extra additions which you might find useful.</p>
        <p>We've updated this page to show what you need to play on the Events Server. Our new content pack has been compressed, resulting in a significantly smaller download size with minimal quality loss. We recommend unsubscribing from any of our old content packs, as our new pack replaces all of them.</p>
      </div>

      <aside id="sidebar">
        <div class="dark">
          <h3>Workshop Collection</h3>
          <p>It's even easier than before! You can now get all our content in one place. Subscribe to our Official Workshop Collection here:</p>
          <a href="https://steamcommunity.com/sharedfiles/filedetails/?id=3359319410" target="_blank"><img src="./contentpage_new/images/collection_showcase.png"></a>
        </div>
      </aside>

      <article id="main-col2">
        <ul id="services">
          <li>
            <h3 class="darkh3">Map Content</h3>
            <p class="describe">Content for our current map. This will be required to be able to connect to our servers successfully.</p>
            <p>Current Map: <span class="highlight_1">rp_evocity_v4b1_fl2</span></p>
            <a href="https://steamcommunity.com/sharedfiles/filedetails/?id=3362683323" target="_blank"><button class="button_1">Get Map</button></a>
            <img class="map_showcase" src="./contentpage_new/images/mapshowcase_v4b1.png">
            <p class="heading2">Additional Map Content</p>
            <a href="https://steamcommunity.com/sharedfiles/filedetails/?id=745155460" target="_blank"><button class="button_1">Evocity HL2 Content</button></a>
          </li>
          <li>
            <h3 class="darkh3">Extra Content</h3>
            <p class="describe">Additional content that you might find useful.</p>
            <p class="heading">Evocity Ambient Sound Fix</p>
            <p>This small pack will fix some annoying landscape sound from Half Life 2: Episode 2. To subscribe manually, click the button below. If you still have issues, please post a comment on the Workshop page.</p>
            <a href="https://steamcommunity.com/sharedfiles/filedetails/?id=1416494365" target="_blank"><button class="button_1">Go to Workshop</button></a>
            <br><br>
            <p class="heading">Custom Spawnlist</p>
            <p class="describe">Created by <a href="https://www.fearlessrp.net/member.php?action=profile&uid=18433" target="_blank">RedPanda</a>, <a href="https://www.fearlessrp.net/member.php?action=profile&uid=18968" target="_blank">Joe</a>, <a href="https://www.fearlessrp.net/member.php?action=profile&uid=11883" target="_blank">Divey</a>, <a href="https://www.fearlessrp.net/member.php?action=profile&uid=13571" target="_blank">Lesanka</a> | Last updated: 17/11/2024 | <a href="https://www.fearlessrp.net/showthread.php?tid=95473&pid=888327#pid888327" target="_blank">View Thread</a></p>
            <p>A clean and easy-to-use spawnlist of props, organised into categories.<br><br>
              <a href="dl/spawnlist.zip?dl=0" target="_blank"><button class="button_1">Download custom spawnlist</button></a>
            <p><b>How to install (assuming default gmod installation directory):</b></p>
            <p><b>1.)</b>Backup this folder<br> C:\Program Files\Steam\steamapps\common\GarrysMod\garrysmod\settings\spawnlist.
              <br><b>2.)</b> Delete all the contents of the spawnlist folder.
              <br><b>3.)</b> Replace the contents of the spawnlist folder with the contents of the folder you have downloaded from Dropbox.
              <br><b>4.)</b> Restart Gmod or type spawnmenu_reload in console.
              <br><b>5.)</b> Enjoy the spawnlist!
            </p>
            </p>
          </li>
        </ul>
      </article>
    </div>
  </section>

  <footer>
    <div class="copyright">
      <p>Made by Tomo | Copyright &copy; 2008-<?php echo date("Y"); ?>: Fearless Community</p>
    </div>
  </footer>
</body>
