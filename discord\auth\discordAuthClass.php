<?php

class discordRankSync
{
	public $url = 'https://discordauth.fearlessrp.net/discord-linked';

	public function checkCooldown($uid)
	{
		global $db;
		$sql = "SELECT id, forumuid, time FROM discord_auth WHERE forumuid = '" . $uid . "' ORDER BY id DESC LIMIT 1";
		$result = $db->query($sql);
		if ($result->num_rows > 0) {
			while ($row = $result->fetch_assoc()) {
				if (time() - $row["time"] <= 10) {
					return true;
				}
			}
		}
		return false;
	}

	public function getDiscordID($uid)
	{
		global $db;
		$sql = "SELECT uid FROM discord_auth WHERE forumuid = '" . $uid . "' ORDER BY id DESC LIMIT 1";
		$result = $db->query($sql);
		if ($result->num_rows > 0) {
			while ($row = $result->fetch_assoc()) {
				return $row['uid'];
			}
		}
	}

	public function getSteamID($uid)
	{
		global $db;
		$sql = "SELECT steamid FROM fearless_steamids WHERE id = '" . $uid . "'";
		$result = $db->query($sql);

		if ($result->num_rows > 0) {
			$row = $result->fetch_assoc();
			return $row['steamid'];
		}
		return null; // Return when no result found
	}

	public function convertSteamID($sid)
	{
		// Convert STEAM_X:Y:Z to Steam64
		if (preg_match('/^STEAM_/', $sid)) {
			$parts = explode(':', $sid);
			return bcadd(bcadd(bcmul($parts[2], '2'), '76561197960265728'), $parts[1]);
		}
		// Convert Steam64 to STEAM_X:Y:Z
		elseif (is_numeric($sid)) {
			$sid = bcsub($sid, '76561197960265728');
			return 'STEAM_0:' . bcmod($sid, '2') . ':' . bcdiv($sid, 2);
		}
		// Return when invalid
		return null;
	}

	private function getCityRPStats($forumuid)
	{
		global $db;

		$sid = $this->getSteamID($forumuid);
		$sid64 = $this->convertSteamID($sid);

		if ($sid64 == null) {
			return null;
		}

		// Defaults incase they've never joined
		$defaultStats = array(
			'money' => 0,
			'timeplayed' => 0,
			'points' => 0,
			'rank' => 0,
			'invvalue' => 0,
			'sid' => $sid64
		);

		$cityrpData = $db->newQuery(
			"SELECT _Money, _TimePlayed, _Points, _svrank
			FROM #cityrp#.players
			WHERE _SteamID64 = '" . $sid64 . "'"
		)->fetch_assoc();

		// Return defaults as they've never joined the server
		if ($cityrpData == null) {
			return $defaultStats;
		}

		// Map the database column names to our stats keys
		$stats = array(
			'money' => $cityrpData['_Money'],
			'timeplayed' => $cityrpData['_TimePlayed'],
			'points' => $cityrpData['_Points'],
			'rank' => $cityrpData['_svrank'],
			'sid' => $sid64
		);

		return array_merge($defaultStats, $stats);
	}

	public function getDiscordToken($uid)
	{
		global $db;

		$sql = "SELECT token, refresh, expires FROM discord_auth WHERE forumuid = '" . $uid . "' ORDER BY id DESC LIMIT 1";
		$result = $db->query($sql);

		if ($result->num_rows > 0) {
			$row = $result->fetch_assoc();
			// Token could be empty as we didn't save this before
			if (!empty($row['token']) && !empty($row['refresh'])) {
				// Create new AccessToken object with expires value
				return new \League\OAuth2\Client\Token\AccessToken([
					'access_token' => $row['token'],
					'refresh_token' => $row['refresh'],
					'expires' => $row['expires'] ?? time() + 604800 // Default to 1 week if not set
				]);
			}
		}
		return null;
	}

	/**
	 * @param integer $forumuid
	 * @param integer $discorduid
	 * @param \League\OAuth2\Client\Token\AccessToken $token
	 * @param string $usergroup
	 */
	private function saveDiscordToDB($forumuid, $discorduid, $token, $usergroup)
	{
		global $db;

		$time = time();
		$token_auth = $token->getToken();
		$token_refresh = $token->getRefreshToken();
		$expires = $token->getExpires();

		$sql = "INSERT INTO discord_auth(uid, forumuid, usergroup, time, token, refresh, expires)
				VALUES('" . $discorduid . "', '" . $forumuid . "', '" . $usergroup . "',
					'" . $time . "', '" . $token_auth . "', '" . $token_refresh . "', '" . $expires . "')";
		$result = $db->query($sql);

		return $result ? true : null;
	}

	/**
	 * @param integer $forumuid
	 * @param integer $discordid
	 * @param \League\OAuth2\Client\Token\AccessToken $token
	 * @param string $usergroup
	 * @param array $cityrpStats
	 * @param boolean $auto
	 */
	private function sendToDiscordBot($forumuid, $discordid, $token, $usergroup, $regdate, $cityrpStats, $auto = false)
	{
		global $env;
		$key = $env['DISCORD_AUTH_TOKEN'];

		$ch = curl_init($this->url);
		$data = array(
			'forumuid' => $forumuid,
			'discordid' => $discordid,
			'usergroup' => $usergroup,
			'regdate' => $regdate,
			'cityrpstats' => $cityrpStats,
			'token' => $token->getToken(),
			'auto' => $auto
		);
		$payload = json_encode($data);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json', 'Token: ' . $key));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		$result = curl_exec($ch);
		if ($result === false) {
			echo 'Curl error: ' . curl_error($ch);
		}

		curl_close($ch);
	}

	/**
	 * @param integer $forumuid
	 * @param \League\OAuth2\Client\Token\AccessToken $token
	 * @param \Wohali\OAuth2\Client\Provider\Discord $provider
	 * @param string $usergroup
	 */
	public function processDiscordAuth($forumuid, $token, $provider, $usergroup, $regdate)
	{
		try {
			$discordUser = $provider->getResourceOwner($token);
			$discordId = $discordUser->getId();

			$savedSuccess = $this->saveDiscordToDB($forumuid, $discordId, $token, $usergroup);
			if (!$savedSuccess) {
				return 'Authentication failed, please try again. If this issue persists, please contact Pollux';
			}

			$cityrpStats = $this->getCityRPStats($forumuid);
			$this->sendToDiscordBot($forumuid, $discordId, $token, $usergroup, $regdate, $cityrpStats);

			return 'Successfully verified, you should receive your role within the next minute.';
		} catch (\Exception $e) {
			$this->clearDiscordToken($forumuid);
			throw $e; // Re-throw to be caught by outer try-catch
		}
	}

	public function clearDiscordToken($uid)
	{
		global $db;
		$sql = "UPDATE discord_auth SET token = NULL, refresh = NULL, expires = NULL
				WHERE forumuid = '" . $uid . "'";
		return $db->query($sql);
	}
}
