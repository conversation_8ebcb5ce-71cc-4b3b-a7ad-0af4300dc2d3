<?php

ob_start();

$options = [
	'scope' => ['identify', 'role_connections.write', 'guilds', 'guilds.join']
];

$provider = new \Wohali\OAuth2\Client\Provider\Discord([
	'clientId'     => $env['DISCORD_AUTH_CLIENT'],
	'clientSecret' => $env['DISCORD_AUTH_SECRET'],
	'redirectUri'  => 'https://fearlessrp.net/discord.php',
]);
?>

<head>
	<title>Fearless - Discord Authentication</title>
	<link rel="stylesheet" href="discord/auth/style.css">
	<link rel="icon" href="images/logo.png">
</head>

<body>
	<div class='discord_title_box'>
		<p class="discord_title">Discord Authentication</p>
		<p class="discord_sub_title">Synchronize your Discord account with the forums</p>
	</div>
	<section id="main">
		<div class="container">
			<center>
				<h1>Discord Authentication</h1>
				<?php
				require("discordAuthClass.php");
				$discordAuth = new discordRankSync();
				$forumuid = $mybb->user['uid'];
				$regdate = $mybb->user['regdate'];

				if ($forumuid) {
					$sql = "SELECT uid, usergroup FROM mybb_users WHERE uid = '" . $forumuid . "'";
					$result = $db->query($sql);
					if ($result->num_rows > 0) {
						while ($row = $result->fetch_assoc()) {
							$usergroup = $row["usergroup"];
						}
					}

					if (check_steam_only()) {
						die('<div id="verify-form" style="padding: 10px;">
								<p>It seems that your account is not linked with your steam account.</p>
								<p>Please link your steam account using the button below.</p>
								<a href="../../steam.php?ref_url=' . $mybb->settings['bburl'] . $_SERVER['REQUEST_URI'] . '">
									<img style="vertical-align: middle" width="100px" src="../../images/icons/steambig.png"><h2 style="vertical-align: middle; display: inline-block; margin-left: 5px">Login with Steam</h2>
								</a>
							</div><br><br>');
						return;
					};

					if (!isset($_GET['code'])) {
						if ($discordAuth->checkCooldown($forumuid)) {
							echo 'You must wait 10 seconds before verifying again.';
							return;
						}

						// First check if we've saved a token before that we can refresh
						$existingToken = $discordAuth->getDiscordToken($forumuid);

						if ($existingToken) {
							try {
								// Refresh existing token if expired
								if ($existingToken->hasExpired()) {
									try {
										$newTokens = $provider->getAccessToken('refresh_token', [
											'refresh_token' => $existingToken->getRefreshToken()
										]);

										echo $discordAuth->processDiscordAuth($forumuid, $newTokens, $provider, $usergroup, $regdate);
										return;
									} catch (\Exception $e) {
										// If refresh fails, clear the token and redirect to new auth
										$discordAuth->clearDiscordToken($forumuid);
										header('Location: ' . $provider->getAuthorizationUrl($options));
										exit;
									}
								}

								// Token is still valid
								echo $discordAuth->processDiscordAuth($forumuid, $existingToken, $provider, $usergroup, $regdate);
								return;
							} catch (\Exception $e) {
								// If any error occurs during auth, clear token and restart auth process
								$discordAuth->clearDiscordToken($forumuid);
								header('Location: ' . $provider->getAuthorizationUrl($options));
								exit;
							}
						}

						// No token found, need to auth
						echo 'Please click below to link your discord account<br>';
						echo '<a href="' . $provider->getAuthorizationUrl($options) . '"><img src="discord/auth/discord_login.png" width="120" height="50" title="Discord Login" alt="Discord Login"></a>';

						// Set state for security
						$state = $provider->getState();
						my_setcookie('discord_oauth_state', $state, TIME_NOW + 3600, true, true);

						// Check given state against previously stored one to mitigate CSRF attack
					} elseif (empty($_GET['state']) || ($_GET['state'] !== $mybb->cookies['discord_oauth_state'])) {
						my_unsetcookie('discord_oauth_state');
						exit('Invalid state, authentication failed, please try again.');

						// Token given from Discord
					} elseif (isset($_GET['code'])) {
						my_unsetcookie('discord_oauth_state');

						if ($discordAuth->checkCooldown($forumuid)) {
							echo 'You must wait 10 seconds before verifying again.';
							return;
						}

						$token = $provider->getAccessToken('authorization_code', [
							'code' => $_GET['code']
						]);

						$user = $provider->getResourceOwner($token);

						// Not sure if this is still used, but it's here incase it is
						$sql3 = "UPDATE mybb_users SET discord_token = '" . $user->getId() . "' WHERE uid = '" . $mybb->user['uid'] . "'";
						$result2 = $db->query($sql3);

						// Process auth - this will save token to DB
						echo $discordAuth->processDiscordAuth($forumuid, $token, $provider, $usergroup, $regdate);
					}
				} else {
					echo 'Please log in to the forums before continuing.';
				}
				?>
			</center>
		</div>
	</section>
</body>
<footer>
	<div class="copyright">
		<p>&copy; 2008-<?php echo date("Y"); ?> Fearless Community</p>
	</div>
</footer>

<?php
ob_end_flush();
?>
