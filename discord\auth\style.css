#gametracker{

display: none;
}

@font-face {
	font-family: SansProLight;
	src: url(../../flood/fonts/SourceSansPro-Light.woff);
}

#container{
	padding: 0;
}


#panel_2017{
	padding-left: 0px;
    padding-right: 0px;
}

#menuspacer_2017{
	padding-left: 0px;
    padding-right: 0px;
}

#usuariomenu{
	padding-left: 5px;
}

a:hover{
cursor: pointer; }
.discord_title_box{
	background: #2b2f30;
	text-transform: capitalize;
	margin: 0 auto;
	padding: 17px;
	border-bottom: #00808a 20px solid;
}

p.discord_title{
	color: white;
	margin: 0;
	font-size: 18pt;
	text-transform: uppercase;
	text-align: center;

		font-family: SansProLight;
}

p.discord_sub_title{
	color: white;
	text-transform: lowercase;
	font-size: 10pt;
	text-align: center;
	margin: 0;
	opacity: 0.8;
}

body{
  background-color: #f4f4f4;
}

.wip{
  text-align: center;
  background-color: red;
  color: white;
  font-weight: bold;
  margin: 0;
}

.container{
  width: 98%;
  margin: auto;
  overflow: hidden;
}

.describe{
  font-style: italic;
  text-align: right;
  margin-bottom: 0px;
  margin-right: 5px;
}

footer .copyright{
  height: 22px;
  padding-top : 0px;
  padding-bottom: 0px;
  margin-top: 20px;
  color: #ffffff;
  background-color: #00808a;
  text-align: center;
}

footer .credits{
  height: 40px;
  padding: 10px;
  margin-top: 0;
  color: #ffffff;
  background-color: #000000;
  text-decoration: none;
}

footer .credits p{
  text-align: left;
  float: left;
  margin-top: 5px;
  font-size: 18px;
  transform: inherit;
  transition: all 0.3s;
}

footer .credits p:hover{
  transform: scale(1.1) translateX(5px);
  font-weight: bold;
}

footer .credits img{
  width: 20px;
  float: right;
  margin-top: 10px;
  margin-right: 20px;
  transform: inherit;
  transition: all 0.3s;
  width: 20px;
}

footer .credits img:hover{
  transform: rotateZ(360deg);
}

@media(max-width: 768px){
  header #branding,
  header nav,
  header nav li,
  #newsletter h1,
  #newsletter form,
  #boxes .box,
  article#main-col,
  article#main-col2,
  .introbar,
  aside#sidebar,
  aside#sidebar2{
    float:none;
    text-align: center;
    width: 100%;
  }

  #newsletter form .button_1{
    padding-left: 50px;
    padding-right: 50px;
  }

  #boxes .box{
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
  }

  aside#sidebar{
    margin-left: 0;
    margin-right: 0;
  }

  header{
    padding-bottom: 20px;
  }

  #showcase h1{
    margin-top: 40px;
  }

  #newsletter button, .quote button{
    display: block;
    width: 100%;
  }

  #newsletter form input[type="email"], .quote input, .quote textarea{
    width:100%;
    margin-bottom: 5px;
  }

  ul#services li img#leftImg, ul#services li img#rightImg {
    width:100%;
    float: none;
  }

  ul#services li img#leftImg:hover, ul#services li img#rightImg:hover{
    transform: scale(1.1);
  }
}

a:link.rank_color_owner, a:visited.rank_color_owner {
  color: green;
  font-style: italic;
}

a:link.rank_color_superadmin, a:visited.rank_color_superadmin {
  color: #87009b;
}

a:link.rank_color_admin, a:visited.rank_color_admin {
  color: #CC00CC;
}

a:link.rank_color_developer, a:visited.rank_color_developer {
  color: #cc0000;
}

a:link.rank_color_trialadmin, a:visited.rank_color_trialadmin {
  color: #137b89;
}

a:link.rank_color_contributor, a:visited.rank_color_contributor {
  color: #f26d40;
}

a:link.rank_color_mediateam, a:visited.rank_color_mediateam {
  color: #F26C6F;
}

p.read_more {
  font-family: SansProLight;
}

a:link.read_more_here, a:visited.read_more_here {
  font-family: SansProLight;
  color: #2ed3dd;
}

table {
  width: 100%;
  margin-top: 15px;
  border-collapse: collapse;
  font-family: Helvetica;
  font-size: 14px;
}

th, td{
  padding:10px;
  text-align: left;
}

th {
  font-size: 16px;
}

tr {
	background-color: #f2f2f2;
	border-bottom: 1px solid white
}

tr.tophead {
  border-bottom: 0;
}

/*tr:nth-child(even) {background-color: #f2f2f2;}*/

.rank_head{
	background: #313232;
  color: white;
  font-family: SansProLight;
}

.description_row{
	background: #00929b;
	color: white;
	font-size: 12px;
	font-weight: normal;
	padding-top: 5px;
	padding-bottom: 8px;
  font-family: SansProLight;
}

.column_headers{
	font-size: 14px;
	background: #e6e6e6;
}

.column_headers_pm{
  font-size: 14px;
  width: 35px;
  background: #e6e6e6;
}

.pm_button, .pm_button a {
	background: #00929b;
	color: white;
    display: inline-block;
    padding: 2px 3px;
    margin: 2px;
    border-radius: 0px;
    font-size: 11px;
    float: right;
}

.bottom_button, .bottom_button a {
  background: #00929b;
  color: white;
    display: inline-block;
    padding: 2px 3px;
    margin: 2px;
    border-radius: 0px;
    font-size: 14px;
    font-family: SansProLight;
    float: right;
}

button {
  background: #00929b;
  color: white;
  padding: 10px;
  border: 1px solid #00929b;
}

button:hover {
  background: #2ed3dd;
  border: 1px solid #2ed3dd;
}

/* The Modal (background) */
.modal {
display: none; /* Hidden by default */
position: fixed; /* Stay in place */
z-index: 1; /* Sit on top */
padding-top: 50px; /* Location of the box */
left: 0;
top: 0;
width: 100%; /* Full width */
height: 100%; /* Full height */
overflow: auto; /* Enable scroll if needed */
background-color: rgb(0,0,0); /* Fallback color */
background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
background-color: #fefefe;
color: black;
margin: auto;
padding: 5px;
border: 1px solid #888;
width: 30%;
}

/* The Close Button */
.close {
color: #aaaaaa;
float: right;
font-size: 28px;
font-weight: bold;
}

.close:hover,
.close:focus {
color: #000;
text-decoration: none;
cursor: pointer;
}

p.modal_title{
	color: black;
	margin: 0;
	font-size: 18pt;
	text-transform: uppercase;
	text-align: center;

	font-family: SansProLight;
}

#verify-form {
  border: 1px solid #777777;
  padding: 10px;
  font-size: 9pt;
}