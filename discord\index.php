<meta http-equiv="refresh" content="0; url=https://discord.gg/bx3GYcS"/>

<!--
AUTHOR: Awestruck
https://www.fearlessrp.net/member.php?action=profile&uid=17825
-->

<?php

chdir("../"); // path to MyBB
define("IN_MYBB", 1);
require("./global.php");
chdir("../");

$ip = (isset($_SERVER["HTTP_CF_CONNECTING_IP"])?$_SERVER["HTTP_CF_CONNECTING_IP"]:$_SERVER['REMOTE_ADDR']);
$details = json_decode(file_get_contents("http://ipinfo.io/{$ip}/json"));
$host = $_SERVER['SERVER_NAME'];

$webhook = "https://discordapp.com/api/webhooks/788461370395983893/3-Njd5FxQ2T0edrJ0vwpAeTa5GLhqMjRxODv_zF1hBenwigCRHf13aRquFqS3KC3n2bn";
$avatar = "https://www.fearlessrp.net/discord/avatar.png";

$mybbUser = $mybb->user['username'];
$mybbUserID = $mybb->user['uid'];
$mybbUserProfile = "https://www.fearlessrp.net/member.php?action=profile&uid=".$mybbUserID;

if($mybb->user['uid'])
{
    $hookObject = json_encode([
        "username" => "FearlessRP IP Bot",
        "avatar_url" => $avatar,
        "tts" => false,
        "embeds" => [
            [
                "type" => "rich",
                "timestamp" => (new DateTime())->format('c'),
                "color" => hexdec("4bf442"),
                "footer" => [
                    "text" => "IP Tracker"
                ],
                "author" => [
                    "name" => "FearlessRP IP Tracker",
                    "url" => "https://www.fearlessrp.net"
                ],
                "fields" => [
                    [
                        "name" => "New IP logged!",
                        "value" => "IP: $details->ip \r\n",
                        "inline" => false
                    ],
                    [
                        "name" => "Logged in to Forum as:",
                        "value" => "[".$mybbUser."](".$mybbUserProfile.")",
                        "inline" => false
                    ]
                ]
            ]
        ]
    ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE );
}
else
{
    $hookObject = json_encode([
        "username" => "FearlessRP IP Bot",
        "avatar_url" => $avatar,
        "tts" => false,
        "embeds" => [
            [
                "type" => "rich",
                "timestamp" => (new DateTime())->format('c'),
                "color" => hexdec("4bf442"),
                "footer" => [
                    "text" => "IP Tracker"
                ],
                "author" => [
                    "name" => "FearlessRP IP Tracker",
                    "url" => "https://www.fearlessrp.net"
                ],
                "fields" => [
                    [
                        "name" => "New IP logged!",
                        "value" => "Visitor IP: $details->ip\r\n",
                        "inline" => false
                    ],
                    [
                        "name" =>"Not logged into the Forum",
                        "value" => "--------------",
                        "inline" => false
                    ]
                ]
            ]
        ]
    ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE );
}

$ch = curl_init($webhook);
curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-type: application/json'));
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $hookObject);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
curl_setopt($ch, CURLOPT_HEADER, 0);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

$response = curl_exec($ch);

curl_close($ch);
?>
