.main{position:fixed;width:2560px;height:1440px}.logo{width:620px;filter:drop-shadow(0px 0 10px rgba(0,0,0,0.81)) saturate(150%)}.left-cont{font-size:23px;color:white;text-shadow:0 0 5px rgba(255,255,255,0.5);padding-left:30px;padding-top:20px}.info-text{width:240px;background:0;color:white;font-family:'Roboto',sans-serif;margin-left:85px;margin-top:-25px;font-size:13px;line-height:16px;font-weight:500;text-shadow:0 0 5px black}.info-text-map{width:270px;background:0;color:white;font-family:'Roboto',sans-serif;margin-left:90px;margin-top:-32px;font-size:18px;text-shadow:0 0 5px black}.left-cont-map{font-size:23px;color:white;text-shadow:0 0 5px rgba(255,255,255,0.5);padding-left:30px;padding-top:15px}.info-text-map-sub{width:270px;background:0;color:#d9f6ff;font-family:'<PERSON><PERSON>',sans-serif;margin-left:90px;margin-top:-15px;font-size:12px;font-weight:bold;text-shadow:0 0 5px black}.cityrp{display:block;background:#323232;width:350px;height:auto;margin-left:120px;margin-top:-150px;box-shadow:0 0 25px blaCK;border-left-style:solid;border-width:5px;border-color:#2cc9d3;padding-bottom:10px;border-radius:2px}.map{background:#323232;width:350px;height:55px;margin-left:120px;margin-top:15px;box-shadow:0 0 25px blaCK;border-left-style:solid;border-width:5px;border-color:#2cc9d3;padding-bottom:5px;border-radius:2px}.info{background:#323232;width:350px;height:auto;margin-left:120px;margin-top:15px;box-shadow:0 0 25px blaCK;border-left-style:solid;border-width:5px;border-color:#2cc9d3;padding-bottom:10px;border-radius:2px}.overslide{position:absolute;width:100%;height:100%;background:rgba(0,0,0,0.3);left:0;top:0;z-index:-2}.overslide2{position:absolute;width:100%;height:100%;background:-moz-linear-gradient(left,rgba(44,201,211,0.37) 0,rgba(70,196,218,0) 32%,rgba(125,185,232,0) 100%);background:-webkit-linear-gradient(left,rgba(44,201,211,0.37) 0,rgba(70,196,218,0) 32%,rgba(125,185,232,0) 100%);background:linear-gradient(to right,rgba(44,201,211,0.37) 0,rgba(70,196,218,0) 32%,rgba(125,185,232,0) 100%);filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#5e2cc9d3',endColorstr='#007db9e8',GradientType=1);left:0;top:0;z-index:-2}.footer{position:fixed;color:#e6e6e6;width:100%;text-align:center;bottom:0;padding-bottom:10px;font-family:'Roboto',sans-serif;font-size:10px;text-shadow:0 0 10px black}body{color:white;height:100%;width:100%;overflow:hidden;background-attachment:fixed}