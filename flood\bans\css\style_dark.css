#gametracker{

display: none;
}

@font-face {
		font-family: SansProLight;
		src: url(/flood/fonts/SourceSansPro-Light.woff);
		}

#container{
	padding: 0;

}


#panel_2017{
	    padding-left: 0px;
    padding-right: 0px;
}

#menuspacer_2017{
	   padding-left: 0px;
    padding-right: 0px;
}

#usuariomenu{
	padding-left: 5px;
}

a:hover{
cursor: pointer; }
.changelog_title_box{
	background: #2b2f30;
	text-transform: capitalize;
	margin: 0 auto;
	padding: 17px;
}

p.changelog_title{
	color: white;
	margin: 0;
	font-size: 18pt;
	text-transform: uppercase;
	text-align: center;

		font-family: SansProLight;
}

p.changelog_sub_title{
	color: white;
	text-transform: lowercase;
	font-size: 10pt;
	text-align: center;
	margin: 0;
	opacity: 0.8;
}

thead.changelog_main_table{
	background: #00808a;
	color: white;
	font-size: 8pt;

}
tbody.changelog_main_table{
	width: 100%;	border:2px solid;
	border-color: #ddd ;
	border-top: none;

}

.thead_style{
	padding:5px;
}

img.changelog_table_avatar{
	width: 40px;
	height: 40px;
	padding: 10px;
	border-radius: 15px;
}

img.changelog_table_avatar_small{
	width: 30px;
	height: 30px;
	padding: 7.5px;
	border-radius: 15px;
}

img.steam_icon{
	vertical-align: middle;
}

.changelog_table_avatar_wrapper{
	position: relative;
}

.changelog_table_avatar_wrapper_small{
	position: relative;
	display: inline-block;
	float: left;
}

.changelog_table_avatar_wrapper:hover{
	cursor: pointer;

	opacity: 0.8;
}

.changelog_table_avatar_wrapper_small:hover{
	cursor: pointer;

	opacity: 0.8;
}

.changelog_table_overlay{
	width: 40px;
	height: 40px;
	background: black;
	border-radius: 6px;
	position: absolute;
	margin-top: -50px;
	margin-left: 10px;
	opacity: 0.4;
}

.changelog_table_overlay_small{
	width: 30px;
	height: 30px;
	background: black;
	border-radius: 6px;
	position: absolute;
	margin-top: -37.5px;
	margin-left: 7.5px;
	opacity: 0.4;
}


.changelog_table_rank {
  position: absolute;
  padding: 0;
  margin: 0;
  color: white;
  width: 40px;
  margin-top: -38px;
  margin-left: 10px;
  text-align: center;
	font-size: 9pt;
}

tr.changelog_table_tr{
	font-size: 10pt;
	background: #1f1f1f;
}

tr:nth-child(even) {background: #151515}


.changelog_extended_text{
	/*background: #262626;*/
}

p.changelog_extended_text{
	padding-top: 10px;
	padding-bottom: 10px;
	padding-right: 10px;
	/*background: #262626;*/
}

td.table_cell_style{
	padding: 0px 5px;
}

.contrib_input_box{
	width: 56px;
}


.wip{
  text-align: center;
  background-color: red;
  color: white;
  font-weight: bold;
  margin: 0;
}

table {
	color: white;

}



a:hover {
	color: white;
}

/*.infocell{
	background: #262626;
}

.blank_cell{
	background: #262626;
}*/

form.search_box{
	text-align: end;
}

input.search_bans{
	background: #00808a;
	color: white;
	border: none;
	padding: 3px 5px;
	font-family: SansProLight;
	font-size: 13px;
}

.reset_search{
	background: #00808a;
	color: white;
	border: none;
	padding: 3px 5px;
	width: 28px;
	display: inline-block;
	font-family: SansProLight;
	font-size: 13px;
}

input.search_bans:hover, .reset_search:hover{
	background: #00aab3;
	cursor: pointer;
}

input.search_input{
	border: none;
	padding: 2px 5px;
	font-family: SansProLight;
}

div.pagination {
	display: flex;
	justify-content: center;
}

div.pagination a {
	margin-right: 5px;
}

div.pagination a:last-of-type {
	margin-right: initial;
}

div.pagination a.active {
    background: #00808a;
    color: #fff;
    border-color: #263c30;
    text-decoration: none;
}

div.pagination a:hover {
	background-color: #00aab3;
}

div.pagination a.disabled {
	pointer-events: none;
}

b.steamid {
    width: 160px;
	display: inline-block;
}