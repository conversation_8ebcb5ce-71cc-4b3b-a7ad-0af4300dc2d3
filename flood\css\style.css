#gametracker{
	
display: none;
}

@font-face {
	font-family: SansProLight;
	src: url(../../../flood/fonts/SourceSansPro-Light.woff);
	} 

#container{
	padding: 0;
}


#panel_2017{
	padding-left: 0px;
    padding-right: 0px;
}

#menuspacer_2017{
	   padding-left: 0px;
    padding-right: 0px;
}

#usuariomenu{
	padding-left: 5px;
}

a:hover{
	cursor: pointer; }
	.changelog_title_box{
	background: #2b2f30;
	text-transform: capitalize;
	margin: 0 auto;
	padding: 17px;
}

p.changelog_title{
	color: white;
	margin: 0;
	font-size: 18pt;
	text-transform: uppercase;
	text-align: center;
	font-family: SansProLight;
}

p.proplimit_guideline_link{
	color: blue;
	margin: 0;
	text-align: center;
	font-size: 10pt;
	text-transform: none;
	font-family: SansProLight;
}

p.changelog_sub_title{
	color: white;
	text-transform: lowercase;
	font-size: 10pt;
	text-align: center;
	margin: 0;
	opacity: 0.8;
	font-family: SansProLight;
}

table{
	color: black;
}


thead.changelog_main_table{
	background: #00808a;
	color: white;
	font-size: 8pt;
	
}
tbody.changelog_main_table{
	width: 100%;	border:2px solid;
	border-color: #ddd ;
	border-top: none;
	
}

.thead_style{
	padding:5px;
}

img.changelog_table_avatar{
	width: 40px;
	height: 40px;
	padding: 10px;
	border-radius: 15px;
}

.changelog_table_avatar_wrapper{
	position: relative;
}

.changelog_table_avatar_wrapper:hover{
	cursor: pointer;
	opacity: 0.8;
}

.changelog_table_overlay{
	width: 40px;
	height: 40px;
	background: black;
	border-radius: 6px;
	position: absolute;
	margin-top: -50px;
	margin-left: 10px;
	opacity: 0.4;
}


.changelog_table_rank {
  position: absolute;
  padding: 0;
  margin: 0;
  color: white;
  width: 40px;
  margin-top: -38px;
  margin-left: 10px;
  text-align: center;
	font-size: 9pt;
}

tr.changelog_table_tr td{
	border-top: 1px solid #fff;
}
tr.changelog_table_tr{
	font-size: 10pt;
	height: 45px;
	background: #f2f2f2;

}

tr.changelog_table_tr:hover td{
background:				#e6e6e6;
}

.new{
	background-color:#c4ffbc;
}

.fix{
	background-color:#bcf9ff;
}

.delete{
	background-color:#ffc9bc;
}

p.changelog_extended_text{
	padding-top: 10px;
	padding-bottom: 10px;
}


