
/* Just temporary 
   ---------- */
.loading_overlay{
	display: block;
width: 100%;
height: 300%;
top: 0;
left: 0;
 position: absolute;
background: #f3f3f3;
z-index: 10000;	
}

.loading_overlay_content{
	position: fixed;
	margin-left: 42%;
	margin-top: 22%;
	font-size: 15pt;
	
}
/* ---------- */

	@font-face {
		font-family: SansProLight;
		src: url(../../fonts/SourceSansPro-Light.woff);
		} 
		
		
		#container{
			font-family: SansProLight;
			margin-bottom: 70px;
			margin-top: 70px;
			
		}
		
		#header{
			display: none;
		}

.server_info{
	display: none;
}

#container{
	padding: 0;
}

#menuspacer_2017{
display: none;
}

#panel_2017{
	    padding-left: 0px;
    padding-right: 0px;
	display: none;
}

#usuariomenu{
	padding-left: 5px;
}

.hub_main_table{
	width: 100%;
	text-align: center;
}

.hub_main_wrapper{
	position: relative;
}


.hub_snd_table{
	width: 100%;
	 position: absolute;
  top: 45%;
  z-index: 100;
  color: white;
  text-align: center;
}

p.hub_snd_title{
	font-size: 1.5vw;
	margin: 0;
	margin-bottom: 10px;
}

p.hub_snd_icon{
	font-size: 1.5vw;
	margin: 0;
	margin-bottom: 10px;
}

span.hub_snd_undertitle{
	background: #00808a;
	color: white;
	padding: 3px 10px 3px 10px;
	font-size: 9pt;
}

.hub_snd_content{
	margin-bottom: 5px;
}

td.hub_main_td{
	
	overflow: hidden;
	width: 25%;
}
td.hub_main_td:last-child{
	
	border-right: none;
}
td.hub_main_td:hover{
	cursor: pointer;
}

.menu_pic{
	width: 100%;
	 transition: all .2s ease-in-out;
	z-index: -100;
}

.menu_pic:hover{
	transform: scale(1.1);
	z-index: -100;
}

p.hub_menu_title{
	position: absolute;
	
}