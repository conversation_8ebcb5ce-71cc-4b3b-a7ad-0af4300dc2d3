<?php
$proplimitStylesheet;
$darktheme = 38;
$currenttheme = $mybb->user['style'];
if ($darktheme == $currenttheme) {
    $proplimitStylesheet = "style_dark.css";
} else {
    $proplimitStylesheet = "style.css";
}
?>

<head>
   <!-- Page Info -->
   <title>Fearless Proplimit Increase</title>
   <link type="text/css" rel="stylesheet" href="flood/proplimit/css/<?= $proplimitStylesheet . '?' . time(); ?>.css" />
	<script src="https://use.fontawesome.com/1c797fefd5.js"></script>
	<link rel="icon" href="images/logo.png">
   <!-- Various --><script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>
   <script src="changelog/javascript/timeago.js" type="text/javascript"></script>
   <script type="text/javascript">
jQuery(document).ready(function() {
  jQuery("abbr.timeago").timeago();
});</script>
<script>

$(document).ready(function(){
	$(".open-bext").click(function(){
    	$(this).parents("tr").next("tr").toggle();
    });
	
})

</script>
</head>
<body>
			
	
			<div class='changelog_title_box'>
				<p class="changelog_title">
					Proplimit Increases
				</p>
				<p class="changelog_sub_title">
					A list of proplimit increases by our staff team
				</p>
				<p class="proplimit_guideline_link">
					<a href="https://www.fearlessrp.net/showthread.php?tid=84347" style="color:#D4D5D5;">Click to see the Guidelines</a>
				</p>
					
			</div>
				<table class="changelog_main_table" style="width: 100%"  cellspacing='0' cellpadding='0'>
					<thead class="changelog_main_table">
						<td style="width: 1%">
						</td>
						<td style="width: 1%">
						<p class="thead_style">#</p>
						</td>
						<td style="width: 5%">
						<p class="thead_style">SteamID</p>
						</td>
						<td  style="width: 6%">
						<p class="thead_style">User</p>
						</td>
						<td  style="width: 24%">
						<p class="thead_style">Reason</p>
						</td>
						<td  style="width: 3%; text-align: center;">
						<p class="thead_style">Amount</p>
						</td>
						<td  style="width: 6%; text-align: center;">
						<p class="thead_style">Type</p>
						</td>	
						<td  style="width: 5%">
						<p class="thead_style" style="text-align: center">Admin</p>
						</td>
						<td  style="width: 5%">
						<p class="thead_style" style="text-align: right">Date</p>
						</td>
						<td style="width: 1%">
						</td>
					</thead>
					
					<tbody class="changelog_main_table">
					
						<?php include('flood/proplimit/php/get_data.php'); ?>
					
						
					</tbody>
				</table>
				<?php  if(isset($error)){ echo $error; } ?>

</body>