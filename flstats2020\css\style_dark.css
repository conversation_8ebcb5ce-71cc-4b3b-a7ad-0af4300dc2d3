

	@font-face {
	  font-family: SansProLight;
	  src: url(../fonts/SourceSansPro-Light.woff);
	}

	@font-face {
	  font-family: GravityLight;
	  src: url(../fonts/Gravity-Light.woff);
	}

	body {
	  min-width: 1554px;
	  overflow-x: scroll;
		/*font-family: SansProLight;*/
	}

	#container {
	  padding: 0;
	  width: 100%;
	  /* height: 1090px;
	  margin-bottom: 100px; */
	  margin-top: 0;
	  font-family: SansProLight;
	}

	#content {
	  padding: 0;
	}

	#panel_2017 {
	  min-width: 1380px;
	  width: 72.5%;
	}

	#menuspacer_2017 {
	  padding: 0;
	}

	#gametracker {
	  display: none;
	}

	div.red_alert {
	  display: none;
	}

	a.main_menu_link:hover {
	  text-decoration: none;
	}

	.footer_stats {
	  background: #00808a;
	  width: 99.3%;
	  padding: 5px;
	  font-size: 9pt;
	  color: white;
	  margin-top: 60px;
	  text-align: center;
	}

	.stats_header {
	  background-image: url(../images/header_bigger.jpg);
	  height: 258px;
	  width: 100%;
	  position: relative;
	  border: 4px solid #00808a;
	  border-left: none;
	  border-right: none;
	  /*margin-top: -20px;*/
	}

	img.stats_header_logo {
	  position: absolute;
	  top: 40%;
	  left: 50%;
	  transform: translate(-50%, -50%);
	}

	span.stats_header_subtext {
	  background: #00808a;
	  color: white;
	  padding: 3px 10px 3px 10px;
	  font-size: 9pt;
	  position: absolute;
	  top: 78%;
	  left: 50%;
	  transform: translate(-50%, -50%);
	}

	.content_stat {
	  width: 90%;
	  margin: 0 auto;
	}

	.top_image_stats {
	  margin: auto;
	  margin-top: 40px;
	  margin-bottom: 40px;
	}

	table.top_image_stats_table {
	  margin: auto;
	}

	table.top_image_stats_table td{
	  padding: 0px 20px;
	}

	.top_image_stats_box {
	  width: 185px;
	  height: 185px;
	  border: 4px solid #00808a;
	}

	p.top_image_stats_box_statistic {
	  color: white;
	  font-size: 18pt;
	  font-weight: light;
	  text-align: center;
	  margin: 0;
	  margin-top: 55px;
	}

	p.top_image_stats_box_subtext {
	  color: white;
	  font-size: 10pt;
	  font-weight: light;
	  text-align: center;
	  margin: 0;
	}

	hr.top_image_stats_box_divider {
	  border: none;
	  background: white;
	  height: 1px;
	  width: 50px;
	}

	#box_bg_1 {
	  background-image: url(../images/image_stat_bg_5.jpg);
	}

	#box_bg_2 {
	  background-image: url(../images/image_stat_bg_2.jpg);
	}

	#box_bg_3 {
	  background-image: url(../images/image_stat_bg_4_3.jpg);
	  background-size: contain;
	}

	#box_bg_4 {
	  background-image: url(../images/image_stat_bg_3.jpg);
	}

	#box_bg_5 {
	  background-image: url(../images/image_stat_bg_1_1.jpg);
	}

	.main_content {
	  margin: 0 auto;
	  width: 95.5%;
	}

	.main_menu_header {
	  padding: 20px;
	  text-align: center;
	  text-transform: uppercase;
	  border-bottom: 3px solid #00808a;
	  background: #2b2f30;
	  text-align: center;
	  color: white;
	  font-size: 14pt;
	}

	p.main_menu_cell_text {
	  padding: 15px;
	  color: white;
	  background: #2b2f30;
	  text-transform: capitalize;
	  border-bottom: 1px solid #1e201f;
	  -o-transition: .3s;
	  -ms-transition: .3s;
	  -moz-transition: 3s;
	  -webkit-transition: .3s;
	  transition: .3s;
	  margin: 0px;
	}

	p.main_menu_cell_text:hover {
	  background: #181818;
	  border-bottom: 1px solid black;
	  cursor: pointer;
	}

	#active_nav {
	  background: #181818;
	  border-bottom: 1px solid black;
	  border-top: 1px solid black;
	}

	.main_menu_cell_active {
	  height: 49px;
	  width: 3px;
	  background: #00808a;
	  margin: 0;
	  float: left;
	  margin: 0;
	  padding: 0;
	}

	.stat_display {
	  width: 97%;
	  margin: 0 auto;
	}

	.stat_box_title {
	  background: #2b2f30;
	  text-transform: capitalize;
	  border-bottom: 3px solid #00808a;
	  margin: 0 auto;
	}

	p.stat_box_title_main {
	  color: white;
	  padding: 20px;
	  margin: 0;
	  font-size: 14pt;
	  text-transform: uppercase;
	  display: inline-block;
	}

	.groupButton {
	  color: white;
	  margin: 0;
	  background: #2b2f30;
	  color: #00808a;
	  border: 2px solid #00808a;
	  padding: 10px;
	  float: right;
	  margin-right: 20px;
	  -o-transition: .3s;
	  -ms-transition: .3s;
	  -moz-transition: 3s;
	  -webkit-transition: .3s;
	  transition: .3s;
	}

	.groupButton:hover {
	  cursor: pointer;
	  background: #181a1b;
	  color: #00a8b5;
	  border-color: #00a8b5;
	}

	tr.stat_table_tr {
	  font-size: 11pt;
	  font-family: helvetica;
	}

	tr.stat_table_tr a {
	  text-shadow: 1px 1px 2px #000;
	}

	tr.stat_table_tr:nth-child(even) {
	  background-color: #0d0d0d;
	  /*#404040*/
	}

	img.stat_table_avatar {
	  width: 40px;
	  height: 40px;
	  padding: 10px;
	  border-radius: 15px;
	}

	.stat_table_avatar_wrapper {
	  position: relative;
	}

	.stat_table_overlay {
	  width: 40px;
	  height: 40px;
	  background: black;
	  border-radius: 6px;
	  position: absolute;
	  margin-top: -50px;
	  margin-left: 10px;
	  opacity: 0.7;
	}

	.scrollable {
	  max-height: 510px;
	  height: 510px;
	  width: 99.5%;
	  overflow-y: auto;
	  border: 2px solid;
	  border-color: #333;
	  border-top: none;
	}

		/* width */
	.scrollable::-webkit-scrollbar {
	  width: 12px;
	}

	/* Track */
	.scrollable::-webkit-scrollbar-track {
	  background: #1a1a1a;
	}

	/* Handle */
	.scrollable::-webkit-scrollbar-thumb {
	  background: #003a3d;
	}

	/* Handle on hover */
	.scrollable::-webkit-scrollbar-thumb:hover {
	  background: #00929b;
	}

	.stat_table_rank {
	  position: absolute;
	  padding: 0;
	  margin: 0;
	  color: white;
	  width: 40px;
	  margin-top: -38px;
	  margin-left: 10px;
	  text-align: center;
	}

	.admin_span {
	  color: #CC00CC;
	  font-weight: bold;
	}

	.developer_span {
	  color: #cc0000;
	  font-weight: bold;
	}

	.supervisora_span {
	  color: #87009b;
	  font-weight: bold;
	}

	.owner_span {
	  color: green;
	  font-style: italic;
	  font-weight: bold;
	}

	.mod_span {
	  color: #1a8cff;
	  font-weight: bold;
	}

	.moderator_span {
	  color: #137b89;
	  font-weight: bold;
	}

	.trialadmin_span {
	  color: #137b89;
	  font-weight: bold;
	}

	.eventmanager_span{
		color: #6ab4fc;
		font-weight: bold;
	}

	.eventcoordinator_span{
		color: blue;
		font-weight: bold;
	}

	.contributor_span {
	  color: #f26d40;
	  font-weight: bold;
	}

	.mediateam_span {
	  color: #F26C6F;
	  font-weight: bold;
	}

	.teacher_span {
	  color: blue;
	  font-weight: bold;
	}

	.veteran_admin_span {
	  color: #ddc100;
	  font-weight: bold;
	}

	.veteran_sa_span {
	  color: #99ccff;
	  font-weight: bold;
	}

	.veteran_dev_span {
	  color: #b87333;
	  font-weight: bold;
	}

	.founder_span {
	  color: #808000;
	  font-style: italic;
	  font-weight: bold;
	}

	.supporter_span{
		color: #2C55FF;
		font-weight: bold;
	}

	.supporterplus_span{
		color: #ff0081;
		font-weight: bold;
	}

	.banned_span {
	  text-decoration: line-through;
	}

	.loading_overlay {
	  display: block;
	  width: 100%;
	  height: 300%;
	  top: 0;
	  left: 0;
	  position: absolute;
	  background: #f3f3f3;
	  z-index: 10000;
	}

	.loading_overlay_content {
	  position: fixed;
	  margin-left: 42%;
	  margin-top: 22%;
	  font-size: 15pt;
	}
