<?php
$curIndex = $_GET['stat'];
if ($curIndex == "mostPosts" ||
    $curIndex == "mostThreads" ||
    $curIndex == "mostViews" ||
    $curIndex == "mostLikes" ||
    $curIndex == "mostReputation" ||
    $curIndex == "mostActiveForum") {

$curPage = 0;
$content = array();
$curPlayerNum = 1;
$activeTime = strtotime('-2 month', time());

//print("<pre>".print_r($stats["forumStats"][$curIndex],true)."</pre>");
foreach($stats["forumStats"][$curIndex] AS $player) {
  $curPlayerInfo = array();
  $curPlayerInfo['name'] = $player['username'];
  $curPlayerInfo['ID'] = $curPlayerNum;
  $curPlayerInfo['avatar'] = $player['avatar'];
  $curPlayerInfo['userGroup'] = $player['usergroup'];
  $curPlayerInfo['userID'] = $player['uid'];
  $curPlayerInfo['active'] = ($player['lastactive'] > $activeTime) ? 1 : 0;

  if ($curIndex == "mostPosts") {
    $curPlayerInfo['coloumn2Title'] = "Last Post";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastpost']);
    $curPlayerInfo['coloumn3'] = $player['postnum']. " Posts";
  } else if ($curIndex == "mostThreads") {
    $curPlayerInfo['coloumn2Title'] = "Last Post";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastpost']);
    $curPlayerInfo['coloumn3'] = $player['threadnum']. " Threads";
  } else if ($curIndex == "mostViews") {
    $curPlayerInfo['coloumn2Title'] = "Last Active";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastactive']);;
    $curPlayerInfo['coloumn3'] = number_format($player['amount']) . " Views";
  } else if ($curIndex == "mostLikes") {
    $curPlayerInfo['coloumn2Title'] = "Last Post";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastpost']);
    $curPlayerInfo['coloumn3'] = number_format($player['tyl_unumrcvtyls']) . " Likes";
  } else if ($curIndex == "mostReputation") {
    $curPlayerInfo['coloumn2Title'] = "Last Post";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastpost']);
    $curPlayerInfo['coloumn3'] = $player['reputation'] . " Reputation";
  } else if ($curIndex == "mostActiveForum") {
    $curPlayerInfo['coloumn2Title'] = "Last Active";
    $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['lastactive']);
    $curPlayerInfo['coloumn3'] = secondsToTime($player["timeonline"]);
  }
  array_push($content, $curPlayerInfo);
}



$rank_colors = array(
  4 =>"owner_span", // Owner
  27 => "supervisora_span", // SA
  6 => "admin_span", // Admin
  68 => "eventmanager_span", // Event Manager
  53 => "trialadmin_span", // TA Stage 1
  18 => "trialadmin_span", // TA Stage 2
  17 => "developer_span", // Developer
  25 => "contributor_span", // Contributor
  64 => "founder_span", // Founder
  46 => "veteran_sa_span", // Veteran SA
  22 => "veteran_admin_span", // Veteran Admin
  45 => "veteran_dev_span", // Veteran Dev
  63 => "eventcoordinator_span", // Event Coordinator
  69 => "supporter_span", // Supporter
  70 => "supporterplus_span", //Supporter Plus
  7 => "banned_span" // Banned
);


//print("<pre>".print_r($content,true)."</pre>");

?>

<?php for ($i = 0; $i < $forumStatLimit; $i++) {
  if ($_GET['filter'] == "active" && $content[$i]['active'] == 0) continue;
?>
  <tr class="stat_table_tr">
    <td style="width: 40px">
      <div class="stat_table_avatar_wrapper">
        <img class="stat_table_avatar" src="<?php echo $content[$i]['avatar'] ?>">
        <div class="stat_table_overlay"></div>
        <div class="stat_table_rank"><?php echo $i + 1 ?></div>
      </div>
    </td>
    <td>
      <a target="_blank" <?php echo !empty($content[$i]['userID']) ? 'href="https://www.fearlessrp.net/member.php?action=profile&uid=' . $content[$i]['userID'] . '"' : 'href=""' ?>>
        <span class="<?php echo $rank_colors[$content[$i]['userGroup']] ?>">
          <?php echo !empty($content[$i]['name']) ? $content[$i]['name'] : $content[$i]['steamName']?>
        </span>
      </a>
    </td>
    <?php if (!empty($content[$i]['coloumn2Title'])) { ?>
    <td style="text-align: right center">
      <table>
        <tr>
          <td>
            <span style="font-size: 8pt"><?php echo $content[$i]['coloumn2Title'] ?></span>
          </td>
        </tr>
        <tr>
          <td>
            <span style="font-size: 10pt"><?php echo $content[$i]["coloumn2"] ?></span>
          </td>
        </tr>
      </table>
    </td>
    <?php } else { ?>
    <td style="text-align:right; padding-right: 15px;">
      <?php echo $content[$i]["coloumn2"] ?>
    </td>
    <?php } ?>

    <?php if (!empty($content[$i]['coloumn3Title'])) { ?>
    <td style="text-align:right; padding-right: 15px;">
      <table style ="float: right">
        <tr>
          <td>
            <span style="font-size: 8pt"><?php echo $content[$i]['coloumn3Title'] ?></span>
          </td>
        </tr>
        <tr>
          <td>
            <span style="font-size: 10pt"><?php echo $content[$i]["coloumn3"] ?></span>
          </td>
        </tr>
      </table>
    </td>
    <?php } else { ?>
    <td style="text-align:right; padding-right: 15px;">
      <?php echo $content[$i]["coloumn3"] ?>
    </td>
    <?php } ?>
  </tr>
<?php }
}
?>
