<?php
$curIndex = $_GET['stat'];
if (empty($curIndex)) $curIndex = "mostHours";
if ($curIndex == "mostHours" ||
    $curIndex == "mostPoints" ||
    $curIndex == "mostMoney" ||
    $curIndex == "highestInvValue" ||
    $curIndex == "highestNetworth" ||
    $curIndex == "mostActive" ||
	$curIndex == "mostPlants" ||
	$curIndex == "mostRocks" ||
	$curIndex == "mostFish" ||
	$curIndex == "mostWeed" ||
	$curIndex == "mostContra") {

    $curPage = 0;
    $content = array();

    $curPlayerNum = 1;
    $activeTime = strtotime('-2 month', time());

    foreach($stats["gameStats"][$curIndex] AS $player) {
      $curPlayerInfo = array();
      $curPlayerInfo["name"] = $player['Forum']['username'];
      $curPlayerInfo["steamName"] = $player['_Name'];
      $curPlayerInfo['ID'] = $curPlayerNum;
      $curPlayerInfo['avatar'] = $player['Forum']['avatar'];
      $curPlayerInfo['userGroup'] = $player['Forum']['usergroup'];
      $curPlayerInfo['userID'] = $player['Forum']['id'];
      $curPlayerInfo['active'] = ($player['_Lastonline'] > $activeTime) ? 1 : 0;

      if (empty($curPlayerInfo['avatar'])) $curPlayerInfo['avatar'] = "images/default_avatar.png";

      if ($curIndex == "mostHours") {
        $curPlayerInfo['coloumn2Title'] = "Last Active";
        $curPlayerInfo['coloumn2'] = gmdate("d-m-Y", $player['_Lastonline']);
        $curPlayerInfo['coloumn3'] = $player['hours']. " Hours";
      } else if ($curIndex == "mostPoints") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = $player['_Points']. " Points";
      } else if ($curIndex == "mostMoney") {
        $curPlayerInfo['coloumn2Title'] = "SteamID";
        $curPlayerInfo['coloumn2'] = $player['_SteamID'];
        $curPlayerInfo['coloumn3'] = "$" . number_format($player['_Money']);
      } else if ($curIndex == "highestInvValue") {
        $curPlayerInfo['coloumn2Title'] = "SteamID";
        $curPlayerInfo['coloumn2'] = $player['_SteamID'];
        $curPlayerInfo['coloumn3'] = "$" . number_format($player['_Invvalue']);
      } else if ($curIndex == "highestNetworth") {
        $curPlayerInfo['coloumn2Title'] = "Breakdown (Inventory + Wallet)";
        $curPlayerInfo['coloumn2'] = "$" . number_format($player['_Invvalue']) . " + $" . number_format($player['_Money']);
        $curPlayerInfo['coloumn3'] = "$" . number_format($player['networthadded']);
      } else if ($curIndex == "mostActive") {
        $curPlayerInfo['coloumn2Title'] = "Breakdown";
        $curPlayerInfo['coloumn2'] = "<b>Main:</b> " . intval($player['timeMain'] / 60 / 60 - $player['timeAFK'] / 60 / 60)  . " hours <br>" . "<b>Build:</b> " . intval($player['timeBuild'] / 60 / 60) . " hours <br>" . "<b>AFK:</b> " . intval($player['timeAFK'] / 60 / 60) . " hours <br> " ;
        $curPlayerInfo['coloumn3'] = intval($player['timeTotal'] / 60 / 60) . " Hours";
	  } else if ($curIndex == "mostPlants") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = $player['_PlantsHarvested']. " Plants";
	  } else if ($curIndex == "mostRocks") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = $player['_RocksMined']. " Rocks";
	  } else if ($curIndex == "mostFish") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = $player['_FishCaught']. " Fish";
	} else if ($curIndex == "mostWeed") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = $player['_WeedHarvested']. " Bags";
	} else if ($curIndex == "mostContra") {
        $curPlayerInfo['coloumn2Title'] = "RP Name";
        $curPlayerInfo['coloumn2'] = "\"".$player['_Igname']."\"";
        $curPlayerInfo['coloumn3'] = "$" . number_format($player['_MoneyFromContra']);
      }
      array_push($content, $curPlayerInfo);
    }



    $rank_colors = array(
      4 =>"owner_span", // Owner
      27 => "supervisora_span", // SA
      6 => "admin_span", // Admin
      68 => "eventmanager_span", // Event Manager
      53 => "trialadmin_span", // TA Stage 1
      18 => "trialadmin_span", // TA Stage 2
      17 => "developer_span", // Developer
      25 => "contributor_span", // Contributor
      64 => "founder_span", // Founder
      46 => "veteran_sa_span", // Veteran SA
      22 => "veteran_admin_span", // Veteran Admin
      45 => "veteran_dev_span", // Veteran Dev
      63 => "eventcoordinator_span", // Event Coordinator
      69 => "supporter_span", // Supporter
      70 => "supporterplus_span", //Supporter Plus
      7 => "banned_span" // Banned
    );


    //print("<pre>".print_r($content,true)."</pre>");

    ?>

    <?php for ($i = 0; $i < $gameStatLimit; $i++) {
      if ($_GET['filter'] == "active" && $content[$i]['active'] == 0) continue;
    ?>
      <tr class="stat_table_tr">
        <td style="width: 40px">
          <div class="stat_table_avatar_wrapper">
            <img class="stat_table_avatar" src="<?php echo $content[$i]['avatar'] ?>">
            <div class="stat_table_overlay"></div>
            <div class="stat_table_rank"><?php echo $i + 1 ?></div>
          </div>
        </td>
        <td>
          <a target="_blank" <?php echo !empty($content[$i]['userID']) ? 'href="https://www.fearlessrp.net/member.php?action=profile&uid=' . $content[$i]['userID'] . '"' : 'href=""' ?>>
            <span class="<?php echo $rank_colors[$content[$i]['userGroup']] ?>">
              <?php echo !empty($content[$i]['name']) ? $content[$i]['name'] : $content[$i]['steamName']?>
            </span>
          </a>
        </td>
        <?php if (!empty($content[$i]['coloumn2Title'])) { ?>
        <td style="text-align: right center">
          <table>
            <tr>
              <td>
                <span style="font-size: 8pt"><?php echo $content[$i]['coloumn2Title'] ?></span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="font-size: 10pt"><?php echo $content[$i]["coloumn2"] ?></span>
              </td>
            </tr>
          </table>
        </td>
        <?php } else { ?>
        <td style="text-align:right; padding-right: 15px;">
          <?php echo $content[$i]["coloumn2"] ?>
        </td>
        <?php } ?>

        <?php if (!empty($content[$i]['coloumn3Title'])) { ?>
        <td style="text-align:right; padding-right: 15px;">
          <table style ="float: right">
            <tr>
              <td>
                <span style="font-size: 8pt"><?php echo $content[$i]['coloumn3Title'] ?></span>
              </td>
            </tr>
            <tr>
              <td>
                <span style="font-size: 10pt"><?php echo $content[$i]["coloumn3"] ?></span>
              </td>
            </tr>
          </table>
        </td>
        <?php } else { ?>
        <td style="text-align:right; padding-right: 15px;">
          <?php echo $content[$i]["coloumn3"] ?>
        </td>
        <?php } ?>
      </tr>
    <?php }
}
?>
