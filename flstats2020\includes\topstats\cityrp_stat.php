<?php

function secondsToTime($inputSeconds) {
    $secondsInAMinute = 60;
    $secondsInAnHour = 60 * $secondsInAMinute;
    $secondsInADay = 24 * $secondsInAnHour;
    $secondsInAWeek = 7 * $secondsInADay;
    $secondsInAMonth = 31 * $secondsInADay;

    // Extract months
    $months = floor($inputSeconds / $secondsInAMonth);

    // Extract weeks
    $weeksSeconds = $inputSeconds % $secondsInAMonth;
    $weeks = floor($weeksSeconds / $secondsInAWeek);

    // Extract days
    $daysSeconds = $weeksSeconds % $secondsInAWeek;
    $days = floor($daysSeconds / $secondsInADay);

    // Extract hours
    $hourSeconds = $daysSeconds % $secondsInADay;
    $hours = floor($hourSeconds / $secondsInAnHour);

    // Extract minutes
    $minuteSeconds = $hourSeconds % $secondsInAnHour;
    $minutes = floor($minuteSeconds / $secondsInAMinute);

    // Extract the remaining seconds
    $remainingSeconds = $minuteSeconds % $secondsInAMinute;
    $seconds = ceil($remainingSeconds);

    // Format and return
    $timeParts = [];
    $sections = [
        'month' => (int)$months,
        'week' => (int)$weeks,
        'day' => (int)$days,
        'hour' => (int)$hours
    ];

    foreach ($sections as $name => $value){
        if ($value > 0){
            $timeParts[] = $value. ' '.$name.($value == 1 ? '' : 's');
        }
    }

    return implode(', ', $timeParts);
}

$cachefile = "flstats2020/includes/cache/cache.json";

$cacheModified = 0;
$cacheLastUpdated = 0;

$stats;

if (file_exists($cachefile)) {
	$cacheModified = filemtime($cachefile);
	$cacheLastUpdated = time() - $cacheModified;
}
//echo $cacheLastUpdated;
if ($cacheLastUpdated <= 1800 && filesize($cachefile)) {
	$cacheContent = file_get_contents($cachefile);
	$stats = json_decode($cacheContent);
	$stats = json_decode(json_encode($stats), true);
} else {
  echo '<div class="loading_overlay">
  <div class="loading_overlay_content">
  Setting up cache... hang in there!
  </div>
  </div>';

	$startMonth = date('Y-m-01');
	# Topbar statistics
		$sqlTopbar = "SELECT COUNT(*) FROM #cityrp#.players; ";
		$sqlTop1 = "SELECT sum(_Money) FROM #cityrp#.players; ";
		$sqlTop2 = "SELECT COUNT(*) FROM #cityrp#.logs WHERE type='prop' AND date>='$startMonth'; ";
		$sqlTop3 = "SELECT sum(_Points) FROM #cityrp#.players; ";
		$sqlTop4 = "SELECT COUNT(*) FROM #cityrp#.logs WHERE type='manufacture' AND date>='$startMonth'; ";
		$sqlTop5 = "SELECT SUM(_TimePlayed) FROM #cityrp#.players; ";

		$sql = $sqlTopbar . $sqlTop1 . $sqlTop2 . $sqlTop3 . $sqlTop4 . $sqlTop5;

		if (!$db->newQuery($sql, null, true)) {
			die("Error code 03: Multi query failed (Topbar Statistics)");
		}

		$topInfo = array();

		do {
		    if ($res = $db->store_result()) {
						array_push($topInfo, $res->fetch_row()[0]);
		        $res->free();
		    }
		} while ($db->more_results() && $db->next_result());


	# In-game statistics
		$sqlMostHours = "SELECT _Name, _SteamID, FLOOR(_TimePlayed / 3600) as hours, _Lastonline FROM #cityrp#.players ORDER BY hours DESC LIMIT $gameStatLimit; ";
		$sqlMostPoints = "SELECT _Name, _SteamID, _Igname, _Points, _Lastonline FROM #cityrp#.players ORDER BY _Points DESC LIMIT $gameStatLimit; ";
		$sqlMostMoney = "SELECT _Name, _SteamID, _Igname, _Money, _Lastonline FROM #cityrp#.players ORDER BY _Money DESC LIMIT $gameStatLimit; ";
		$sqlmostInvValue = "SELECT _Name, _SteamID, _Igname, _Invvalue, _Money, _Lastonline FROM #cityrp#.players ORDER BY _Invvalue DESC LIMIT $gameStatLimit; ";
		$sqlNetWorth = "SELECT _Name, _SteamID, _Igname, _Invvalue, _Money, _Invvalue + _Money AS networthadded, _Lastonline FROM #cityrp#.players ORDER BY networthadded DESC LIMIT $gameStatLimit; ";
		$sqlMostActive = "SELECT _Name, _SteamID, _svrank, server, FLOOR(_TimePlayed / 3600) AS hours, _Lastonline, connect, disconnect, steamid,
											SUM(CASE WHEN server != 'build' THEN disconnect - connect ELSE 0 END) AS timeMain,
											SUM(CASE WHEN server = 'build' THEN disconnect - connect ELSE 0 END) AS timeBuild,
											SUM(CASE WHEN server != 'build' THEN afktime ELSE 0 END) AS timeAFK,
									    SUM(disconnect - connect) AS timeTotal
											FROM #cityrp#.onlinelog
									    RIGHT JOIN #cityrp#.players ON steamid = _SteamID
									    WHERE MONTH(FROM_UNIXTIME(connect)) = MONTH(CURRENT_DATE()) AND YEAR(FROM_UNIXTIME(connect)) = YEAR(CURRENT_DATE()) AND _SteamID != 'BOT'
									    GROUP BY steamid
									    ORDER BY timeTotal
									    DESC LIMIT $gameStatLimit; ";
		$sqlMostPlants = "SELECT _Name, _SteamID, _Igname, _PlantsHarvested, _Lastonline FROM #cityrp#.players WHERE _PlantsHarvested != 0 ORDER BY _PlantsHarvested DESC LIMIT $gameStatLimit; ";
		$sqlMostRocks = "SELECT _Name, _SteamID, _Igname, _RocksMined, _Lastonline FROM #cityrp#.players WHERE _RocksMined != 0 ORDER BY _RocksMined DESC LIMIT $gameStatLimit; ";
		$sqlMostFish = <<<EOT
			SELECT _Name, _SteamID, _Igname, (iq._FishCaught + p._FishCaught) AS _FishCaught, _Lastonline FROM
				(
					SELECT mmu.steamid, SUM(jt.fish_count) AS `_FishCaught` FROM #cityrp#.meska_metrics_user mmu, JSON_TABLE(
						mmu.metrics,
						"$.fishing.*"
						COLUMNS (fish_count int path '$[0]')
					) jt
					GROUP BY
						mmu.steamid
				) iq
			INNER JOIN
				#cityrp#.players p ON iq.steamid COLLATE utf8mb4_general_ci = p._SteamID
			WHERE
				iq._FishCaught != 0 AND p._FishCaught != 0
			ORDER BY
				_FishCaught DESC
			LIMIT $gameStatLimit;
		EOT;
		$sqlMostWeed = "SELECT _Name, _SteamID, _Igname, _WeedHarvested, _Lastonline FROM #cityrp#.players WHERE _WeedHarvested != 0 ORDER BY _WeedHarvested DESC LIMIT $gameStatLimit; ";
		$sqlMostContra = "SELECT _Name, _SteamID, _Igname, _MoneyFromContra, _Lastonline FROM #cityrp#.players WHERE _MoneyFromContra != 0 ORDER BY _MoneyFromContra DESC LIMIT $gameStatLimit; ";


		$sql = $sqlMostHours . $sqlMostPoints . $sqlMostMoney . $sqlmostInvValue . $sqlNetWorth . $sqlMostActive . $sqlMostPlants . $sqlMostRocks . $sqlMostFish . $sqlMostWeed . $sqlMostContra;

		if (!$db->newQuery($sql, null, true)) {
			die("Error code 03: Multi query failed (Game Statistics)");
		}

		$gameStats = array();

		do {
		    if ($res = $db->store_result()) {
						array_push($gameStats, $res->fetch_all(MYSQLI_ASSOC));
		        $res->free();
		    }
		} while ($db->more_results() && $db->next_result());

		$newGameStat = array();

		foreach ($gameStats as $statLists) {
			$newList = array();
			foreach($statLists as $player) {
				$steamID = $player['_SteamID'];
				$sqlForum = "SELECT fearless_steamids.id, username, avatar, usergroup FROM fearless_steamids LEFT JOIN mybb_users ON mybb_users.uid = fearless_steamids.id WHERE steamid='$steamID'";
				$result = $db->query($sqlForum);
				if (mysqli_num_rows($result) > 0) {
					$row = mysqli_fetch_assoc($result);
					$player["Forum"] = $row;
				}
				array_push($newList, $player);
			}
			array_push($newGameStat, $newList);
		}

		$newGameStat["mostHours"] = $newGameStat[0];
		$newGameStat["mostPoints"] = $newGameStat[1];
		$newGameStat["mostMoney"] = $newGameStat[2];
		$newGameStat["highestInvValue"] = $newGameStat[3];
		$newGameStat["highestNetworth"] = $newGameStat[4];
		$newGameStat["mostActive"] = $newGameStat[5];
		$newGameStat["mostPlants"] = $newGameStat[6];
		$newGameStat["mostRocks"] = $newGameStat[7];
		$newGameStat["mostFish"] = $newGameStat[8];
		$newGameStat["mostWeed"] = $newGameStat[9];
		$newGameStat["mostContra"] = $newGameStat[10];

		unset($newGameStat[10]);
		unset($newGameStat[9]);
		unset($newGameStat[8]);
		unset($newGameStat[7]);
		unset($newGameStat[6]);
		unset($newGameStat[5]);
		unset($newGameStat[4]);
		unset($newGameStat[3]);
		unset($newGameStat[2]);
		unset($newGameStat[1]);
		unset($newGameStat[0]);


	# Forum statistics
		$sqlMemberCount = "SELECT COUNT(*) as playerCount FROM mybb_users; ";
		$sqlPosts = "SELECT uid, username, avatar, usergroup, displaygroup, lastpost, lastactive, postnum FROM mybb_users ORDER BY postnum DESC LIMIT $forumStatLimit; ";
		$sqlThreads = "SELECT uid, username, avatar, usergroup, displaygroup, lastpost, lastactive, threadnum FROM mybb_users ORDER BY threadnum DESC LIMIT $forumStatLimit; ";
		$sqlThreadViews = "SELECT mybb_users.uid, mybb_users.username, avatar, usergroup, displaygroup, mybb_users.lastpost, mybb_users.lastactive, SUM(views) as 'amount' FROM mybb_users LEFT JOIN mybb_threads ON mybb_threads.uid = mybb_users.uid GROUP BY uid ORDER BY amount DESC LIMIT $forumStatLimit; ";
		$sqlLikes = "SELECT uid, username, avatar, usergroup, displaygroup, lastpost, lastactive, tyl_unumrcvtyls FROM mybb_users ORDER BY tyl_unumrcvtyls DESC LIMIT $forumStatLimit; ";
		$sqlReputation = "SELECT uid, username, avatar, usergroup, displaygroup, lastpost, lastactive, reputation FROM mybb_users ORDER BY reputation DESC LIMIT $forumStatLimit; ";
		$sqlActivity = "SELECT uid, username, avatar, usergroup, displaygroup, lastpost, lastactive, timeonline FROM mybb_users ORDER BY timeonline DESC LIMIT $forumStatLimit; ";

		$sql2 = $sqlPosts . $sqlThreads . $sqlThreadViews . $sqlLikes . $sqlReputation . $sqlActivity;

        if (!$db->newQuery($sql2, null, true)) {
            die("Error code 03: Multi query failed (Forum Statistics)");
        }

        $forumStat = array();

        do {
            if ($res = $db->store_result()) {
                array_push($forumStat, $res->fetch_all(MYSQLI_ASSOC));
                $res->free();
            }
        } while ($db->more_results() && $db->next_result());

        $forumStat["mostPosts"] = $forumStat[0];
        $forumStat["mostThreads"] = $forumStat[1];
        $forumStat["mostViews"] = $forumStat[2];
        $forumStat["mostLikes"] = $forumStat[3];
        $forumStat["mostReputation"] = $forumStat[4];
        $forumStat["mostActiveForum"] = $forumStat[5];

        unset($forumStat[5]);
        unset($forumStat[4]);
        unset($forumStat[3]);
        unset($forumStat[2]);
        unset($forumStat[1]);
        unset($forumStat[0]);

        $result = $db->query($sqlMemberCount);
        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $forumStat["memberCount"] = $row["playerCount"];
        }

        $stats = array(
            "topBar" => $topInfo,
            "gameStats" => $newGameStat,
            "forumStats" => $forumStat
        );

        file_put_contents($cachefile, json_encode($stats));
        header("Refresh:0");
}
//print("<pre>".print_r($stats,true)."</pre>");
