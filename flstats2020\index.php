<!doctype html>

<html lang="en">
<?php

include('flstats2020/includes/config.php');

$titles = array(
  "mostHours" => '<i style="margin-right: 10px" class="far fa-clock" aria-hidden="true"></i> MOST HOURS',
  "mostPoints" => '<i style="margin-right: 10px" class="fa fa-star" aria-hidden="true"></i> MOST POINTS',
  "mostMoney" => '<i style="margin-right: 10px" class="fas fa-money-bill" aria-hidden="true"></i> RICHEST PLAYERS',
  "highestInvValue" => '<i style="margin-right: 10px" class="fa fa-university" aria-hidden="true"></i> HIGHEST INVENTORY VALUE',
  "highestNetworth" => '<i style="margin-right: 10px" class="fa fa-globe" aria-hidden="true"></i> HIGHEST NET WORTH',
  "mostActive" => '<i style="margin-right: 10px" class="fa fa-calendar" aria-hidden="true"></i> MOST ACTIVE IN '.date("F"),
  "mostPlants" => '<i style="margin-right: 10px" class="fa fa-leaf" aria-hidden="true"></i> MOST PLANTS HARVESTED',
  "mostRocks" => '<i style="margin-right: 10px" class="fas fa-hard-hat" aria-hidden="true"></i> MOST ROCKS MINED',
  "mostFish" => '<i style="margin-right: 10px" class="fas fa-fish" aria-hidden="true"></i> MOST FISH CAUGHT',
  "mostWeed" => '<i style="margin-right: 10px" class="fas fa-cannabis" aria-hidden="true"></i> MOST WEED HARVESTED',
  "mostContra" => '<i style="margin-right: 10px" class="fas fa-print" aria-hidden="true"></i> MOST MONEY FROM CONTRABAND',
  "mostPosts" => '<i style="margin-right: 10px" class="fa fa-sticky-note" aria-hidden="true"></i> MOST POSTS',
  "mostThreads" => '<i style="margin-right: 10px" class="fas fa-file-alt" aria-hidden="true"></i> MOST THREADS',
  "mostViews" => '<i style="margin-right: 10px" class="fa fa-wifi" aria-hidden="true"></i> HIGHEST TOTAL THREAD VIEWS',
  "mostLikes" => '<i style="margin-right: 10px" class="fas fa-thumbs-up" aria-hidden="true"></i> MOST LIKES RECEIVED',
  "mostReputation" => '<i style="margin-right: 10px" class="fa fa-user-plus" aria-hidden="true"></i> MOST REPUTATION',
  "mostActiveForum" =>'<i style="margin-right: 10px" class="fa fa-eye" aria-hidden="true"></i> MOST ACTIVE FORUM MEMBERS'
);

$statsStylesheet;
$darktheme = 38;
$currenttheme = $mybb->user['style'];
if ($darktheme == $currenttheme) {
    $statsStylesheet = "style_dark.css";
} else {
    $statsStylesheet = "style.css";
}
?>
<head>
  <meta charset="utf-8">

  <title>FearlessRP Statistics</title>
  <meta name="description" content="Statistics from Fearless server and forums">
  <meta name="author" content="FearlessRP - Floodify, Snowredwolf & Tomo">

  <link rel="stylesheet" href="flstats2020/css/<?php echo $statsStylesheet; ?>">
  <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css" integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous"/>
  <!-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.2/css/font-awesome.min.css"> -->
  <script src="flstats2020/js/jquery.counterup.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.js"></script>
<?php
if (!isset($_GET['stat'])){
?>
  <script>
    jQuery(document).ready(function($) {
      $('.counter').counterUp({
        delay: 10,
        time: 2000
      });
    });
  </script>
<?php } ?>

</head>
<body>
  <div class="stats_header">
    <img class="stats_header_logo" src="flstats2020/images/header_logo_final.png">
    <span class="stats_header_subtext">
      the community with <span class="counter"><?php echo number_format($stats["topBar"][0]); ?></span> joined players and <span class="counter"><?php echo number_format($stats["forumStats"]["memberCount"]); ?></span> members
    </span>
  </div>
  <div class="content_stat">
    <div class="top_image_stats">
      <table class="top_image_stats_table">
        <td>
          <div class="top_image_stats_box" id="box_bg_1">
            <p class="top_image_stats_box_statistic">
              $ <span class="counter"><?php echo number_format($stats["topBar"][1]); ?></span>
            </p>
            <hr class="top_image_stats_box_divider">
            <p class="top_image_stats_box_subtext">
              <?php echo $topInfoText[0] ?>
            </p>
          </div>
        </td>
        <td>
          <div class="top_image_stats_box" id="box_bg_2">
            <p class="top_image_stats_box_statistic">
              <span class="counter"><?php echo number_format($stats["topBar"][2]); ?></span>
            </p>
            <hr class="top_image_stats_box_divider">
            <p class="top_image_stats_box_subtext">
              <?php echo $topInfoText[1] ?>
            </p>
          </div>
        </td>
        <td>
          <div class="top_image_stats_box" id="box_bg_3">
            <p class="top_image_stats_box_statistic">
              <span class="counter"><?php echo number_format($stats["topBar"][3]); ?></span>
            </p>
            <hr class="top_image_stats_box_divider">
            <p class="top_image_stats_box_subtext">
              <?php echo $topInfoText[2] ?>
            </p>
          </div>
        </td>
        <td>
          <div class="top_image_stats_box" id="box_bg_4">
            <p class="top_image_stats_box_statistic">
              <span class="counter"><?php echo number_format($stats["topBar"][4]); ?></span>
            </p>
            <hr class="top_image_stats_box_divider">
            <p class="top_image_stats_box_subtext">
              <?php echo $topInfoText[3] ?>
            </p>
          </div>
        </td>
        <td>
          <div class="top_image_stats_box" id="box_bg_5">
            <p class="top_image_stats_box_statistic">
              <span class="counter"><?php echo number_format(intVal($stats["topBar"][5]/3600)); ?></span>
            </p>
            <hr class="top_image_stats_box_divider">
            <p class="top_image_stats_box_subtext">
              <?php echo $topInfoText[4] ?>
            </p>
          </div>
        </td>
      </table>
    </div>


    <div class="main_content">
      <table class="stats_complete_table" style="width: 100%;" cellspacing='0' cellpadding='0'>
        <td style="width: 20%" valign="top">
          <div class="main_menu">
            <table style="width: 100%" cellspacing='0' cellpadding='0'>
              <tr>
                <td>
                  <div class="main_menu_header">
                    Server Statistics
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostHours&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostHours" || empty($_GET['stat'])){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostHours" || empty($_GET['stat'])){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="far fa-clock" aria-hidden="true"></i> Most Hours
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostPoints&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostPoints"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostPoints"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-star" aria-hidden="true"></i> Most Roleplay Points
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostMoney&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostMoney"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostMoney"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-money-bill" aria-hidden="true"></i> Richest Players
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=highestInvValue&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="highestInvValue"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="highestInvValue"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-university" aria-hidden="true"></i> Highest Inventory Value
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=highestNetworth&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="highestNetworth"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="highestNetworth"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-globe" aria-hidden="true"></i> Highest Net Worth
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostActive&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostActive"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostActive"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-calendar" aria-hidden="true"></i> Most Active Players
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
            </table>
          </div>
		  <!--Farming-->
		  <div class="main_menu" style="margin-top: 12px">
            <table style="width: 100%" cellspacing='0' cellpadding='0'>
              <tr>
                <td>
                  <div class="main_menu_header">
                    Game Statistics
                  </div>
                </td>
              </tr>
			  <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostPlants&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostPlants"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostPlants"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-leaf" aria-hidden="true"></i> Most Plants Harvested
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
			  <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostRocks&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostRocks"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostRocks"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-hard-hat" aria-hidden="true"></i> Most Rocks Mined
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
			  <tr>
                <td>
                  <!-- <a class="main_menu_link" href="?stat=mostTrees&filter=all"> -->
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostTrees"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostTrees"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-tree" aria-hidden="true"></i> Coming Soon...
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
            </table>
          </div>
        </td>

        <td style="width: 60%" valign="top">
          <div class="stat_display" id="stat_display">

            <div class='stat_box_title'>
              <table style="width: 100%" cellspacing='0' cellpadding='0'>
                <td>
                  <p class="stat_box_title_main">
                    <?php echo (!empty($_GET['stat'])) ? $titles[$_GET['stat']] : $titles['mostHours']?>
                  </p>
                </td>
                <td>
                  <a href="<?php echo '?stat=' . htmlspecialchars($_GET['stat']) . (($_GET['filter'] == 'all') ? '&filter=active' : '&filter=all') ?>">
                    <span class="groupButton">
          						 <?php echo ($_GET['filter'] == 'all') ? "Show Last 2 Month Active" : "Show  All Time Active"; ?>
          					</span>
                  </a>
                </td>
              </table>
            </div>
            <div class="scrollable">
              <table class="stats_middle_table ::-webkit-scrollbar" style="width: 100%" cellspacing='0' cellpadding='0'>
                <?php include("includes/serverstats/gameTop.php") ?>
                <?php include("includes/serverstats/forumTop.php") ?>
              </table>
            </div>
          </div>
        </td>
        <td style="width: 20%" align="right" valign="top">
          <div class="main_menu">
            <table style="width: 100%" cellspacing='0' cellpadding='0'>
              <tr>
                <td>
                  <div class="main_menu_header">
                    Forum Statistics
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostPosts&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostPosts"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostPosts"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-sticky-note" aria-hidden="true"></i> Most Forum Posts
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostThreads&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostThreads"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostThreads"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-file-alt" aria-hidden="true"></i> Most Threads Posted
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostViews&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostViews"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostViews"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-wifi" aria-hidden="true"></i> Most Thread Views
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostLikes&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostLikes"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostLikes"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-thumbs-up" aria-hidden="true"></i> Most Likes Received
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostReputation&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostReputation"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostReputation"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-user-plus" aria-hidden="true"></i> Most Reputation Received
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostActiveForum&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostActiveForum"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostActiveForum"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fa fa-eye" aria-hidden="true"></i> Most Active Member
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
            </table>
          </div>
		  <!--New-->
		  <div class="main_menu" style="margin-top: 12px">
            <table style="width: 100%" cellspacing='0' cellpadding='0'>
              <tr>
                <td>
                  <div class="main_menu_header">
                    Game Statistics
                  </div>
                </td>
              </tr>
			  <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostWeed&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostWeed"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostWeed"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-cannabis" aria-hidden="true"></i> Most Weed Harvested
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostContra&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostContra"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostContra"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-print" aria-hidden="true"></i> Most Contra Money
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
			  <tr>
                <td>
                  <a class="main_menu_link" href="?stat=mostFish&filter=all">
                    <div class="main_menu_cell">
                      <?php if($_GET['stat']=="mostFish"){ echo '<div class="main_menu_cell_active"></div>'; } ?>
                      <p class="main_menu_cell_text" <?php if($_GET['stat']=="mostFish"){ echo 'id="active_nav"'; } ?>>
                        <i style="margin-right: 10px" class="fas fa-fish" aria-hidden="true"></i> Most Fish Caught
                      </p>
                    </div>
                  </a>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </table>
    </div>
  </div>
  <div class="footer_stats">

    All statistics updated every 30 mins | Frontend by <a style="color: white;" target="_blank" href="https://www.fearlessrp.net/member.php?action=profile&uid=7347">Floodify</a> & backend recoded by <a style="color: white;" target="_blank" href="https://www.fearlessrp.net/member.php?action=profile&uid=8696">SnowredWolf</a> | &copy; 2008-<?php echo date("Y"); ?> Fearless Community
  </div>
</body>
</html>
