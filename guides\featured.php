<?php

global $hide, $guides, $parser;
if (!defined('IN_MYBB')){
	die();
}

?>
<div class="<?= $hide ? "" : "" ?> p-4 bg-silver-100 rounded-lg md:p-8 dark:bg-gray-800 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="guides-<?= $pid ?>" role="tabpanel" aria-labelledby="guides-button-<?= $pid ?>">
	<?php
		if ($pid === ''){
			$pid = 0;
		}
		foreach ($guides[$pid] as $guide){
			$post = get_post($guide['firstpost']);

			$summary = '';
			$image = false;
			if ($post){
				$summary = $parser->parse_message($post['message'], ['allow_html' => false, 'filter_badwords' => true, 'allow_mycode' => true, 'allow_smilies' => false, 'nl2br' => true, 'me_username' => false, 'filter_cdata' => true, 'allow_imgcode' => true]);

				$doc = new DOMDocument();
				$doc->loadHTML('<root>' . $summary . '</root>');
				$images = $doc->getElementsByTagName('img');
				$img = false;
				foreach ($images as $image){
					/** @var DOMElement $image */
					if (!$img){
						$img = $image->getAttribute('src');
					}
					$image->parentNode->removeChild($image);
				}
				$image = $img;
				$summary = $doc->saveHTML();
				$summary = strip_tags($summary, []);
				$summary = preg_replace('/\[(Image|Video): (.*?)\]/', '', $summary);
				$summary = trim($summary);
				$summary = preg_replace('/\n\n+/', "\n", $summary);
				if (mb_strlen($summary) > 200){
					$summary = mb_substr($summary, 0, 200);
					$summary = trim($summary);
					$summary = explode(' ', $summary);
					array_pop($summary);
					$summary = implode(' ', $summary) . '...';
				}
			}

			if ($summary === '...'){
				$post = false;
				$summary = '';
			}
	?>
		<a href="?gid=<?= $guide['tid'] ?>" class="max-w-sm bg-silver-50 border border-silver-200 rounded-lg shadow hover:bg-silver-400">
			<?php if ($image){ ?>
				<img class="rounded-t-lg" src="<?= $image ?>" alt="<?= htmlspecialchars_uni($guide['subject']) ?> Header Image" />
			<?php } ?>
				<h1 class="text-center mx-4 my-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white"><?= htmlspecialchars_uni($guide['subject']) ?></h1>
				<p class="mx-4 mb-2 font-normal bg-gradient-to-b from-dark via-dark to-transparent bg-clip-text text-transparent"><?= $summary ?></p>
		</a>
	<?php } ?>
</div>
