<?php

if (!defined('IN_MYBB')){
	die();
}

$gid = (int)($_GET['gid'] ?? 0);
$guide = get_thread($gid);

if ((int)$guide['fid'] !== GUIDE_FID && (int)$guide['fid'] !== HANDS_FID){
	return true;
}
if ((int)$guide['visible'] !== 1){
	return true;
}

while (str_starts_with($guide['closed'], 'moved')){
	$guide = get_thread(explode('|', $guide['closed'])[1]);
}

$post = get_post($guide['firstpost']);
$prefix = build_prefixes($guide['prefix'])['prefix'] ?? 'General';
$pid = (int)($guide['prefix'] ?? 0);

global $parser, $mybb;
$message = $parser->parse_message($post['message'], ['allow_html' => false, 'filter_badwords' => true, 'allow_mycode' => true, 'allow_smilies' => false, 'nl2br' => true, 'me_username' => false, 'filter_cdata' => true, 'allow_imgcode' => true, 'allow_videocode' => true]);

$user = get_user($guide['uid']);
$avatar = format_avatar($user['avatar'], $user['avatardimensions'], my_strtolower($mybb->settings['memberlistmaxavatarsize']))['image'];

$status = 'bg-silver-700';
$last_seen = max((int)$user['lastactive'], (int)$user['lastvisit']);
if ($user['invisible'] != 1){
	$online = TIME_NOW - ((int)$mybb->settings['wolcutoffmins'] * 60);
	if ($online < $last_seen){
		$status = 'bg-green-500';
	}
}

?>
<html lang="en_GB">
	<head>
		<title>Fearless Guides</title>
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;1,300;1,400;1,500;1,600&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;1,100;1,300;1,400;1,500&display=swap" rel="stylesheet">

		<link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.css" rel="stylesheet">
		<link href="./css/style.css?date=************" rel="stylesheet">
	</head>

	<body class="font-body bg-dark">
		<div class="max-w-screen flex flex-col flex-wrap px-24 py-12">
			<nav class="flex px-5 py-3 text-silver-700 border border-b-0 border-silver-200 rounded-t-lg bg-silver-50" aria-label="Breadcrumb">
				<ol class="inline-flex items-center space-x-1 md:space-x-3">
					<li class="inline-flex items-center">
						<a href="." class="inline-flex items-center text-sm font-medium text-dark hover:text-accent-blue">
							<svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
								<path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
							</svg>
							Guides
						</a>
					</li>
					<li>
						<div class="flex items-center">
							<svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
							</svg>
							<a href="?page=<?= $pid ?>" class="ml-1 text-sm font-medium text-dark md:ml-2 hover:text-accent-blue"><?= $prefix ?></a>
						</div>
					</li>
					<li aria-current="page">
						<div class="flex items-center">
							<svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
							</svg>
							<span class="ml-1 text-sm font-medium text-dark md:ml-2 cursor-default"><?= htmlspecialchars_uni($guide['subject']) ?></span>
						</div>
					</li>
				</ol>
			</nav>
			<div class="flex items-center justify-center py-4 space-x-3 rounded-b-lg text-silver-700 border border-t-0 border-silver-200 bg-silver-50 mb-8">
				<div class="relative">
					<img class="w-10 h-10 rounded-full ring-2 ring-accent-blue" src="<?= $avatar ?>" alt="<?= htmlspecialchars_uni($user['username']) ?>'s Profile Picture>">
					<span class="bottom-0 left-7 absolute w-3.5 h-3.5 <?= $status ?> border-2 border-white dark:border-gray-800 rounded-full"></span>
				</div>
				<div class="flex items-center divide-x-2 divide-silver-500">
					<cite class="pr-3 tex-lg font-medium text-silver-900"><?= htmlspecialchars_uni($user['username']) ?></cite>
					<time class="pl-3 text-sm text-silver-800">Last Updated: <?= date('Y-m-d H:i', $post['edittime'] === '0' ? $post['dateline'] : $post['edittime']) ?></time>
				</div>
			</div>

			<div class="items-center justify-center py-4 space-x-3 rounded-lg text-silver-700 border border-t-0 border-silver-200 bg-silver-50 px-4">
				<?= $message ?>
			</div>
		</div>
	</body>
</html>
