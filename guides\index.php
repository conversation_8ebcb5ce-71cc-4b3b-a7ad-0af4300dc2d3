<?php

function dd(...$a){
	echo "<pre>";
	foreach ($a as $b){
		var_dump($b);
	}
	echo "</pre>";
	die();
}

define('IN_MYBB', 1);
define('THIS_SCRIPT', 'guides');
define('GUIDE_FID', 61);
define('HANDS_FID', 85);

require_once __DIR__ . "/../global.php";
require_once MYBB_ROOT . "inc/functions_post.php";
require_once MYBB_ROOT . "inc/functions_forumlist.php";
require_once MYBB_ROOT . "inc/class_parser.php";

$parser = new postParser();

global $mybb;
$page = ($_GET['page'] ?? false);
if ($page !== false){
	$page = (int)$page;
}

$gid = (int)($_GET['gid'] ?? false);


if ($gid){
	require __DIR__ . '/guide.php';
	die();
}

global $db;

$query = $db->query(sprintf("
	SELECT
		p.*
	FROM
		%sthreads t
			LEFT JOIN
				%sthreadprefixes p
			ON
				t.prefix = p.pid
	WHERE
		t.fid IN (%s, %s) AND
		t.visible >= 1 AND
		t.closed IN (0, 1)
	GROUP BY
		t.prefix
", TABLE_PREFIX, TABLE_PREFIX, GUIDE_FID, HANDS_FID));
$groups = [];
$query = $query->fetch_all(MYSQLI_ASSOC);
foreach ($query as $group){
	if (is_null($group['pid'])){
		$groups[$group['pid']] = 'General';
	} elseif ($group['prefix'] !== 'Meta'){
		$groups[$group['pid']] = $group['prefix'];
	}
}

uksort($groups, static function (string $ak, string $bk) use ($groups){
	$remap = ['Featured' => -2, 'General' => -1, 'Policy' => 0];

	$av = $groups[$ak];
	$bv = $groups[$bk];

	if (isset($remap[$av])){
		$ak = $remap[$av];
	}
	if (isset($remap[$bv])){
		$bk = $remap[$bv];
	}

	return (int)$ak - (int)$bk;
});

$query = $db->query(sprintf("
	SELECT
		*
	FROM
		%sthreads t
	WHERE
		t.fid IN (%s, %s) AND
		t.visible >= 1 AND
		t.closed IN (0, 1)
	ORDER BY
		t.sticky DESC,
		t.dateline DESC
", TABLE_PREFIX, GUIDE_FID, HANDS_FID));

$guides = [];
foreach ($query as $guide){
	$guides[] = $guide;
}
$guides = array_groupby('prefix', $guides);

require __DIR__ . '/list.php';


die();

