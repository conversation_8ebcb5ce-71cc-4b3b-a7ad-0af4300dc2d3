<?php

global $groups, $page;

if (!defined('IN_MYBB')){
	die();
}

?>
<html lang="en_GB">
	<head>
		<title>Fearless Guides</title>
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;1,300;1,400;1,500;1,600&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;1,100;1,300;1,400;1,500&display=swap" rel="stylesheet">

		<link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.css" rel="stylesheet">
		<link href="./css/style.css?date=************" rel="stylesheet">
	</head>

	<body class="font-body bg-dark">
		<div class="max-w-screen flex flex-col flex-wrap px-24 py-12">

			<div class="w-full bg-white border border-gray-200 rounded-lg shadow">
				<?php
					$hide = false;
					$first = true;
					$i = 0;

					$cnt = count($groups);
					if ($cnt > 0){ $hide = true; ?>
					<ul class="flex flex-wrap text-lg font-header font-medium text-center text-silver-300 divide-x divide-y divide-silver-200 rounded-lg" id="fullWidthTab" data-tabs-toggle="#fullWidthTabContent" role="tablist">
						<?php foreach ($groups as $pid => $prefix){
							$i++;
							if ($page !== false && $pid !== 51){
								$first = (int)$pid === $page;
							}
						?>
							<li class="flex-auto basis-1/3 md:basis-1/4 bg-silver-100 hover:text-accent-blue focus:outline-none <?= $i === 1 ? 'rounded-tl-lg' : ($i === $cnt ? 'rounded-tr-lg' : '') ?>">
								<button
									id="guides-button-<?= $pid ?>"
									data-tabs-target="#guides-<?= $pid ?>"
									type="button"
									role="tab"
									aria-controls="stats"
									aria-selected="<?= $first ? 'true' : 'false' ?>"
									class="inline-block w-full p-4 <?= $i === 1 ? 'rounded-tl-lg' : ($i === $cnt ? 'rounded-tr-lg' : '') ?> aria-selected:text-accent-blue-600 hover:bg-silver-200"><?= $prefix ?></button>
							</li>
						<?php $first = false; } ?>
					</ul>
				<?php } ?>
				<div id="fullWidthTabContent" class="border-t-2 border-silver-200">
					<?php
						global $guides, $parser;
						foreach ($groups as $pid => $prefix){
							if ($prefix === 'Featured'){
								require __DIR__ . '/featured.php';
								continue;
							}
					?>
					<div class="<?= $hide ? "hidden" : "" ?> p-4 bg-silver-100 rounded-lg md:p-8" id="guides-<?= $pid ?>" role="tabpanel" aria-labelledby="guides-button-<?= $pid ?>">
						<?php
							if ($pid === ''){
								$pid = 0;
							}
							foreach ($guides[$pid] as $guide){
								$post = get_post($guide['firstpost']);

								$summary = '';
								if ($post){
									$summary = $parser->parse_message($post['message'], ['allow_html' => false, 'filter_badwords' => true, 'allow_mycode' => true, 'allow_smilies' => false, 'nl2br' => true, 'me_username' => false, 'filter_cdata' => true]);

									$doc = new DOMDocument();
									$doc->loadHTML('<root>' . $summary . '</root>');
									$images = $doc->getElementsByTagName('img');
									foreach ($images as $image){
										$doc->removeChild($image);
									}
									$summary = $doc->saveHTML();
									$summary = strip_tags($summary, []);
									$summary = preg_replace('/\[(Image|Video): (.*?)\]/', '', $summary);
									$summary = trim($summary);
									$summary = preg_replace('/\n\n+/', "\n", $summary);

									if (mb_strlen($summary) > 200){
										$summary = substr($summary, 0, 200);
										$summary = trim($summary);
										$summary = explode(' ', $summary);
										array_pop($summary);
										$summary = implode(' ', $summary) . '...';
									}
								}

								if ($summary === '...'){
									$post = false;
									$summary = '';
								}
						?>
							<a href="/guides/?gid=<?= $guide['tid'] ?>" class="mb-4 w-full flex flex-col items-center bg-silver-200 border border-silver-300 rounded-lg shadow md:flex-row hover:bg-silver-400">
								<?php if ($guide['sticky']){ ?>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-12 h-12 pl-2 min-w-[48px] text-accent-blue">
										<path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
									</svg>
									<!--				--><?php //} else { ?>
									<!--					<img class="object-cover w-full rounded-t-lg h-96 md:h-auto md:w-48 md:rounded-none md:rounded-l-lg" src="--><?php //= str_starts_with($guide['avatar'], '.') ? htmlspecialchars_uni('https://fearlessrp.net/' . $guide['avatar']) : htmlspecialchars_uni($guide['avatar']) ?><!--" alt="">-->
								<?php } ?>
								<?php if ($guide['closed'] === '1'){ ?>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-12 h-12 pl-2 min-w-[48px] text-silver-600">
										<path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z" clip-rule="evenodd" />
									</svg>
								<?php } elseif (str_starts_with($guide['closed'], 'moved')){ ?>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-12 h-12 pl-2 min-w-[48px] text-accent-blue">
										<path fill-rule="evenodd" d="M19.902 4.098a3.75 3.75 0 00-5.304 0l-4.5 4.5a3.75 3.75 0 001.035 *********** 0 01-.646 1.353 5.25 5.25 0 01-1.449-8.45l4.5-4.5a5.25 5.25 0 117.424 7.424l-1.757 1.757a.75.75 0 11-1.06-1.06l1.757-1.757a3.75 3.75 0 000-5.304zm-7.389 4.267a.75.75 0 011-.353 5.25 5.25 0 011.449 8.45l-4.5 4.5a5.25 5.25 0 11-7.424-7.424l1.757-1.757a.75.75 0 111.06 1.06l-1.757 1.757a3.75 3.75 0 105.304 5.304l4.5-4.5a3.75 3.75 0 00-1.035-*********** 0 01-.354-1z" clip-rule="evenodd" />
									</svg>
								<?php } ?>

								<div class="flex flex-col justify-between p-4 leading-normal">
									<h5 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white font-header"><?= htmlspecialchars_uni($guide['subject']) ?></h5>
									<?php if ($summary){ ?>
										<span class="mt-2 font-normal bg-gradient-to-b from-dark via-dark to-transparent bg-clip-text text-transparent"><?= $summary ?></span>
									<?php } ?>
									<!--					<pre>--><?php //var_dump($post); ?><!--</pre>-->
								</div>
							</a>
						<?php } ?>
					</div>
					<?php } ?>
				</div>
			</div>
		</div>
	</body>

	<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.js"></script>
</html>
