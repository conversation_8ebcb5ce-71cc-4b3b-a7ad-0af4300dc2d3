const plugin = require('tailwindcss/plugin')

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './*.php',
    "./node_modules/flowbite/**/*.js"
  ],

  safelist: [
    'bg-green-500',
    'bg-silver-700',
    'aria-selected:text-accent-blue-600'
  ],

  theme: {
    extend: {
      colors: {
        'fearless-blue': '#32D2DC',

        'accent-blue': {
          DEFAULT: '#018F99',
          '50': '#eafffc',
          '100': '#cafff8',
          '200': '#9cfff5',
          '300': '#58fff2',
          '400': '#0efff8',
          '500': '#00e8e9',
          '600': '#00b8c3',
          '700': '#018f99',
          '800': '#0b747f',
          '900': '#0e5f6b',
          '950': '#02414a',
        },
        'silver': {
          DEFAULT: '#bababa',
          '50': '#f7f7f7',
          '100': '#ededed',
          '200': '#dfdfdf',
          '300': '#c8c8c8',
          '400': '#bababa',
          '500': '#999999',
          '600': '#888888',
          '700': '#7b7b7b',
          '800': '#676767',
          '900': '#545454',
          '950': '#363636'
        },
        'dark': {
          DEFAULT: '#363636',
        },
        'light': {
          DEFAULT: '#ededed',
        },

        // Rank Colors
        'management': '#87019B',
        'admin': '#CC01CC',
        'trial-admin': '#137B89',

        // Development
        'developer': '#CC0101',
        'contributor': '#F26D40',

        // Events
        'event-manager': '#6ab4fc',
        'event-coord': '#0c7ae2',

        // Supporters
        'supporter-plus': '#ff0081',
        'supporter': '#0c7ae2',

        // Veteran
        'vet-founder': '#808000',
        'vet-sa': '#99CCFF',
        'vet-admin': '#DDC101',
        'vet-dev': '#B87333'
      },

      fontFamily: {
        'logo': ['Harabara Mais'],
        'header': ['Roboto', 'sans-serif'],
        'body': ['Open Sans', 'sans-serif']
      },

      textShadow: {
        sm: '0 1px 2px var(--tw-shadow-color)',
        DEFAULT: '0 2px 4px var(--tw-shadow-color)',
        lg: '0 8px 16px var(--tw-shadow-color)',
      },
    },
  },

  plugins: [
    require('flowbite/plugin'),

    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
          {
            'text-shadow': (value) => ({
              textShadow: value,
            }),
          },
          { values: theme('textShadow') }
      )
    }),
  ],
}
