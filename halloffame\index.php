<?php

$darktheme = 38;
$currenttheme = $mybb->user['style'];

$teampageStylesheet = "pagestyle.css";
if ($darktheme === $currenttheme){
	$teampageStylesheet = "pagestyle_dark.css";
}

?>
<head>
	<!-- Page Info -->
	<title>Fearless Team</title>
	<link type="text/css" rel="stylesheet" href="../team/css/<?= $teampageStylesheet . '?' . time(); ?>.css" />
	<script src="https://use.fontawesome.com/1c797fefd5.js"></script>
	<link rel="icon" href="images/logo.png">
</head>
<body>
	<div class='changelog_title_box'>
		<p class="changelog_title">
			Fearless Hall of Fame
		<p class="changelog_sub_title">
			A list of our former team members
		</p>
		</p>
	</div>

	<section id="main">
		<div class="container">
			<?php

			$groups = [46, 22, 45, 64];
			$gmap = [];
			$search = implode(", ", $groups);
			$groupQuery = $db->simple_select("usergroups", "gid, title, description, namestyle", "gid IN ({$search})", [
				'order_by' => 'disporder',
				'order_dir' => 'ASC'
			]);

			if ($groupQuery->num_rows >= 1){
				foreach ($groupQuery as $group){
					$users = $db->simple_select('users', "uid, username, avatar, usergroup, lastvisit, invisible, lastactive", "usergroup = {$group['gid']} OR FIND_IN_SET('{$group['gid']}', additionalgroups)");
					?>
					<table>
						<tr class='tophead'>
							<th colspan='4' class='rank_head'><?= htmlspecialchars_uni($group['title']) ?></th>
						</tr>
						<tr>
							<th colspan='4' class='description_row'><?= htmlspecialchars_uni($group['description']) ?></th>
						</tr>
						<tr>
							<th class='column_headers'></th>
							<th style='width:65%;' class='column_headers'>Username</th>
							<th class='column_headers'>Last Visit</th>
							<th class='column_headers_pm'>PM</th>
						</tr>
						<?php foreach ($users as $row){
							if ($row["avatar"] === ''){
								$row["avatar"] = 'images/default_avatar.png';
							}
							if ($row['invisible'] === 1 && $mybb->usergroup['canviewwolinvis'] !== 1 && $row['uid'] !== $mybb->user['uid']){
								if ($row['lastactive']){
									$row['lastvisit'] = $lang->lastvisit_hidden;
								} else {
									$row['lastvisit'] = $lang->lastvisit_never;
								}
							} else {
								$row['lastvisit'] = my_date('relative', $row['lastactive']);
							}


							?>
							<tr>
								<td style='width:40px; height:40px;'>
									<img src='<?= htmlspecialchars_uni($row["avatar"]) ?>'>
								</td>
								<td>
									<?php if (isset($gmap[$group['gid']])){ ?>
										<a class='rank_color_<?= $gmap[$group['gid']] ?>' target='_blank' href='<?= $mybb->settings['bburl'] ?>/<?= get_profile_link($row['uid']) ?>'><?= format_name(htmlspecialchars_uni($row['username']), $group['gid']) ?></a>
									<?php } else { ?>
										<a target='_blank' href='<?= $mybb->settings['bburl'] ?>/<?= get_profile_link($row['uid']) ?>'><?= format_name(htmlspecialchars_uni($row['username']), $group['gid']) ?></a>
									<?php } ?>
								</td>
								<td><?= $row['lastvisit'] ?></td>
								<td>
									<div class='pm_button'>
										<a href='<?= $mybb->settings['bburl'] ?>/private.php?action=send&uid=<?= $row['uid'] ?>' title='Send this user a Private Message'>PM</a>
									</div>
								</td>
							</tr>
						<?php } ?>
					</table>
				<?php }
			} ?>
			<br>
			<div style='text-align:center;' class='bottom_button'>
				<a href='<?= $mybb->settings['bburl'] ?>/team.php'>View the Team</a>
			</div>
		</div>
	</section>

	<footer>
		<div class="copyright">
			<p>&copy; 2008-<?php echo date("Y"); ?> Fearless Community</p>
		</div>
	</footer>
</body>
