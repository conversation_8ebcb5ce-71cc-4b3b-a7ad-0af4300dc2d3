<?php
/**
 * The original PHP version of this code was written by <PERSON>
 * <<EMAIL>>, and is used/adapted with his permission.
 *
 * Copyright 2004 <PERSON> <<EMAIL>>
 * Copyright 2004-2017 Horde LLC (http://www.horde.org/)
 *
 * See the enclosed file COPYING for license information (LGPL). If you did
 * not receive this file, see http://www.horde.org/licenses/lgpl21.
 *
 * @package Text_Diff
 * <AUTHOR> <<EMAIL>>
 */

// Disallow direct access to this file for security reasons
if(!defined("IN_MYBB"))
{
	die("Direct initialization of this file is not allowed.<br /><br />Please make sure IN_MYBB is defined.");
}

class Horde_Text_Diff_Op_Add extends Horde_Text_Diff_Op_Base
{
    public function __construct($lines)
    {
        $this->final = $lines;
        $this->orig = false;
    }

    public function reverse()
    {
        return new Horde_Text_Diff_Op_Delete($this->final);
    }
}
