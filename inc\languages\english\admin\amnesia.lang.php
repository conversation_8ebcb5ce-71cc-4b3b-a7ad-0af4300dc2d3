<?php

$l['amnesia_admin_pluginlibrary_missing'] = 'Add <a href="https://github.com/frostschutz/MyBB-PluginLibrary">PluginLibrary</a> in order to use the plugin';

$l['amnesia_admin_personal_data_erasure_requests'] = 'Data Erasure Requests';
$l['amnesia_admin_personal_data_erasure_requests_unapproved'] = 'Unapproved Requests';
$l['amnesia_admin_personal_data_erasure_requests_unapproved_description'] = 'This section allows you to manage personal data erasure requests.';

$l['amnesia_admin_personal_data_erasure_requests_pending'] = 'Pending Requests';
$l['amnesia_admin_personal_data_erasure_requests_pending_description'] = 'This section allows you to view pending erasure requests.';

$l['amnesia_admin_personal_data_erasure_requests_completed'] = 'Completed Requests';
$l['amnesia_admin_personal_data_erasure_requests_completed_description'] = 'This section allows you to view completed erasure requests.';

$l['amnesia_admin_user'] = 'User';
$l['amnesia_admin_user_id'] = 'ID {1}';
$l['amnesia_admin_date'] = 'Date';
$l['amnesia_admin_scheduled_date'] = 'Scheduled for';
$l['amnesia_admin_action_date'] = 'Erased';
$l['amnesia_admin_with_content'] = 'Content';
$l['amnesia_admin_comment'] = 'Comment';
$l['amnesia_admin_view_profile'] = 'View Profile';
$l['amnesia_admin_approve'] = 'Approve';
$l['amnesia_admin_cancel'] = 'Cancel';
$l['amnesia_admin_personal_data_erasure_requests_unapproved_empty'] = 'No requests awaiting approval.';
$l['amnesia_admin_personal_data_erasure_requests_pending_empty'] = 'No pending requests.';
$l['amnesia_admin_personal_data_erasure_requests_completed_empty'] = 'No completed requests.';
$l['amnesia_admin_personal_data_erasure_approve_confirm_title'] = 'Confirm request approval';
$l['amnesia_admin_personal_data_erasure_approve_confirm_message'] = 'Are you sure you want to approve the selected erasure request?';
$l['amnesia_admin_personal_data_erasure_cancel_confirm_title'] = 'Confirm request cancellation';
$l['amnesia_admin_personal_data_erasure_cancel_confirm_message'] = 'Are you sure you want to cancel the selected erasure request?';
$l['amnesia_admin_personal_data_erasure_request_approved'] = 'Selected request was successfully approved.';
$l['amnesia_admin_personal_data_erasure_request_canceled'] = 'Selected request was successfully canceled.';

$l['amnesia_erasure_task_ran'] = 'The Amnesia (erasure) task successfully ran.';
