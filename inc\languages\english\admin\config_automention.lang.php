<?php
$l['automention_sett_desc'] = 'Settings related to the Automention plugin';
$l['automention_onoff_title'] = 'Enable automention?';
$l['automention_onoff_desc'] = 'Set to "Yes" if you want to enable automention; no to disable it';
$l['automention_limitems_title'] = 'Maximum pop-up items';
$l['automention_limitems_desc'] = 'Set here the maximum number of items to show in the automention pop-up.';
$l['automention_maxlength_title'] = 'Maximum mention match length';
$l['automention_maxlength_desc'] = 'Set here the maximum length of the string after `at` (@) to try to match.';
$l['automention_avatar_title'] = 'Support avatars?';
$l['automention_avatar_desc'] = 'Set to "Yes" if you want to enable avatar support in the automention pop-up.';
$l['automention_space_title'] = 'Support spaces?';
$l['automention_space_desc'] = 'Set to "Yes" if you want to enable support for spaces in usernames to be auto-completed.<br />N.B. This feature can degrade fluidity in typing.';
$l['automention_fulltext_title'] = 'Support full-text search?';
$l['automention_fulltext_desc'] = 'Set to "Yes" if you want to match anywhere inside usernames, rather than from only the beginning of usernames.';
$l['automention_threadpart_title'] = 'Support thread participant listings?';
$l['automention_threadpart_desc'] = 'Set to "Yes" if you want the automention pop-up for an empty match (an @ character alone) to list thread participants.';
