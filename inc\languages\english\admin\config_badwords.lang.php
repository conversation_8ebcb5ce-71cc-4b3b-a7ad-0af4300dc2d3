<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['bad_words'] = "Word Filters";
$l['edit_bad_word'] = "Edit Filter";
$l['edit_bad_word_desc'] = "Here you can edit the filter and its replacement.";
$l['bad_word_filters'] = "Word Filters";
$l['bad_word_filters_desc'] = "This feature allows you to manage a listing of words or phrases which are automatically replaced in posts on your forum. It is useful for replacing swear words and such.";

$l['bad_word'] = "Word";
$l['bad_word_desc'] = "Enter the word which you wish to be filtered. The '*' symbol represents any number of characters and the '+' symbol represents any single character (other than space and new line).";
$l['bad_word_max'] = "A filtered word can't be longer than 100 characters.";
$l['replacement'] = "Replacement";
$l['replacement_desc'] = "Enter the string which will replace the filtered word (If this is blank, asterisks will be shown).";
$l['regex'] = "Regular Expression";
$l['regex_desc'] = "Treat the \"Word\" field as a regular expression.";
$l['replacement_word_max'] = "A replacement word can't be longer than 100 characters.";
$l['error_replacement_word_invalid'] = "A replacement word cannot be equivalent to it's word filter.";

$l['save_bad_word'] = "Save Filter";
$l['no_bad_words'] = "There are no word filters currently set at this time.";
$l['add_bad_word'] = "Add a Filter";
$l['add_bad_word_desc'] = "Here you can add a word filter and its replacement.";

$l['error_missing_bad_word'] = "You did not enter a word filter.";
$l['error_invalid_regex'] = "The specified regular expression is invalid.";
$l['error_invalid_bid'] = "The specified filter does not exist.";
$l['error_bad_word_filtered'] = "The filter you entered already exists.";

$l['success_added_bad_word'] = "The filter has been added successfully.";
$l['success_deleted_bad_word'] = "The filter has been deleted successfully.";
$l['success_updated_bad_word'] = "The filter has been updated successfully.";

$l['confirm_bad_word_deletion'] = "Are you sure you wish to delete this filter?";

