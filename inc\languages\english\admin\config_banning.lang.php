<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['banning'] = "Banning";
$l['banned_ips'] = "Banned IPs";
$l['banned_ips_desc'] = "Here you can manage IP addresses which are banned from accessing your board.";
$l['banned_accounts'] = "Banned Accounts";
$l['disallowed_usernames'] = "Disallowed Usernames";
$l['disallowed_usernames_desc'] = "Here you manage a list of usernames which cannot be registered or used by users. This feature is also particularly useful for reserving usernames.";
$l['disallowed_email_addresses'] = "Disallowed Email Addresses";
$l['disallowed_email_addresses_desc'] = "Here you manage a list of email addresses which cannot be registered or used by users.";

$l['banned_ip_addresses'] = "Banned IP Addresses";
$l['username'] = "Username";
$l['date_disallowed'] = "Date Disallowed";
$l['last_attempted_use'] = "Last Attempted Use";
$l['email_address'] = "Email Address";
$l['ip_address'] = "IP Address";
$l['ban_date'] = "Ban Date";
$l['last_access'] = "Last Access";
$l['no_bans'] = "There are no bans currently set at this time.";
$l['add_disallowed_username'] = "Add a Disallowed Username";
$l['username_desc'] = "Note: To indicate a wild card match, use *";
$l['disallow_username'] = "Disallow Username";
$l['add_disallowed_email_address'] = "Add a Disallowed Email Address";
$l['email_address_desc'] = "Note: To indicate a wild card match, use *";
$l['disallow_email_address'] = "Disallow Email Address";
$l['ban_an_ip_address'] = "Ban an IP Address";
$l['ip_address_desc'] = "Note: To ban a range of IP addresses use * (Ex: 127.0.0.*) or CIDR notation (Ex: *********/8)";
$l['ban_ip_address'] = "Ban IP Address";

$l['error_missing_ban_input'] = "You did not enter a value to ban.";
$l['error_invalid_filter'] = "The specified filter does not exist.";
$l['error_filter_already_banned'] = "The filter you entered is already banned.";

$l['success_ip_banned'] = "The IP address has been banned successfully.";
$l['success_username_disallowed'] = "The username has been disallowed successfully.";
$l['success_email_disallowed'] = "The email address has been disallowed successfully.";
$l['success_ban_deleted'] = "The specified ban has been deleted successfully.";

$l['confirm_ban_deletion'] = "Are you sure you wish to delete this ban?";

