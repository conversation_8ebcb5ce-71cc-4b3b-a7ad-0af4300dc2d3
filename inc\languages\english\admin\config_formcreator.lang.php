<?php
$l['formcreator'] = "Form Creator";
$l['fc_view_forms'] = "View Forms";
$l['fc_view_forms_desc'] = "View all forms created for this website";
$l['fc_create_form'] = "Create New Form";
$l['fc_create_form_desc'] = "Create a new form for this website";
$l['fc_export'] = "Export";
$l['fc_export_desc'] = "Export forms for backuping or sharing";
$l['fc_import'] = "Import";
$l['fc_import_desc'] = "Import forms from a export code";
$l['fc_edit_form'] = "Edit Form";
$l['fc_delete_form'] = "Delete Form";
$l['fc_view_fields'] = "View Fields";
$l['fc_fields'] = "Fields";
$l['fc_change_template'] = "Change Output Template";
$l['fc_edit_form_desc'] = "Change the settings of the form";
$l['fc_output_template'] = "Form Output Template";
$l['fc_output_template_desc'] = "Change the output template for this form. Leave fields empty to use the default outputs";
$l['fc_yes'] = "Yes";
$l['fc_no'] = "No";
$l['fc_view_form_fields'] = "View Form Fields";
$l['fc_view_form_fields_desc'] = "Change the form fields. Add/Edit or Delete";
$l['fc_add_field'] = "Add Field";
$l['fc_add_field_desc'] = "Add a field";
$l['fc_edit_field'] = "Edit Field";
$l['fc_edit_field_desc'] = "Edit a field";
$l['fc_form_edit_not_found'] = "The form you tried to edit doesn't exist!";
$l['fc_empty_formname'] = "Form Name is empty!";
$l['fc_empty_allowed_groups_type'] = "The way allowed groups are handled wasn't set";
$l['fc_empty_allowed_groups'] = "There were no allowed groups selected!";
$l['fc_success_form_add'] = "The form is added succesfully. You can now configure fields.";
$l['fc_error_oops'] = "Oops something went wrong!";
$l['fc_success_form_edit'] = "The form is edited succesfully.";
$l['fc_form_del_not_found'] = "The form you are trying to delete doesn't exist";
$l['fc_success_form_delete'] = "The form was succesfully deleted";
$l['fc_confirm_form_delete'] = "Are you sure you would like to delete";
$l['fc_form_output_template'] = "Form Output Template";
$l['fc_form_output_not_found'] = "The form output you are trying to edit doesn't exist";
$l['fc_form_does_not_exist.'] = "The form you are looking for doesn't exist!";
$l['fc_subject_validation_failed'] = "Validation of the subject template failed!";
$l['fc_message_validation_failed'] = "Validation of the message template failed!";
$l['fc_output_template_success'] = "The output template has succesfully be changed";
$l['fc_form_no_fields'] = "This form doesn't have any fields yet. Please add fields before you change the output template.";
$l['fc_add_form'] = "Add Form";
$l['fc_create_new_form'] = "Create a new Form";
$l['fc_form_name'] = "Form Name";
$l['fc_form_name_desc'] = "The title of the form";
$l['fc_allow_all_groups'] = "Allow ALL groups";
$l['fc_allow_selected_groups'] = "Allow selected groups";
$l['fc_allow_unselected_groups'] = "Allow all BUT selected groups";
$l['fc_allowed_groups'] = "Allowed Groups";
$l['fc_allowed_groups_desc'] = "Which groups are allowed to use this form";
$l['fc_custom_denied_message'] = "Custom Denied Message";
$l['fc_custom_denied_message_desc'] = "Enter in a custom message users will get when they are not allowed access the form. Leave empty to use the default (HTML enabled)";
$l['fc_limitusage'] = "Usage limit";
$l['fc_limitusage_desc'] = "Enter the maximun number of times a user can fill in the form (0 = unlimited).";
$l['fc_status'] = "Status";
$l['fc_status_desc'] = "Is this form active yes or no?";
$l['fc_active'] = "Active";
$l['fc_process_options'] = "Process Options";
$l['fc_process_send_pm'] = "Send PM to user(s)";
$l['fc_process_send_pm_desc'] = "Send a PM to the User IDs defined here. If you do not want to trigger a PM leave this empty. Multiple users comma seperated.";
$l['fc_process_send_pm_group'] = "Send PM to Groups";
$l['fc_process_send_pm_group_desc'] = "Send a PM to the Users within the selected groups. If you do not want to trigger a group PM select nothing.";
$l['fc_process_post_thread'] = "Post within forum";
$l['fc_process_post_thread_desc'] = "Create a new thread within the selected forum";
$l['fc_disabled'] = "DISABLED";
$l['fc_none'] = "None";
$l['fc_process_prefix'] = "Thread prefix";
$l['fc_process_prefix_desc'] = "Select a thread prefix for the thread that will be made. Only has use when option for Post within forum is set.";
$l['fc_process_reply_post'] = "Post within thread";
$l['fc_process_reply_post_desc'] = "Create a post within the given thread ID";
$l['fc_process_post_as'] = "Post as user";
$l['fc_process_post_as_desc'] = "Which user is used to post a thread, post or reply. (leave empty to use the user who submits the form, set to -1 to use the Form Creator Bot as user)";
$l['fc_override_button'] = "Override post button";
$l['fc_override_button_desc'] = "Change the create new thread or post reply button to link to the form. Only useful when set to post a new thread or reply.";
$l['fc_show_summary'] = "Show Summary Page";
$l['fc_show_summary_desc'] = "Shows a summary page of the input before finally submitting the data.";
$l['fc_custom_summary_text'] = "Custom Summary Message";
$l['fc_custom_summary_text_desc'] = "Enter in a custom summary page message that you like to display at the top of the summary page. Will only be used when the summary page is enabled. Leave empty uses a default message (HTML is enabled)";
$l['fc_process_signature'] = "Post Signature";
$l['fc_process_signature_desc'] = "Set whether or not the users signature is posted within the thread or post.";
$l['fc_process_posticon'] = "Post Icon";
$l['fc_process_posticon_desc'] = "Select which post icon is used for the post / thread.";
$l['fc_custom_success_page'] = "Custom success page";
$l['fc_custom_success_page_desc'] = "Set an URL the user will be redirected to after completing the form.";
$l['fc_form_layout'] = "Form Layout";
$l['fc_form_talbe_width'] = "Form table width";
$l['fc_form_table_width_desc'] = "Set the width of the table containing the form (either in pixels or percentage, e.g. 100px or 75% )";
$l['fc_label_width'] = "Label column width";
$l['fc_label_width_desc'] = "Set the width of the table column containing the field labels (either in pixels or percentage, e.g. 100px or 75% )";
$l['fc_class'] = "Class";
$l['fc_class_desc'] = "Set the class of the table containing the form";
$l['fc_update_form'] = "Update Form";
$l['fc_create_form'] = "Create Form";
$l['fc_user_info'] = "User Info";
$l['fc_username'] = "Username";
$l['fc_other'] = "Other";
$l['fc_reference_number'] = "Reference Number";
$l['fc_id'] = "ID";
$l['fc_fieldname'] = "Field Name";
$l['fc_fieldvalue'] = "Field Value";
$l['fc_edit_output_template'] = "Edit Output Template";
$l['fc_subject_template'] = "Subject template";
$l['fc_subject_template_desc'] = "Please enter in the template string for the subject. Copy any variables from the template.";
$l['fc_message_template'] = "Message template";
$l['fc_message_template_desc'] = "Please enter in the template for the message. You can use MyCode and the variables by clicking the legend.";
$l['fc_add_variables'] = "Add Variables";
$l['fc_delete_field'] = "Delete Field";
$l['fc_edit_field'] = "Edit Field";
$l['fc_form_has_no_fields'] = "This form has no fields yet!";
$l['fc_field_name_empty'] = "Field name is empty";
$l['fc_options_empty'] = "There were no options entered";
$l['fc_html_empty'] = "HTML block can't be empty";
$l['fc_success_field_add'] = "The field is added succesfully";
$l['fc_success_field_edit'] = "The field is updated succesfully";
$l['fc_form_fields'] = "Form Fields";
$l['fc_add_field'] = "Add Field";
$l['fc_edit_field'] = "Edit Field";
$l['fc_add'] = "Add";
$l['fc_name'] = "Name";
$l['fc_field_name_desc'] = "Please enter a field name";
$l['fc_description'] = "Description";
$l['fc_field_description_desc'] = "Write a description for the field";
$l['fc_placeholder'] = "Placeholder";
$l['fc_field_placeholder_desc'] = "Write a placeholder for the short hint that is displayed in the input field before the user enters a value.";
$l['fc_maxlength'] = "Max length";
$l['fc_field_maxlength_desc'] = "Enter the maximum lenght of characters the user can enter. (0 = unlimited)";
$l['fc_options'] = "Options";
$l['fc_field_options_desc'] = "Please enter the options for the field. One option per line";
$l['fc_prefixes'] = "Available Prefixes";
$l['fc_no_prefixes'] = "- No prefixes availible -";
$l['fc_field_prefixes_desc'] = "Please select the prefixes available for the user. Make sure the prefixes are allowed for the specific thread or forum!";
$l['fc_format'] = "Format";
$l['fc_field_format_desc'] = "Please enter the format for the field (e.g. for dates use jQuery <a href='http://api.jqueryui.com/datepicker/#utility-formatDate'>dateformat</a>)";
$l['fc_default'] = "Default";
$l['fc_field_default_desc'] = "Enter the default value for this field";
$l['fc_required'] = "Required";
$l['fc_field_required_desc'] = "Select if the field is required to fill.";
$l['fc_regex'] = "Regex";
$l['fc_field_regex_desc'] = "Enter a Regex to check the entered value is to the requested format";
$l['fc_regex_error'] = "Regex Error Message";
$l['fc_field_regex_error_desc'] = "Enter the error message that should be shown when the regex fails.";
$l['fc_size'] = "Size";
$l['fc_field_size_desc'] = "Enter the size of the field";
$l['fc_cols'] = "Cols";
$l['fc_field_cols_desc'] = "Enter the size in cols of the field";
$l['fc_rows'] = "Rows";
$l['fc_field_rows_desc'] = "Enter the size in rows of the field";
$l['fc_resize'] = "Allow resize";
$l['fc_field_resize_desc'] = "Set whether or not the user can resize the field";
$l['fc_class'] = "Class";
$l['fc_field_class_desc'] = "Enter a class for the field container";
$l['fc_html_block'] = "HTML Block";
$l['fc_field_html_block_desc'] = "Enter the HTML code you would like to display";
$l['fc_field_type'] = "Field type";
$l['fc_field_example'] = "Field example";
$l['fc_field_type_desc'] = "Select what type of field you would like to add.";
$l['fc_button_update_field'] = "Update Field";
$l['fc_button_create_field'] = "Create Field";
$l['fc_add_field_unknown_form'] = "You are trying to add a field to a form that doesn't exist!";
$l['fc_delete_field_unknown'] = "The field you are trying to delete doesn't exist";
$l['fc_delete_field_form_unknown'] = "The field's form you are trying to delete doesn't exist";
$l['fc_delete_field_success'] = "The field was succesfully deleted";
$l['fc_confirmation_delete_field'] = "Are you sure you would like to delete";
$l['fc_save_order'] = "Save";
$l['fc_order_saved'] = "Order saved";
$l['fc_order'] = "Order";
$l['fc_type'] = "Type";
$l['fc_form_info'] = "Form Info";
$l['fc_send_mail_to'] = "Send Mail to";
$l['fc_no_mail_selected'] = "No mail selected";
$l['fc_export_form'] = "Export Forms";
$l['fc_export_description'] = "Copy and save this to a file or use this to import it else where.";
$l['fc_no_forms_to_export'] = "You have no forms that can be exported!";
$l['fc_forms'] = "Forms";
$l['fc_no_forms'] = "No forms";
$l['fc_field_forms_desc'] = "Which forms do you like to export?";
$l['fc_export_perms'] = "Export Permissions";
$l['fc_export_perms_desc'] = "Do you like to export the permissions? Set this to 'OFF' if you are going to import this on other forums.";
$l['fc_export_process_options'] = "Export Process Options";
$l['fc_export_process_options_desc'] = "Do you like to export the process options? Set this to 'OFF' if you are going to import this on other forums.";
$l['fc_export_usagelog'] = "Export Usage Log";
$l['fc_export_usagelog_desc'] = "Do you like to export the usage history? Set this to 'OFF' if you are going to import this on other forums.";
$l['fc_create_thread_in_forum'] = "Create Thread in Forum";
$l['fc_forum_doesnt_exist'] = "Forum doesn't exist";
$l['fc_no_forum_selected'] = "(No forum selected)";
$l['fc_send_pm_to_usergroups'] = "Send PM to Usergroups";
$l['fc_no_groups_selected'] = "(No groups selected)";
$l['fc_send_pm_to_users'] = "Send PM to Users";
$l['fc_no_users_selected'] = "(No users selected)";
$l['fc_import_form'] = "Import Forms";
$l['fc_import_code'] = "Import code";
$l['fc_import_code_desc'] = "Enter the import code";
$l['fc_no_forms_imported'] = "No forms found to import";
$l['fc_forms_imported'] = "Forms imported ({1} forms and {2} fields)";
$l['fc_url'] = "Link / URL";
$l['admin_log_config_formcreator_add'] = 'Created form #{1} ({2})';
$l['admin_log_config_formcreator_edit'] = 'Edited form #{1} ({2})';
$l['admin_log_config_formcreator_delete'] = 'Deleted form #{1} ({2})';
$l['admin_log_config_formcreator_addfield'] = 'Added field #{2} ({3}) in form #{1}';
$l['admin_log_config_formcreator_editfield'] = 'Edited field #{2} ({3}) in form #{1}';
$l['admin_log_config_formcreator_deletefield'] = 'Deleted field #{2} ({3}) in form #{1}';
$l['fc_summary_parsed'] = "Used parsed output as summary (preview output)?";
$l['fc_summary_parsed_desc'] = "Parse the data as the output used to create a thread, post or PM";
$l['fc_error_missing_template'] = "You are missing a template. Please deactivate the Form Creator and reactivate it to update the templates.";

?>