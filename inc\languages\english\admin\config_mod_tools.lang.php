<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['mod_tools'] = "Moderator Tools";

$l['thread_tools'] = "Thread Tools";
$l['thread_tools_desc'] = "Custom moderator tools allows you to create combinations of moderator actions that can be used on both threads and posts. These can then be used like the default tools when managing your forum. Here you can manage your custom thread tools.";

$l['add_thread_tool'] = "Add Thread Tool";
$l['add_new_thread_tool'] = "Add New Thread Tool";
$l['add_thread_tool_desc'] = "Here you can add a new custom thread moderation tool. This tool will be accessible from both inline thread moderation and from within threads themselves, listed with the default moderation tools.";

$l['post_tools'] = "Post Tools";
$l['post_tools_desc'] = "Custom moderator tools allows you to create combinations of moderator actions that can be used on both threads and posts. These can then be used like the default tools when managing your forum. Here you can manage your custom post tools.";

$l['add_post_tool'] = "Add Post Tool";
$l['add_new_post_tool'] = "Add New Post Tool";
$l['add_post_tool_desc'] = "Here you can add a new custom post moderation tool. This tool will be accessible from within threads themselves, listed with the default moderation tools.";

$l['edit_post_tool'] = "Edit Post Tool";
$l['edit_post_tool_desc'] = "Here you can edit the post tool's settings and actions.";
$l['edit_thread_tool'] = "Edit Thread Tool";
$l['edit_thread_tool_desc'] = "Here you can edit the thread tool's settings and actions.";

$l['no_thread_tools'] = "There are no thread tools setup on your forum.";
$l['no_post_tools'] = "There are no post tools setup on your forum.";

$l['confirm_thread_tool_deletion'] = "Are you sure you want to delete this thread tool?";
$l['confirm_post_tool_deletion'] = "Are you sure you want to delete this post tool?";

$l['success_post_tool_deleted'] = "The selected post moderation tool has been deleted successfully.";
$l['success_thread_tool_deleted'] = "The selected thread moderation tool has been deleted successfully.";

$l['error_invalid_post_tool'] = "The specified post tool does not exist.";
$l['error_invalid_thread_tool'] = "The specified thread tool does not exist.";

$l['general_options'] = "General Options";
$l['short_description'] = "Short Description";
$l['available_in_forums'] = "Available in forums";
$l['available_to_groups'] = "Available to groups";
$l['show_confirmation'] = "Show Confirmation Page";
$l['save_thread_tool'] = "Save Thread Tool";

$l['title'] = "Title";

$l['thread_moderation'] = "Thread Moderation";
$l['approve_unapprove'] = "Approve/Unapprove thread?";

$l['no_change'] = "No Change";
$l['approve'] = "Approve";
$l['unapprove'] = "Unapprove";
$l['stick'] = "Stick";
$l['unstick'] = "Unstick";
$l['open'] = "Open";
$l['close'] = "Close";
$l['stick'] = "Stick";
$l['unstick'] = "Unstick";
$l['toggle'] = "Toggle";
$l['days'] = "Days";
$l['no_prefix'] = "No Prefix";
$l['restore'] = "Restore";
$l['softdelete'] = "Soft delete";

$l['forum_to_move_to'] = "Forum to move to:";
$l['leave_redirect'] = "Leave redirect?";
$l['delete_redirect_after'] = "Delete redirect after";
$l['do_not_move_thread'] = "Do not move thread";
$l['do_not_copy_thread'] = "Do not copy thread";
$l['move_thread'] = "Move thread?";
$l['move_thread_desc'] = "If moving the thread(s), the \"delete redirect after&hellip; days\" is only to be filled in if a redirect will be left.";
$l['forum_to_copy_to'] = "Forum to copy to:";
$l['copy_thread'] = "Copy thread?";
$l['open_close_thread'] = "Open/close thread?";
$l['stick_unstick_thread'] = "Stick/unstick thread?";
$l['softdelete_restore_thread'] = "Soft delete/restore thread?";
$l['delete_thread'] = "Delete thread permanently?";
$l['merge_thread'] = "Merge thread?";
$l['merge_thread_desc'] = "Only if used in inline moderation.";
$l['delete_poll'] = "Delete poll?";
$l['delete_redirects'] = "Delete redirects?";
$l['remove_subscriptions'] = "Remove thread subscriptions?";
$l['recount_rebuild'] = "Recount & Rebuild?";
$l['apply_thread_prefix'] = "Apply thread prefix?";
$l['new_subject'] = "New subject?";
$l['new_subject_desc'] = "Enter the new subject for the thread.";

$l['subject_message_replacements'] = "Available replacements";
$l['subject_message_replacements_desc'] = "{subject} represents the original subject. {username} represents the moderator's username. {author} represents the thread author's username.";

$l['add_new_reply'] = "Add New Reply";
$l['add_new_reply_desc'] = "Leave blank for no reply.";
$l['reply_subject'] = "Reply subject.";
$l['reply_subject_desc'] = "Only used if a reply was made.";

$l['success_mod_tool_created'] = "The moderation tool has been created successfully.";
$l['success_mod_tool_updated'] = "The moderation tool has been updated successfully.";

$l['inline_post_moderation'] = "Inline Post Moderation";
$l['delete_posts'] = "Delete posts permanently?";
$l['merge_posts'] = "Merge posts?";
$l['merge_posts_desc'] = "Only if used from inline moderation.";
$l['approve_unapprove_posts'] = "Approve/unapprove posts?";
$l['softdelete_restore_posts'] = "Soft delete/restore posts?";

$l['split_posts'] = "Split Posts";
$l['split_posts2'] = "Split posts?";
$l['do_not_split'] = "Do not split posts";
$l['split_to_same_forum'] = "Split to same forum";
$l['close_split_thread'] = "Close split thread?";
$l['stick_split_thread'] = "Stick split thread?";
$l['unapprove_split_thread'] = "Unapprove split thread?";
$l['split_thread_prefix'] = "Split thread prefix";
$l['split_thread_subject'] = "Split thread subject";
$l['split_thread_subject_desc'] = "Only required if splitting posts.";
$l['add_new_split_reply'] = "Add reply to split thread";
$l['add_new_split_reply_desc'] = "Leave blank for no reply.";
$l['split_reply_subject'] = "Reply subject";
$l['split_reply_subject_desc'] = "Only used if a reply is made";
$l['save_post_tool'] = "Save Post Tool";

$l['send_private_message'] = 'Send Private Message';
$l['private_message_message'] = 'Message';
$l['private_message_message_desc'] = 'Message to send to the author of the thread. Leave empty to disable this feature.';
$l['private_message_subject'] = 'Subject';
$l['private_message_subject_desc'] = 'Enter the subject of the Private Message.';

$l['error_missing_title'] = "Please enter a name for this tool.";
$l['error_missing_description'] = "Please enter a short description for this tool.";
$l['error_no_forums_selected'] = "Please select the forums in which this tool will be available.";
$l['error_no_groups_selected'] = "Please select the groups to which this tool will be available.";
$l['error_forum_is_category'] = "You can't pick a category-type forum as a destination forum.";
