<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['custom_mycode'] = "Custom MyCode";
$l['mycode'] = "MyCode";
$l['mycode_desc'] = "Custom MyCode allows you to create additional MyCode for your members to use in their messages.";
$l['add_new_mycode'] = "Add New MyCode";
$l['add_new_mycode_desc'] = "Here you can add a new custom MyCode to your forum. You can use the MyCode Sandbox below the form to test your regular expression and replacement before saving.";
$l['edit_mycode_desc'] = "Here you can edit a custom MyCode. You can use the MyCode Sandbox below the form to test your regular expression and replacement before saving your changes.";

$l['title'] = "Title";
$l['short_description'] = "Short Description";
$l['regular_expression'] = "Regular Expression";
$l['regular_expression_desc'] = "Enter a regular expression that will search for a specific combination of characters.  You must make sure the regular expression is valid and safe&mdash;no validation is performed.";
$l['replacement'] = "Replacement";
$l['replacement_desc'] = "Enter a replacement for the regular expression.";
$l['example'] = "Example:";
$l['enabled'] = "Enabled?";
$l['parse_order'] = "Parse Order";
$l['parse_order_desc'] = "MyCodes will be parsed in ascending order relative to other MyCodes.";
$l['edit_mycode'] = "Edit MyCode";
$l['activate_mycode'] = "Activate MyCode";
$l['deactivate_mycode'] = "Deactivate MyCode";
$l['delete_mycode'] = "Delete MyCode";
$l['no_mycode'] = "No custom MyCodes exist at this time.";
$l['save_mycode'] = "Save MyCode";
$l['add_mycode'] = "Add MyCode";
$l['changes_not_saved'] = "Your changes have not yet been saved.";

$l['sandbox'] = "Sandbox";
$l['sandbox_desc'] = "You can use this area to test the regular expression and replacement above before saving your changes.";
$l['test_value'] = "Test Value";
$l['test_value_desc'] = "Enter in text to be tested in the box below.";
$l['result_html'] = "HTML Result";
$l['result_html_desc'] = "The text area below shows the resulting HTML using the regular expression on the test value.";
$l['result_actual'] = "Actual Result";
$l['result_actual_desc'] = "The area below shows the actual result when the HTML is rendered.";
$l['test'] = "Test MyCode";

$l['error_missing_title'] = "You did not enter a title.";
$l['error_missing_regex'] = "You did not enter a regular expression.";
$l['error_regex_already_available'] = "You entered a regular expression which is already defined in another MyCode. Please provide a different regular expression for this MyCode.";
$l['error_missing_replacement'] = "You did not enter a replacement.";
$l['error_invalid_mycode'] = "The specified MyCode does not exist.";

$l['success_added_mycode'] = "The MyCode has been added successfully.";
$l['success_deleted_mycode'] = "The selected MyCode has been deleted successfully.";
$l['success_updated_mycode'] = "The MyCode has been updated successfully.";
$l['success_deactivated_mycode'] = "The selected MyCode has been deactivated successfully.";
$l['success_activated_mycode'] = "The selected MyCode has been activated successfully.";

$l['confirm_mycode_deletion'] = "Are you sure you wish to delete this MyCode?";

$l['mycode_sandbox_test_error'] = 'There was an error fetching the test results.';

