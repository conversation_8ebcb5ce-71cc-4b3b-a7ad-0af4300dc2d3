<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['custom_profile_fields'] = "Custom Profile Fields";
$l['custom_profile_fields_desc'] = "This section allows you to edit, delete, and manage your custom profile fields.";
$l['add_profile_field'] = "Add Profile Field";
$l['add_new_profile_field'] = "Add New Profile Field";
$l['add_new_profile_field_desc'] = "Here you can add a new custom profile field.";
$l['edit_profile_field'] = "Edit Profile Field";
$l['edit_profile_field_desc'] = "Here you can edit a custom profile field.";

$l['title'] = "Title";
$l['short_description'] = "Short Description";
$l['maximum_length'] = "Maximum Length";
$l['maximum_length_desc'] = "This maximum number of characters that can be entered. This only applies to text boxes and text areas.";
$l['field_length'] = "Field Length";
$l['field_length_desc'] = "This length of the field. This only applies to single and multiple select boxes.";
$l['display_order'] = "Display Order";
$l['display_order_desc'] = "This is the order of custom profile fields in relation to other custom profile fields. This number should not be the same as another field.";
$l['text'] = "Textbox";
$l['textarea'] = "Textarea";
$l['select'] = "Select Box";
$l['multiselect'] = "Multiple Option Selection Box";
$l['radio'] = "Radio Buttons";
$l['checkbox'] = "Check Boxes";
$l['field_type'] = "Field Type";
$l['field_type_desc'] = "This is the field type that will be shown.";
$l['field_regex'] = "Regular Expression";
$l['field_regex_desc'] = "Enter a regular expression that should be matched from the user input. You must make sure the regular expression is valid and safe—no validation is performed. Empty for anything.<br />
<strong>Example:</strong> ([a-z0-9_\- ,.+]+)";
$l['selectable_options'] = "Selectable Options?";
$l['selectable_options_desc'] = "Please enter each option on a separate line. This only applies to the select boxes, check boxes, and radio buttons types.";
$l['required'] = "Required?";
$l['required_desc'] = "Is this field required to be filled in during registration or profile editing? Note that this does not apply if the field is hidden or the field is not editable.";
$l['show_on_registration'] = "Show on Registration?";
$l['show_on_registration_desc'] = "Should this field appear on the registration form? Note that this does not apply if the field is not editable. Fields that are required will always appear on registration.";
$l['display_on_profile'] = "Display on profile?";
$l['display_on_profile_desc'] = "Should this field be displayed on the user's profile?";
$l['display_on_postbit'] = "Display on postbit?";
$l['display_on_postbit_desc'] = "Should this field be displayed on the user's posts?";
$l['viewableby'] = 'Viewable By';
$l['viewableby_desc'] = 'Select the allowed groups to view this profile field.';
$l['editableby'] = 'Editable By';
$l['editableby_desc'] = 'Select the allowed groups to edit this profile field.';
$l['min_posts_enabled'] = "Minimum post count?";
$l['min_posts_enabled_desc'] = "Should this field only be available to users with a certain post count? If so, set the minimum amount of posts required here.";
$l['parser_options'] = "Parser Options";
$l['parse_allowhtml'] = "Yes, allow HTML in this profile field.";
$l['parse_allowmycode'] = "Yes, allow MyCode in this profile field.";
$l['parse_allowsmilies'] = "Yes, allow smilies in this profile field.";
$l['parse_allowimgcode'] = "Yes, allow [img] code in this profile field.";
$l['parse_allowvideocode'] = "Yes, allow [video] code in this profile field.";
$l['save_profile_field'] = "Save Profile Field";
$l['name'] = "Name";
$l['registration'] = "Registration?";
$l['editable'] = "Editable?";
$l['profile'] = "Profile?";
$l['postbit'] = "Postbit?";
$l['edit_field'] = "Edit Field";
$l['delete_field'] = "Delete Field";
$l['no_profile_fields'] = "There are no custom profile fields on your forum at this time.";

$l['error_missing_name'] = "You did not enter a title for this custom profile field";
$l['error_missing_description'] = "You did not enter a description for this custom profile field";
$l['error_invalid_fid'] = "The selected profile field does not exist.";

$l['success_profile_field_added'] = "The custom profile field has been created successfully.";
$l['success_profile_field_saved'] = "The custom profile field has been saved successfully.";
$l['success_profile_field_deleted'] = "The selected custom profile field has been deleted successfully.";

$l['confirm_profile_field_deletion'] = "Are you sure you wish to delete this profile field?";
