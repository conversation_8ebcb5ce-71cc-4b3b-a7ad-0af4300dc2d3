<?php
/**
 * English Language Pack
 * 
 * 
 * $Id: config_regsecureq.lang.php 14 2011-07-27 06:43:58Z - G33K - $
 */

$l['regsecureq'] = "Security Questions";
$l['regsecureq_desc'] = "List of security questions from where they will be randomly chosen during registration.";
$l['regsecureq_add'] = "Add Security Question";
$l['regsecureq_edit_q'] = "Edit Security Question";
$l['regsecureq_edit_desc'] = "Edit the question and/or the answer here.";
$l['regsecureq_add_desc'] = "Add a question and its expected answer for the security questions to be used during registration.";
$l['regsecureq_question'] = "Question";
$l['regsecureq_answer'] = "Answer";
$l['regsecureq_stats'] = "Stats";
$l['regsecureq_controls'] = "Controls";
$l['regsecureq_edit'] = "Edit";
$l['regsecureq_delete'] = "Delete";
$l['regsecureq_correct'] = "Correct: {1}%";
$l['regsecureq_incorrect'] = "Incorrect: {1}%";
$l['regsecureq_success_question_added'] = "Question has been successfully added to the list of security questions.";
$l['regsecureq_success_question_edited'] = "Question has been successfully edited.";
$l['regsecureq_missing_question'] = "Please enter the question.";
$l['regsecureq_missing_answer'] = "Please enter the answer.";
$l['regsecureq_question_long'] = "Question is too long. Limit it to 200 characters.";
$l['regsecureq_answer_long'] = "Answer is too long. Limit it to 100 characters.";
$l['regsecureq_error_deleting'] = "There was an error deleting the question.";
$l['regsecureq_confirm_delete'] = "Are you sure you want to delete this question?";
$l['regsecureq_success_question_deleted'] = "Question has been successfully deleted from the list of security questions.";

?>