<?php
$l['rineditor_restore'] = 'Restore';
$l['rineditor_plug_desc'] = 'Rin Editor (Powerd by CKEditor)';
$l['rineditor_sett_desc'] = 'Settings related to the Rin Editor.';
$l['rineditor_language_title'] = 'Language of Rin Editor';
$l['rineditor_language_desc'] = 'Set here language of Rin Editor. Ps. Use default if you want editor detect browser language.';
$l['rineditor_language_val'] = '=Default
af=af
ar=ar
bg=bg
bn=bn
bs=bs
ca=ca
cs=cs
cy=cy
da=da
de=de
el=el
en=en
en-au=en-au
en-ca=en-ca
en-gb=en-gb
eo=eo
es=es
et=et
eu=eu
fa=fa
fi=fi
fo=fo
fr=fr
fr-ca=fr-ca
gl=gl
gu=gu
he=he
hi=hi
hr=hr
hu=hu
id=id
is=is
it=it
ja=ja
ka=ka
km=km
ko=ko
ku=ku
lt=lt
lv=lv
mk=mk
mn=mn
ms=ms
nb=nb
nl=nl
no=no
pl=pl
pt=pt
pt-br=pt-br
ro=ro
ru=ru
si=si
sk=sk
sl=sl
sq=sq
sr=sr
sr-latn=sr-latn
sv=sv
th=th
tr=tr
tt=tt
ug=ug
uk=uk
vi=vi
zh=zh
zh-cn=zh-cn';
$l['rineditor_mobms_title'] = 'Source Mode in Mobile';
$l['rineditor_mobms_desc'] = 'Set to yes if you want load Rin Editor in Source Mode when using mobile device.';
$l['rineditor_enbquick_title'] = 'Show Rin Editor in quick reply and quick edit';
$l['rineditor_enbquick_desc'] = 'Set to yes if you want to show the Rin Editor in quick reply and quick edit.';
$l['rineditor_quickquote_title'] = 'Quick Quote Feature';
$l['rineditor_quickquote_desc'] = 'Set to no if you do not want enable quick quote feature.<br />Ps. This feature require Rin Editor in Quick Reply enabled in setting above.';
$l['rineditor_smile_title'] = 'Show Smile Box in Quick Reply';
$l['rineditor_smile_desc'] = 'Set to no if you do not want to show the smile box in quick reply.';
$l['rineditor_scsmiley_title'] = 'SCEditor style like Smile Box';
$l['rineditor_scsmiley_desc'] = 'Set to yes if you want to use Smile Box like SCEditor instead CKEditor style.';
$l['rineditor_autosave_title'] = 'Autosave Feature';
$l['rineditor_autosave_desc'] = 'Set to yes if you want disable autosave feature.';
$l['rineditor_autosavemsg_title'] = 'Autosaved Notification';
$l['rineditor_autosavemsg_desc'] = 'Set to yes if you want show autosaved notification.';
$l['rineditor_seltext_title'] = 'Do not replace selected text';
$l['rineditor_seltext_desc'] = 'Set to yes if you do not want that selected text is replaced when the custom button is triggered.<br /><strong>Ps.</strong> Enabling this function may result in some bugs.';
$l['rineditor_partial_title'] = 'Partial Mode';
$l['rineditor_partial_desc'] = 'Set to yes if you want enable Partial Mode feature.<br /><strong>Ps.</strong> This feature does not convert quote tag and code tag in WYSIWYG style like in Xenforo.';
$l['rineditor_heightf_title'] = 'Height of the editor in full mode place';
$l['rineditor_heightf_desc'] = 'Set the height of the editor in full mode place (new reply, new thread etc.) (value in px).';
$l['rineditor_heighto_title'] = 'Height of the editor in other place';
$l['rineditor_heighto_desc'] = 'Set the height of the editor in other place (quick reply, quick edit etc.) (value in px).';
$l['rineditor_buttonsf_title'] = 'Remove buttons from full mode place';
$l['rineditor_buttonsf_desc'] = 'Enter the button name. You must make sure the rule is valid and safe—no validation is performed. Separate buttons with "," but <strong>without space</strong>.<br /><strong>Example:</strong> Subscript,Superscript<br /><strong>Button list:</strong> Bold,Italic,Underline,Strike,Subscript,Superscript,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock,Font,FontSize,TextColor,removeFormat,HorizontalRule,<br />Image,Link,Unlink,Video,Smiley,Imgur,NumberedList,BulletedList,Blockquote,Code,PHP,SpecialChar,PasteText,PasteFromWord,Undo,Redo,Maximize,Source,BidiLtr,BidiRtl';
$l['rineditor_buttonso_title'] = 'Remove buttons from other place';
$l['rineditor_rulesf_title'] = 'Add new button in Clickable MyCode Editor in full mode place';
$l['rineditor_rules_desc'] = 'Enter the button name. You must make sure the rule is valid and safe—no validation is performed. Separate buttons with "," but <strong>without space</strong>.<br /><strong>Example:</strong> spoiler,test<br /><strong>Location to put image button:</strong> root/jscripts/rin/editor/icons (Dimension: 16x16 px, File name: Exactly same of button name, File extension: png.)';
$l['rineditor_rulesdesf_title'] = 'Add new button in Clickable MyCode Editor with Description in full mode place';
$l['rineditor_ruleso_title'] = 'Add new button in Clickable MyCode Editor in other place';
$l['rineditor_rulesdeso_title'] = 'Add new button in Clickable MyCode Editor with Description in other place';
$l['rineditor_imgur_title'] = 'Imgur';
$l['rineditor_imgur_desc'] = 'Set here API of imgur (Client ID).<br /><strong>Ps.</strong> You can get client id in https://imgur.com/register/api_anon (oauth2 without callback)';
?>