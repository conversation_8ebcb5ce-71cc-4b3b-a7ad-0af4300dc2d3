<?php

/********************************************************************************************************************************
*
*  Servers board (/inc/languages/english/admin/config_serversboard.lang.php)
*  Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> "Supry<PERSON>" Su<PERSON>rycz<PERSON>ński
*  Copyright: © 2013 - 2016 @ Krzysztof "Su<PERSON>ry<PERSON>" <PERSON><PERSON><PERSON> @ All rights reserved
*  
*  Website: 
*  Description: Show information about games online servers on index page and details about servers on subpage.
*
********************************************************************************************************************************/
/********************************************************************************************************************************
*
* This file is part of "Servers board" plugin for MyBB.
* Copyright © 2013 - 2016 @ Krzysztof "Supryk" Supryczyński @ All rights reserved
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU Lesser General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Lesser General Public License for more details.
*
* You should have received a copy of the GNU Lesser General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*
********************************************************************************************************************************/

$l['serversboard'] = "Servers board";
$l['serversboard_desc'] = "Show information about games online servers on index page and details about servers on subpage.";

$l['serversboard_upload_all_files'] = "Pleas upload all files.";

$l['setting_group_serversboard'] = "Servers board";
$l['setting_group_serversboard_desc'] = "Settings for plugin: Servers board.";

$l['setting_serversboard_onoff'] = "ON/OFF Servers board";
$l['setting_serversboard_onoff_desc'] = "Quick ON/OFF plugin.";

$l['setting_serversboard_index_onoff'] = "ON/OFF Servers board on index";
$l['setting_serversboard_index_onoff_desc'] = "Quick ON/OFF plugin on index.";

$l['setting_serversboard_portal_onoff'] = "ON/OFF Servers board on portal";
$l['setting_serversboard_portal_onoff'] = "Quick ON/OFF plugin on portal.";

$l['setting_serversboard_show_barsplayersnum_onoff'] = "Players count bar";
$l['setting_serversboard_show_barsplayersnum_onoff_desc'] = "";

$l['setting_serversboard_remove_host'] = "Enter the text that has to be invisible in the server name";
$l['setting_serversboard_remove_host_desc'] = "";

$l['setting_serversboard_summation_onoff'] = "Show summary?";
$l['setting_serversboard_summation_onoff_desc'] = "";

$l['setting_serversboard_cache_time'] = "Cache time in minutes";
$l['setting_serversboard_cache_time_desc'] = "";

$l['serversboard_templates'] = "Servers board -";

$l['serversboard_uninstall'] = "Servers board uninstallation";
$l['serversboard_uninstall_message'] = "Do you wish to drop all data from the database?";

$l['servers_list'] = "Servers board";
$l['servers_list_desc'] = "Menage servers board.";
$l['server_add'] = "Add server";
$l['server_add_desc'] = "Add new server to servers board.";
$l['server_add_success'] = "Server add success.";
$l['server_edit'] = "Edit server";
$l['server_edit_desc'] = "Edit server which is located in the table.";
$l['server_edit_error'] = "Error.";
$l['server_edit_success'] = "Edit success.";
$l['server_delete_error'] = "Error.";
$l['server_delete_success'] = "Remove success.";
$l['server_confirm_deletion'] = "You wont to remove server from table?";
$l['server_popup_confirm_deletion'] = "You wont to remove server from table?";
$l['servers_orders_updated_success'] = "Display order No.";

$l['server_ip'] = "IP";
$l['server_ip_desc'] = "Put server IP (like this.: *************:6350 ).";
$l['server_type'] = "Type";
$l['server_type_desc'] = "Select the type of server.";
$l['server_type_select'] = "Select server type";
$l['server_arma2qport'] = "Query port for Arma 2";
$l['server_arma2qport_desc'] = "Query port for Arma 2, default is join port + 1, eg. is join port is 2302 query port will be 2303";
$l['server_arma3qport'] = "Query port for Arma 3";
$l['server_arma3qport_desc'] = "Query port for Arma 3, default is join port + 1, eg. is join port is 2302 query port will be 2303";
$l['server_bf3qport'] = "Query port for Battlefield 3";
$l['server_bf3qport_desc'] = "Query port for Battlefield 3, default is join port + 22000, eg. is join port is 25200 query port will be 47200";
$l['server_bf4qport'] = "Query port for Battlefield 4";
$l['server_bf4qport_desc'] = "Query port for battlefield 4, default is join port + 22000, eg. is join port is 25200 query port will be 47200";
$l['server_dayzqport'] = "Query port for DayZ";
$l['server_dayzqport_desc'] = "Query port for DayZ, default is 27016";
$l['server_dayzmodqport'] = "Query port for DayZ Mod";
$l['server_dayzmodqport_desc'] = "Query port for DayZ Mod, default is 27017/2301 or + 1 to join port, eg. is join port is 2302 query port will be 2303";
$l['server_minecraftqport'] = "Query port/TCP for Minecraft";
$l['server_minecraftqport_desc'] = "Query port/TCP for Minecraft, default 25565";
$l['server_mtaqport'] = "Query port for Multi Theft Auto";
$l['server_mtaqport_desc'] = "Query port for Multi Theft Auto, default is join port + 123, eg. is join port is 22003 query port will be 22126";
$l['server_mumbleqport'] = "Query port for Mumble";
$l['server_mumbleqport_desc'] = "Query port for Mumble, required a Mumble server plugin http://www.gametracker.com/downloads/gtmurmurplugin.php";
$l['server_rustqport'] = "Query port for Rust";
$l['server_rustqport_desc'] = "Query port for Rust, default is join port + 1, eg. is join port is 28016 query port will be 28017";
$l['server_terrariaqport'] = "Query port for Terraria";
$l['server_terrariaqport_desc'] = "Query port for Terraria, default is join port + 101, eg. is join port is 7777 query port will be 7878";
$l['server_ts3qport'] = "Query port/TCP for Team Speak 3";
$l['server_ts3qport_desc'] = "Query port/TCP for Team Speak 3, default 10011";
$l['server_offlinehostname'] = "The server name when it is OFFLINE";
$l['server_offlinehostname_desc'] = "";
$l['server_cuthostname'] = "Shorten the name of the server (enter quantity) characters";
$l['server_cuthostname_desc'] = "";
$l['server_disporder'] = "Display order No.";
$l['server_disporder_desc'] = "";
$l['server_field'] = "Additional field";
$l['server_field_desc'] = "";
$l['server_field_link'] = "Additional field link.";
$l['server_field_link_desc'] = "HLTV link.";
$l['server_field_icon'] = "Additional field icon link";
$l['server_field_icon_desc'] = "Icon HLTV link.";
$l['server_forumid'] = "Select server forum";
$l['server_forumid_desc'] = "Server hostname will be linked to this forum.";
$l['server_forumid_none'] = "None";
$l['server_owner'] = "Enter the username of the server owner.";
$l['server_owner_desc'] = "";
$l['server_visible'] = "The server shown in table.";
$l['server_visible_desc'] = "If you turn this option to NO, the server will only be visible in the admin control panel .";
$l['server_new'] = "\"New\" The server is new or have new IP.";
$l['server_new_desc'] = "If you turn this option to YES and fill out the box below the text , the server will show the prefix appended with the text that you entered.";
$l['server_new_color'] = "Background color for prefix/option \"New\"";
$l['server_new_color_desc'] = "Enter the color for prefix/option \"New\" for example: #0f0f0f";
$l['server_new_text'] = "Text for the \"new\" prefix/option.";
$l['server_new_text_desc'] = "Enter the text to be shown when the option above is \"Yes\".";
$l['server_buddylist'] = "Server buddy list";
$l['server_buddylist_desc'] = "Menage list list of server buddy list. Put uid\'s members separate with commas. Do not leave the comma at the end of.";
$l['server_error_missing_ip'] = "Missing IP.";
$l['server_error_missing_type'] = "Missing Type.";
$l['server_error_missing_arma2qport'] = "Missing query port.";
$l['server_error_missing_arma3qport'] = "Missing query port.";
$l['server_error_missing_bf3qport'] = "Missing query port.";
$l['server_error_missing_bf4qport'] = "Missing query port.";
$l['server_error_missing_dayzqport'] = "Missing query port.";
$l['server_error_missing_dayzmodqport'] = "Missing query port.";
$l['server_error_missing_minecraftqport'] = "Missing query port.";
$l['server_error_missing_mtaqport'] = "Missing query port.";
$l['server_error_missing_mmbleqport'] = "Missing query port.";
$l['server_error_missing_terrariaqport'] = "Missing query port.";
$l['server_error_missing_ts3qport'] = "Missing query port/TCP.";
$l['server_error_missing_offlinehostname'] = "Missing OFFLINE server name.";
$l['server_error_missing_disporder'] = "Missing display order.";
$l['server_add_save'] = "Add server";
$l['server_edit_save'] = "Save";
$l['servers_name'] = "Server name";
$l['servers_status'] = "Status";
$l['servers_online'] = "Online";
$l['servers_offline'] = "Offline";
$l['servers_ip'] = "Server IP";
$l['servers_type'] = "Server type";
$l['servers_order'] = "Display order No.";
$l['servers_options'] = "Options";
$l['server_option_edit'] = "Edit";
$l['server_option_delete'] = "Delete";
$l['no_servers'] = "No server to display.";
$l['save_servers_order'] = "Save display order";

$l['serversboard_admin_permissions'] = "Can menage servers board?";
$l['admin_log_config_serversboard_edit'] = "Edited server in servers board #{1} ({2})";
$l['admin_log_config_serversboard_add'] = "Added server to servers board #{1} ({2})";
$l['admin_log_config_serversboard_delete'] = "Deleted server from servers board #{1} ({2})";
$l['admin_log_config_serversboard_update_order'] = "Updated servers display order in servers board";