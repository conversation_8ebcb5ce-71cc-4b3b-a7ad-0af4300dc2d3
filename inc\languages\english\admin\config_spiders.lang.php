<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['spiders_bots'] = "Spiders / Bots";
$l['spiders_bots_desc'] = "This section allows you to manage the search engine spiders &amp; bots automatically detected by your forum. You're also able to see when a particular bot last visited.";
$l['add_new_bot'] = "Add New Bot";
$l['add_new_bot_desc'] = "Here you can add a new bot that you would like to be detected by your forum.";

$l['edit_bot'] = "Edit Bot";
$l['edit_bot_desc'] = "Here you can edit an existing bot.";

$l['bot'] = "Bot";
$l['last_visit'] = "Last Visit";
$l['no_bots'] = "There are no search engine spiders or web crawlers being tracked by this forum.";

$l['name'] = "Name";
$l['name_desc'] = "Enter the name of this bot which you want to identify it by";
$l['user_agent'] = "User Agent String";
$l['user_agent_desc'] = "Enter the string which will be matched against the bots user agent (partial matches are accepted)";
$l['language_str'] = "Language";
$l['language_desc'] = "Select the language pack the bot will use when viewing the board.";
$l['theme'] = "Theme";
$l['theme_desc'] = "Select the theme the bot will use when viewing the board.";
$l['user_group'] = "User Group";
$l['user_group_desc'] = "Select the user group permissions will be applied from for this board (Note: It is not recommended you change this from the default Guests group)";
$l['save_bot'] = "Save Bot";
$l['use_board_default'] = "Use Board Default";

$l['error_invalid_bot'] = "The specified bot does not exist.";
$l['error_missing_name'] = "You did not enter a name for this bot";
$l['error_missing_agent'] = "You did not enter a user agent string for this bot";

$l['success_bot_created'] = "The bot has been created successfully.";
$l['success_bot_updated'] = "The bot has been updated successfully.";
$l['success_bot_deleted'] = "The selected bot has been deleted successfully.";

$l['confirm_bot_deletion'] = "Are you sure you wish to delete this bot?";

