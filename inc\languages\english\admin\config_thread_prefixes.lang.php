<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['thread_prefixes'] = "Thread Prefixes";
$l['thread_prefixes_in'] = "Thread Prefixes in \"{1}\"";
$l['thread_prefixes_desc'] = "Thread prefixes allow you to define prefixes that users can assign to their threads. Threads can then be filtered by prefix within a forum.";

$l['add_new_thread_prefix'] = "Add New Thread Prefix";
$l['add_new_thread_prefix_desc'] = "Here you can add a new thread prefix and define where it is available, as well as which usergroups can use it.";

$l['edit_prefix'] = "Edit Prefix";
$l['edit_prefix_desc'] = "Here you can edit a thread prefix and change how it is displayed, where it is available and which usergroups can use it.";
$l['edit_thread_prefix'] = "Edit Thread Prefix";
$l['delete_thread_prefix'] = "Delete Thread Prefix";

$l['prefix_options'] = "Prefix Options";
$l['save_thread_prefix'] = "Save Thread Prefix";

$l['prefix'] = "Prefix";
$l['forums'] = "Available in Forums";
$l['prefix_desc'] = "Plain text version of the prefix to be displayed in selection menus.";
$l['display_style'] = "Display Style";
$l['display_style_desc'] = "This is how the prefix will appear next to thread subjects. You may enter HTML markup here or simply use the same as the plain text version above.";
$l['available_in_forums'] = "Available in forums";
$l['available_to_groups'] = "Available to groups";

$l['no_thread_prefixes'] = "There are no thread prefixes setup.";

$l['confirm_thread_prefix_deletion'] = "Are you sure you want to delete this thread prefix? Note: You should remember to update any custom moderator tools which use this prefix after deletion.";

$l['success_thread_prefix_created'] = "The thread prefix has been created successfully.";
$l['success_thread_prefix_updated'] = "The thread prefix has been updated successfully.";
$l['success_thread_prefix_deleted'] = "The thread prefix has been deleted successfully. Please update any custom moderator tools which use this prefix";

$l['error_missing_prefix'] = "Please enter the prefix you wish to add.";
$l['error_missing_display_style'] = "Please enter a display style for this prefix.";
$l['error_no_forums_selected'] = "Please select the forums in which this prefix will be available.";
$l['error_no_groups_selected'] = "Please select the groups to which this prefix will be available.";
$l['error_invalid_prefix'] = "The specified thread prefix does not exist.";

