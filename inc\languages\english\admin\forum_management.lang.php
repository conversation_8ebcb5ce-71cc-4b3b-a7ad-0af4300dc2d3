<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['forum_management'] = "Forum Management";
$l['forum_management_desc'] = "This section allows you to manage the categories and forums on your board. You can manage forum permissions and forum-specific moderators as well. If you change the display order for one or more forums or categories, make sure you submit the form at the bottom of the page.";
$l['add_forum'] = "Add New Forum";
$l['add_forum_desc'] = "Here you can add a new forum or category to your board. You may also set initial permissions for this forum.";
$l['copy_forum'] = "Copy Forum";
$l['copy_forum_desc'] = "Here you can copy forum settings or permissions from an existing forum to another or to a new forum.";
$l['forum_permissions'] = "Permissions";
$l['forum_permissions_desc'] = "Here you can modify the full permissions for an individual group for a single forum";
$l['view_forum'] = "View Forum";
$l['view_forum_desc'] = "Here you can view sub forums, quickly edit permissions and add moderators to your forum.";
$l['add_child_forum'] = "Add Child Forum";
$l['edit_forum_settings'] = "Edit Forum Settings";
$l['edit_forum_settings_desc'] = "Here you can edit an existing forums' settings and its permissions.";
$l['edit_forum'] = "Edit Forum";
$l['edit_mod'] = "Edit Moderator";
$l['edit_mod_desc'] = "Here you can modify a particular moderator's settings.";
$l['forum_moderators'] = "Forum Moderators";
$l['forum_permissions2'] = "Forum Permissions";
$l['more_subforums'] = "and {1} more.";

$l['manage_forums'] = "Manage Forums";
$l['forum'] = "Forum";
$l['order'] = "Order";

$l['subforums'] = "Sub Forums";
$l['moderators'] = "Moderators";
$l['forum_thread_prefixes'] = "Thread Prefixes";
$l['permissions'] = "Permissions";
$l['delete_forum'] = "Delete Forum";

$l['sub_forums'] = "Sub Forums";
$l['update_forum_orders'] = "Save Forum Orders";
$l['update_forum_permissions'] = "Save Forum Permissions";
$l['reset'] = "Reset";
$l['in_forums'] = "Forums in \"{1}\"";
$l['forum_permissions_in'] = "Forum Permissions in \"{1}\"";
$l['moderators_assigned_to'] = "Moderators Assigned to \"{1}\"";
$l['edit_permissions'] = "Edit Permissions";
$l['set_permissions'] = "Set Permissions";
$l['using_custom_perms'] = "Using Custom Permissions";
$l['using_default_perms'] = "Using Default Permissions";
$l['clear_custom_perms'] = "Clear Custom Permissions";
$l['set_custom_perms'] = "Set Custom Permissions";

$l['permissions_use_group_default'] = "Use Group Default";
$l['permissions_group'] = "Group";
$l['permissions_user'] = "User";
$l['permissions_canview'] = "Can view?";
$l['permissions_canpostthreads'] = "Can post threads?";
$l['permissions_canpostreplys'] = "Can post replies?";
$l['permissions_canpostpolls'] = "Can post polls?";
$l['permissions_canuploadattachments'] = "Can upload attachments?";
$l['permissions_all'] = "All?";

$l['overview_allowed_actions'] = "Overview: Allowed Actions";
$l['overview_disallowed_actions'] = "Overview: Disallowed Actions";
$l['perm_drag_canview'] = "&#149; View";
$l['perm_drag_canpostthreads'] = "&#149; Post Threads";
$l['perm_drag_canpostreplys'] = "&#149; Post Replies";
$l['perm_drag_canpostpolls'] = "&#149; Post Polls";

$l['moderator_permissions'] = "Moderator Permissions";
$l['forum_desc'] = "Forum the moderator manages.";
$l['edit_mod_for'] = "Edit moderator options for \"{1}\"";
$l['can_edit_posts'] = "Can edit posts?";
$l['can_soft_delete_posts'] = "Can soft delete posts?";
$l['can_restore_posts'] = "Can restore soft deleted posts?";
$l['can_delete_posts'] = "Can delete posts permanently?";
$l['can_soft_delete_threads'] = "Can soft delete threads?";
$l['can_restore_threads'] = "Can restore soft deleted threads?";
$l['can_delete_threads'] = "Can delete threads permanently?";
$l['can_view_ips'] = "Can view IPs?";
$l['can_view_unapprove'] = "Can view unapproved threads and posts?";
$l['can_view_deleted'] = "Can view deleted threads and posts?";
$l['can_open_close_threads'] = "Can open/close threads?";
$l['can_stick_unstick_threads'] = "Can stick/unstick threads?";
$l['can_approve_unapprove_threads'] = "Can approve/unapprove threads?";
$l['can_approve_unapprove_posts'] = "Can approve/unapprove posts?";
$l['can_approve_unapprove_attachments'] = "Can approve/unapprove attachments?";
$l['can_manage_threads'] = "Can manage threads (split, move, copy, merge)?";
$l['can_manage_polls'] = "Can manage polls?";
$l['can_post_closed_threads'] = "Can post in closed threads?";
$l['can_move_to_other_forums'] = "Can move threads to another forum this user doesn't moderate?";
$l['can_use_custom_tools'] = "Can use custom moderator tools?";
$l['can_manage_announcements'] = "Can manage announcements in this forum?";
$l['can_manage_reported_posts'] = "Can manage reported posts in this forum?";
$l['can_view_mod_log'] = "Can view moderator log entries for this forum?";
$l['moderator_cp_permissions'] = "Moderator CP Permissions";
$l['moderator_cp_permissions_desc'] = "This user must be able to access the Mod CP and have usergroup permission to access these functions in order for these permissions to take effect.";

$l['save_mod'] = "Save Moderator";

$l['no_forums'] = "There are no forums found.";
$l['no_moderators'] = "There are no moderators found.";

$l['success_forum_disporder_updated'] = "The forum display orders have been updated successfully.";
$l['success_forum_deleted'] = "The selected forum has been deleted successfully. Ideally you should now run the <a href=\"index.php?module=tools-recount_rebuild\">Recount &amp; Rebuild</a> tools.";
$l['success_moderator_deleted'] = "The selected moderator has been deleted successfully.<br />Please remember that this hasn't changed this user's group permission, they may still have moderation powers.";
$l['success_forum_permissions_updated'] = "The forum permissions have been updated successfully.";
$l['success_forum_updated'] = "The forum settings have been updated successfully.";
$l['success_moderator_updated'] = "The selected moderator has been updated successfully.";
$l['success_custom_permission_cleared'] = "The custom permissions for this forum have been cleared successfully.";

$l['error_invalid_forum'] = "Please select a valid forum.";
$l['error_invalid_moderator'] = "Please select a valid moderator to delete.";
$l['error_invalid_fid'] = "Invalid Forum ID selected.";
$l['error_forum_parent_child'] = "You can't set the parent forum of this forum to one of it's children.";
$l['error_forum_parent_itself'] = "The forum parent cannot be the forum itself.";
$l['error_incorrect_moderator'] = "Please select a valid moderator.";

$l['confirm_moderator_deletion'] = "Are you sure you wish to remove this moderator from this forum?";
$l['confirm_forum_deletion'] = "Are you sure you wish to delete this forum?";
$l['confirm_clear_custom_permission'] = "Are you sure you wish to clear this custom permission?";

$l['forum_type'] = "Forum Type";
$l['forum_type_desc'] = "Select the type of forum you are creating - a forum you can post in, or a category, which contains other forums.";
$l['category'] = "Category";
$l['title'] = "Title";
$l['description'] = "Description";
$l['save_forum'] = "Save Forum";
$l['parent_forum'] = "Parent Forum";
$l['parent_forum_desc'] = "The Forum that contains this forum. Categories do not have a parent forum - in this case, select 'None' - however, categories can be specified to have a parent forum.";
$l['none'] = "None";
$l['display_order'] = "Display Order";

$l['show_additional_options'] = "Show Additional Options";
$l['hide_additional_options'] = "Hide Additional Options";
$l['additional_forum_options'] = "Additional Forum Options";
$l['forum_link'] = "Forum Link";
$l['forum_link_desc'] = "To make a forum redirect to another location, enter the URL to the destination you wish to redirect to. Entering a URL in this field will remove the forum functionality; however, permissions can still be set for it.";
$l['forum_password'] = "Forum Password";
$l['forum_password_desc'] = "To protect this forum further, you can choose a password that must be entered for access. Note: User groups still need permissions to access this forum.";
$l['access_options'] = "Access Options";
$l['forum_is_active'] = "Forum is Active?";
$l['forum_is_active_desc'] = "If unselected, this forum will not be shown to users and will not \"exist\".";
$l['forum_is_open'] = "Forum is Open?";
$l['forum_is_open_desc'] = "If unselected, users will not be able to post in this forum regardless of permissions.";

$l['copy_to_new_forum'] = "Copy to new forum";
$l['source_forum'] = "Source forum";
$l['source_forum_desc'] = "Forum to copy settings and/or permissions from.";
$l['destination_forum'] = "Destination forum";
$l['destination_forum_desc'] = "Forum to copy settings and/or permissions to.";
$l['new_forum_settings'] = "New Forum Settings";
$l['copy_settings_and_properties'] = "Copy Forum Settings and Properties";
$l['copy_settings_and_properties_desc'] = "Only applies if the destination forum exists.";
$l['copy_user_group_permissions'] = "Copy User Group Permissions";
$l['copy_user_group_permissions_desc'] = "Use CTRL to select multiple groups.";

$l['override_user_style'] = "Yes, override the user's selected style for this forum";
$l['style_options'] = "Style Options";
$l['forum_specific_style'] = "Forum-Specific Style:";
$l['use_default'] = "Use Default";
$l['dont_display_rules'] = "Don't display rules for this forum";
$l['display_rules_inline'] = "Display rules for this forum on the thread listing";
$l['display_rules_inline_new'] = "Display rules in the thread listing and for new threads/replies";
$l['display_rules_link'] = "Display a link to the rules for this forum";
$l['display_method'] = "Display Method:";
$l['rules'] = "Rules:";
$l['forum_rules'] = "Forum Rules";
$l['name'] = "Name";
$l['username'] = "Username";
$l['moderator_username_desc'] = "Username of the moderator to be added";
$l['add_user_as_moderator'] = "Add a user as Moderator";
$l['usergroup'] = "Usergroup";
$l['add_usergroup_as_moderator'] = "Add a usergroup as Moderators";
$l['moderator_usergroup_desc'] = "Select a usergroup to add as a Moderator from the list below.";
$l['add_usergroup_moderator'] = "Add Usergroup Moderator";
$l['add_user_moderator'] = "Add User Moderator";

$l['default_view_options'] = "Default View Options";
$l['default_date_cut'] = "Default Date Cut:";
$l['default_sort_by'] = "Default Sort By:";
$l['default_sort_order'] = "Default Sort Order:";

$l['board_default'] = "Board Default";

$l['datelimit_1day'] = "Last day";
$l['datelimit_5days'] = "Last 5 days";
$l['datelimit_10days'] = "Last 10 days";
$l['datelimit_20days'] = "Last 20 days";
$l['datelimit_50days'] = "Last 50 days";
$l['datelimit_75days'] = "Last 75 days";
$l['datelimit_100days'] = "Last 100 days";
$l['datelimit_lastyear'] = "Last year";
$l['datelimit_beginning'] = "The beginning";

$l['sort_by_subject'] = "Thread subject";
$l['sort_by_lastpost'] = "Last post time";
$l['sort_by_starter'] = "Thread starter";
$l['sort_by_started'] = "Thread creation time";
$l['sort_by_rating'] = "Thread rating";
$l['sort_by_replies'] = "Number of replies";
$l['sort_by_views'] = "Number of views";

$l['sort_order_asc'] = "Ascending";
$l['sort_order_desc'] = "Descending";

$l['misc_options'] = "Miscellaneous Options";
$l['allow_html'] = "Yes, allow HTML in posts";
$l['allow_mycode'] = "Yes, allow MyCode in posts";
$l['allow_smilies'] = "Yes, allow smilies in posts";
$l['allow_img_code'] = "Yes, allow [img] code in posts (requires MyCode to be turned on)";
$l['allow_video_code'] = "Yes, allow [video] code in posts (requires MyCode to be turned on)";
$l['allow_post_icons'] = "Yes, allow post icons to be chosen for posts";
$l['allow_thread_ratings'] = "Yes, allow threads to be rated";
$l['show_forum_jump'] = "Yes, show this forum in the 'forum jump' menu";
$l['use_postcounts'] = "Yes, posts in this forum should count towards user post counts";
$l['use_threadcounts'] = "Yes, threads in this forum should count towards user thread counts";
$l['require_thread_prefix'] = "Yes, require a thread prefix for all threads";

$l['use_permissions'] = "Use Permissions";
$l['use_permissions_desc'] = "Select the permissions you would like to use for this user group - inherited permissions (will delete custom permissions) or custom permissions.";
$l['inherit_permissions'] = "Use user group permissions or inherit permissions from parent forums";
$l['custom_permissions'] = "Use custom permissions (below)";
$l['custom_permissions_for'] = "Custom Permissions for";

$l['inherited_permission'] = "inherited";
$l['custom_permission'] = "custom";

$l['save_permissions'] = "Save Forum Permissions";

$l['error_missing_title'] = "You must enter in a title.";
$l['error_no_parent'] = "You must select a parent forum.";
$l['error_not_empty'] = "Forums with threads cannot be converted to categories.";
$l['error_forum_link_not_empty'] = "Forums with threads cannot be redirected to another webpage.";

$l['success_forum_added'] = "The forum has been created successfully.";
$l['success_moderator_added'] = "The moderator has been added to this forum successfully.";
$l['success_forum_permissions_saved'] = "The forum permissions have been saved successfully.";
$l['success_forum_copied'] = "The selected forum has been copied successfully.";

$l['error_moderator_already_added'] = "The selected user/group is already a moderator of this forum.";
$l['error_moderator_not_found'] = "The specified username/group was not found.";
$l['error_new_forum_needs_name'] = "You need to give your new forum a name.";
$l['error_invalid_source_forum'] = "Invalid source forum.";
$l['error_invalid_destination_forum'] = "Invalid destination forum.";

$l['group_viewing'] = "Viewing";
$l['group_posting_rating'] = "Posting / Rating";
$l['group_editing'] = "Editing";
$l['group_moderate'] = "Moderation";
$l['group_polls'] = "Polls";
$l['group_misc'] = "Miscellaneous";

$l['viewing_field_canview'] = "Can view forum?";
$l['viewing_field_canviewthreads'] = "Can view threads within forum?";
$l['viewing_field_canonlyviewownthreads'] = "Can only view own threads?";
$l['viewing_field_candlattachments'] = "Can download attachments?";

$l['posting_rating_field_canpostthreads'] = "Can post threads?";
$l['posting_rating_field_canpostreplys'] = "Can post replies?";
$l['posting_rating_field_canonlyreplyownthreads'] = "Can only reply to own threads?";
$l['posting_rating_field_canpostattachments'] = "Can post attachments?";
$l['posting_rating_field_canratethreads'] = "Can rate threads?";

$l['editing_field_caneditposts'] = "Can edit own posts?";
$l['editing_field_candeleteposts'] = "Can delete own posts?";
$l['editing_field_candeletethreads'] = "Can delete own threads?";
$l['editing_field_caneditattachments'] = "Can update own attachments?";
$l['editing_field_canviewdeletionnotice'] = "Can view deletion notices?";

$l['moderate_field_modposts'] = "Moderate new posts?";
$l['moderate_field_modthreads'] = "Moderate new threads?";
$l['moderate_field_modattachments'] = "Moderate new attachments?";
$l['moderate_field_mod_edit_posts'] = "Moderate posts after they've been edited?";

$l['polls_field_canpostpolls'] = "Can post polls?";
$l['polls_field_canvotepolls'] = "Can vote in polls?";

$l['misc_field_cansearch'] = "Can search forum?";

$l['confirm_proceed_deletion'] = "Click \"Proceed\" to continue the deletion of the forum.";
$l['automatically_redirecting'] = "Automatically Redirecting&hellip;";
