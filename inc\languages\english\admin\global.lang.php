<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['today_rel'] = "<span title=\"{1}\">Today</span>";
$l['yesterday_rel'] = "<span title=\"{1}\">Yesterday</span>";
$l['today'] = "Today";
$l['yesterday'] = "Yesterday";

$l['size_yb'] = "YB";
$l['size_zb'] = "ZB";
$l['size_eb'] = "EB";
$l['size_pb'] = "PB";
$l['size_tb'] = "TB";
$l['size_gb'] = "GB";
$l['size_mb'] = "MB";
$l['size_kb'] = "KB";
$l['size_bytes'] = "bytes";
$l['na'] = "N/A";

// Header language strings
$l['mybb_admin_panel'] = "MyBB Control Panel";
$l['mybb_admin_cp'] = "MyBB Admin CP";
$l['logged_in_as'] = "Logged in as";
$l['view_board'] = "View Forum";
$l['logout'] = "Log Out";

// Footer language strings
$l['generated_in'] = "Generated in {1} with <a href=\"{2}\" target=\"_blank\">{3} queries</a>. Memory Usage: {4}";

// Login page
$l['enter_username_and_password'] = "Please enter your {1} and password to continue.";
$l['login_username'] = 'username';
$l['login_email'] = 'email';
$l['login_username_and_password'] = 'username/email';
$l['mybb_admin_login'] = "MyBB Control Panel - Login";
$l['return_to_forum'] = "Return to forum";
$l['please_login'] = "Please Login";
$l['username'] = "Username:";
$l['username1'] = "Email:";
$l['username2'] = "Username/Email:";
$l['password'] = "Password:";
$l['secret_pin'] = "Secret PIN:";
$l['login'] = "Login";
$l['lost_password'] = "Forgot your password?";

$l['error_invalid_admin_session'] = "Invalid administration session.";
$l['error_admin_session_expired'] = "Your administration session has expired.";
$l['error_invalid_ip'] = "Your IP address is not valid for this session.";
$l['error_mybb_admin_lockedout'] = "This account has been locked out.";
$l['error_mybb_admin_lockedout_message'] = "Your account is currently locked out after failing to login {1} times. You have been sent an email with instructions on how to unlock your account.";
$l['error_mybb_not_admin_account'] = "You do not have permission to access the administration control panel.";

$l['error_invalid_username'] = "The username you entered is invalid.";
$l['error_invalid_uid'] = "The user id you entered is invalid.";
$l['error_invalid_token'] = "The activation code you entered is invalid.";

$l['success_logged_out'] = "You have been logged out successfully.";
$l['error_invalid_username_password'] = "The {1} and password combination you entered is invalid.";

// Action Confirmation
$l['confirm_action'] = "Are you sure you wish to perform this action?";

// Common words and phrases
$l['home'] = "Home";
$l['name'] = "Name";
$l['size'] = "Size";
$l['controls'] = "Controls";
$l['view'] = "View";
$l['yes'] = "Yes";
$l['no'] = "No";
$l['cancel'] = "Cancel";
$l['options'] = "Options";
$l['proceed'] = "Proceed";
$l['ok'] = "OK";
$l['error'] = "Error";
$l['edit'] = "Edit";
$l['never'] = "Never";
$l['legend'] = "Legend";
$l['version'] = "Version";
$l['languagevar'] = "Language";
$l['use_default'] = "Use Default";
$l['file'] = "File";
$l['go'] = "Go";
$l['clear'] = "Clear";
$l['unknown'] = "Unknown";
$l['year'] = "Year";
$l['year_short'] = "y";
$l['years'] = "Years";
$l['years_short'] = "y";
$l['month'] = "Month";
$l['month_short'] = "m";
$l['months'] = "Months";
$l['months_short'] = "m";
$l['week'] = "Week";
$l['week_short'] = "w";
$l['weeks'] = "Weeks";
$l['weeks_short'] = "w";
$l['day'] = "Day";
$l['day_short'] = "d";
$l['days'] = "Days";
$l['days_short'] = "d";
$l['hour'] = "Hour";
$l['hour_short'] = "h";
$l['hours'] = "Hours";
$l['hours_short'] = "h";
$l['minute'] = "Minute";
$l['minute_short'] = "m";
$l['minutes'] = "Minutes";
$l['minutes_short'] = "m";
$l['second'] = "Second";
$l['second_short'] = "s";
$l['seconds'] = "Seconds";
$l['seconds_short'] = "s";
$l['permanent'] = "Permanent";
$l['all_forums'] = "All Forums";
$l['all_groups'] = "All groups";
$l['all_prefix'] = "All prefixes";
$l['select_forums'] = "Select forums";
$l['select_groups'] = "Select groups";
$l['select_prefix'] = "Select prefixes";
$l['forums_colon'] = "Forums:";
$l['groups_colon'] = "Groups:";
$l['prefix_colon'] = "Prefixes:";
$l['none'] = "None";
$l['mybb_acp'] = "MyBB ACP";
$l['pages'] = "Pages";
$l['previous'] = "Previous";
$l['page'] = "Page";
$l['next'] = "Next";
$l['delete'] = "Delete";
$l['reset'] = "Reset";
$l['and'] = "and";
$l['on'] = "On";
$l['off'] = "Off";
$l['alt_enabled'] = "Enabled";
$l['alt_disabled'] = "Disabled";
$l['enable'] = "Enable";
$l['disable'] = "Disable";
$l['saved'] = 'Saved';
$l['guest'] = 'Guest';

$l['rel_in'] = "In ";
$l['rel_ago'] = "ago";
$l['rel_less_than'] = "Less than ";
$l['rel_time'] = "<span title=\"{5}{6}\">{1}{2} {3} {4}</span>";
$l['rel_minutes_single'] = "minute";
$l['rel_minutes_plural'] = "minutes";
$l['rel_hours_single'] = "hour";
$l['rel_hours_plural'] = "hours";

// Parser bits
$l['quote'] = "Quote:";
$l['wrote'] = "Wrote:";
$l['code'] = "Code:";
$l['php_code'] = "PHP Code:";
$l['linkback'] = "Original Post";

// The months of the year
$l['january'] = "January";
$l['february'] = "February";
$l['march'] = "March";
$l['april'] = "April";
$l['may'] = "May";
$l['june'] = "June";
$l['july'] = "July";
$l['august'] = "August";
$l['september'] = "September";
$l['october'] = "October";
$l['november'] = "November";
$l['december'] = "December";

// Access Denied
$l['access_denied'] = "Access Denied";
$l['access_denied_desc'] = "You do not have permission to access this part of the administration control panel.";

// Super Administrator required
$l['cannot_perform_action_super_admin_general'] = "Sorry, but you cannot perform this action because you are not a super administrator.<br /><br />To be able to perform this action, you need to add your user ID to the list of super administrators in inc/config.php.";

// AJAX
$l['loading_text'] = "Loading<br />Please wait&hellip;";

// Time zone selection boxes
$l['timezone_gmt_minus_1200'] = "(GMT -12:00) Howland and Baker Islands";
$l['timezone_gmt_minus_1100'] = "(GMT -11:00) Nome, Midway Island";
$l['timezone_gmt_minus_1000'] = "(GMT -10:00) Hawaii, Papeete";
$l['timezone_gmt_minus_950'] = "(GMT -9:30) Marquesas Islands";
$l['timezone_gmt_minus_900'] = "(GMT -9:00) Alaska";
$l['timezone_gmt_minus_800'] = "(GMT -8:00) Pacific Time";
$l['timezone_gmt_minus_700'] = "(GMT -7:00) Mountain Time";
$l['timezone_gmt_minus_600'] = "(GMT -6:00) Central Time, Mexico City";
$l['timezone_gmt_minus_500'] = "(GMT -5:00) Eastern Time, Bogota, Lima, Quito";
$l['timezone_gmt_minus_450'] = "(GMT -4:30) Caracas";
$l['timezone_gmt_minus_400'] = "(GMT -4:00) Atlantic Time, La Paz, Halifax";
$l['timezone_gmt_minus_350'] = "(GMT -3:30) Newfoundland";
$l['timezone_gmt_minus_300'] = "(GMT -3:00) Brazil, Buenos Aires, Georgetown, Falkland Is.";
$l['timezone_gmt_minus_200'] = "(GMT -2:00) Mid-Atlantic, South Georgia and the South Sandwich Islands";
$l['timezone_gmt_minus_100'] = "(GMT -1:00) Azores, Cape Verde Islands";
$l['timezone_gmt'] = "(GMT) Casablanca, Dublin, Edinburgh, London, Lisbon, Monrovia";
$l['timezone_gmt_100'] = "(GMT +1:00) Berlin, Bratislava, Brussels, Copenhagen, Madrid, Paris, Prague, Rome, Warsaw";
$l['timezone_gmt_200'] = "(GMT +2:00) Athens, Istanbul, Cairo, Jerusalem, South Africa";
$l['timezone_gmt_300'] = "(GMT +3:00) Kaliningrad, Minsk, Baghdad, Riyadh, Nairobi";
$l['timezone_gmt_350'] = "(GMT +3:30) Tehran";
$l['timezone_gmt_400'] = "(GMT +4:00) Moscow, Abu Dhabi, Baku, Muscat, Tbilisi";
$l['timezone_gmt_450'] = "(GMT +4:30) Kabul";
$l['timezone_gmt_500'] = "(GMT +5:00) Islamabad, Karachi, Tashkent";
$l['timezone_gmt_550'] = "(GMT +5:30) Mumbai, Kolkata, Chennai, New Delhi";
$l['timezone_gmt_575'] = "(GMT +5:45) Kathmandu";
$l['timezone_gmt_600'] = "(GMT +6:00) Almaty, Dhaka, Yekaterinburg";
$l['timezone_gmt_650'] = "(GMT +6:30) Yangon";
$l['timezone_gmt_700'] = "(GMT +7:00) Bangkok, Hanoi, Jakarta";
$l['timezone_gmt_800'] = "(GMT +8:00) Beijing, Hong Kong, Perth, Singapore, Taipei, Manila";
$l['timezone_gmt_850'] = "(GMT +8:30) Pyongyang";
$l['timezone_gmt_875'] = "(GMT +8:45) Eucla";
$l['timezone_gmt_900'] = "(GMT +9:00) Osaka, Sapporo, Seoul, Tokyo, Irkutsk";
$l['timezone_gmt_950'] = "(GMT +9:30) Adelaide, Darwin";
$l['timezone_gmt_1000'] = "(GMT +10:00) Melbourne, Papua New Guinea, Sydney, Yakutsk";
$l['timezone_gmt_1050'] = "(GMT +10:30) Lord Howe Island";
$l['timezone_gmt_1100'] = "(GMT +11:00) Magadan, New Caledonia, Solomon Islands, Vladivostok";
$l['timezone_gmt_1150'] = "(GMT +11:30) Norfolk Island";
$l['timezone_gmt_1200'] = "(GMT +12:00) Auckland, Wellington, Fiji, Marshall Islands";
$l['timezone_gmt_1275'] = "(GMT +12:45) Chatham Islands";
$l['timezone_gmt_1300'] = "(GMT +13:00) Samoa, Tonga, Tokelau";
$l['timezone_gmt_1400'] = "(GMT +14:00) Line Islands";
$l['timezone_gmt_short'] = "GMT {1}({2})";

// Global language strings used for log deletion pages
$l['confirm_delete_logs'] = "Prune the selected log entries?";
$l['confirm_delete_all_logs'] = "Prune all log entries?";
$l['selected_logs_deleted'] = "The selected log entries have been deleted.";
$l['all_logs_deleted'] = "All log entries have been deleted.";
$l['delete_selected'] = "Delete Selected";
$l['delete_all'] = "Delete All Filtered";

// Misc
$l['encountered_errors'] = "The following errors were encountered:";
$l['invalid_post_verify_key'] = "An authorization code mismatch occurred. Please confirm that you wish to perform the action below.";
$l['invalid_post_verify_key2'] = "An authorization code mismatch occurred. Please double check that you are accessing this page correctly.";
$l['unknown_error'] = "An unknown error has occurred.";

// Code buttons editor language strings
$l['editor_bold'] = "Bold";
$l['editor_italic'] = "Italic";
$l['editor_underline'] = "Underline";
$l['editor_strikethrough'] = "Strikethrough";
$l['editor_subscript'] = "Subscript";
$l['editor_superscript'] = "Superscript";
$l['editor_alignleft'] = "Align left";
$l['editor_center'] = "Center";
$l['editor_alignright'] = "Align right";
$l['editor_justify'] = "Justify";
$l['editor_fontname'] = "Font Name";
$l['editor_fontsize'] = "Font Size";
$l['editor_fontcolor'] = "Font Color";
$l['editor_removeformatting'] = "Remove Formatting";
$l['editor_cut'] = "Cut";
$l['editor_copy'] = "Copy";
$l['editor_paste'] = "Paste";
$l['editor_cutnosupport'] = "Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X";
$l['editor_copynosupport'] = "Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C";
$l['editor_pastenosupport'] = "Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V";
$l['editor_pasteentertext'] = "Paste your text inside the following box:";
$l['editor_pastetext'] = "Paste Text";
$l['editor_numlist'] = "Numbered list";
$l['editor_bullist'] = "Bullet list";
$l['editor_undo'] = "Undo";
$l['editor_redo'] = "Redo";
$l['editor_rows'] = "Rows:";
$l['editor_cols'] = "Cols:";
$l['editor_inserttable'] = "Insert a table";
$l['editor_inserthr'] = "Insert a horizontal rule";
$l['editor_code'] = "Code";
$l['editor_php'] = "PHP";
$l['editor_width'] = "Width (optional):";
$l['editor_height'] = "Height (optional):";
$l['editor_insertimg'] = "Insert an image";
$l['editor_email'] = "E-mail:";
$l['editor_insertemail'] = "Insert an email";
$l['editor_url'] = "URL:";
$l['editor_insertlink'] = "Insert a link";
$l['editor_unlink'] = "Unlink";
$l['editor_more'] = "More";
$l['editor_insertemoticon'] = "Insert an emoticon";
$l['editor_videourl'] = "Video URL:";
$l['editor_videotype'] = "Video Type:";
$l['editor_insert'] = "Insert";
$l['editor_insertyoutubevideo'] = "Insert a YouTube video";
$l['editor_currentdate'] = "Insert current date";
$l['editor_currenttime'] = "Insert current time";
$l['editor_print'] = "Print";
$l['editor_viewsource'] = "View source";
$l['editor_description'] = "Description (optional):";
$l['editor_enterimgurl'] = "Enter the image URL:";
$l['editor_enteremail'] = "Enter the e-mail address:";
$l['editor_enterdisplayedtext'] = "Enter the displayed text:";
$l['editor_enterurl'] = "Enter URL:";
$l['editor_enteryoutubeurl'] = "Enter the YouTube video URL or ID:";
$l['editor_insertquote'] = "Insert a Quote";
$l['editor_invalidyoutube'] = "Invalid YouTube video";
$l['editor_dailymotion'] = "Dailymotion";
$l['editor_metacafe'] = "MetaCafe";
$l['editor_mixer'] = "Mixer";
$l['editor_vimeo'] = "Vimeo";
$l['editor_youtube'] = "Youtube";
$l['editor_facebook'] = "Facebook";
$l['editor_liveleak'] = "LiveLeak";
$l['editor_insertvideo'] = "Insert a video";
$l['editor_maximize'] = "Maximize";

$l['missing_task'] = "Error: Task file does not exist";
$l['task_backup_cannot_write_backup'] = "Error: The database backup task cannot write to backups directory.";
$l['task_backup_ran'] = "The database backup task successfully ran.";
$l['task_checktables_ran'] = "The check tables task successfully ran with no corrupted tables found.";
$l['task_checktables_ran_found'] = "Notice: The check tables task successfully ran and repaired the {1} table(s).";
$l['task_dailycleanup_ran'] = "The daily cleanup task successfully ran.";
$l['task_hourlycleanup_ran'] = "The hourly cleanup task successfully ran.";
$l['task_logcleanup_ran'] = "The log cleanup task successfully ran and pruned any old logs.";
$l['task_promotions_ran'] = "The promotions task successfully ran.";
$l['task_threadviews_ran'] = "The thread views task successfully ran.";
$l['task_usercleanup_ran'] = "The user cleanup task successfully ran.";
$l['task_massmail_ran'] = "The mass mail task successfully ran.";
$l['task_userpruning_ran'] = "The user pruning task successfully ran.";
$l['task_delayedmoderation_ran'] = "The delayed moderation task successfully ran.";
$l['task_massmail_ran_errors'] = "One or more problems occurred sending to \"{1}\":
{2}";
$l['task_versioncheck_ran'] = "The version check task successfully ran.";
$l['task_versioncheck_ran_errors'] = "Could not connect to MyBB for a version check.";
$l['task_recachestylesheets_ran'] = 'Re-cached {1} stylesheets.';
$l['task_sendmailqueue_ran'] = 'The send mail queue task sent up to {1} messages.';

$l['massmail_username'] = "Username";
$l['email_addr'] = "Email Address";
$l['board_name'] = "Board Name";
$l['board_url'] = "Board URL";

// Unlock ACP
$l['lockout_unlock'] = "Unlock Admin Control Panel";
$l['enter_username_and_token'] = "Please enter your username and the activation code to continue.";
$l['unlock_token'] = "Activation code:";
$l['unlock_account'] = "Unlock Account";

// Email message for if an admin account has been locked out
$l['locked_out_subject'] = "Administrator Account Locked Out at {1}";
$l['locked_out_message'] = "{1},

Your administrator account at {2} has been locked after {3} failed login attempts.

To unlock your account, please go to the URL below in your web browser.

{4}/{5}/index.php?action=unlock&uid={7}&token={6}

If the above link does not work correctly, go to

{4}/{5}/index.php?action=unlock

You will need to enter the following:
Username: {1}
Activation Code: {6}

Thank you,
{2} Staff";

$l['comma'] = ", ";

$l['search_for_a_user'] = "Search for a user";

$l['mybb_engine'] = "MyBB Engine";

// If the language string for "Username" is too cramped in the ACP Login box
// then use this to define how much larger you want the gap to be (in px)
// $l['login_field_width'] = "0";

$l['my2fa'] = "Two-Factor Authentication";
$l['my2fa_failed'] = "The code was incorrect, you're logged out now";
$l['my2fa_code'] = "Please enter the authentication code";
$l['my2fa_label'] = "Authentication code:";
$l['my2fa_no_codes'] = "Note: you've used all of your recovery codes. Please visit the <a href=\"index.php?module=home-preferences&amp;action=recovery_codes\">recovery codes page</a> to generate a new set.";
