<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['preferences_and_personal_notes'] = "Preferences &amp; Personal Notes";
$l['prefs_and_personal_notes_description'] = "Here you can manage your Admin Control Panel preferences and leave personal notes for yourself.";

$l['preferences'] = "Preferences";
$l['global_preferences'] = "Global Preferences";
$l['acp_theme'] = "Admin Control Panel Theme";
$l['select_acp_theme'] = "Please select a theme to use in the Admin Control Panel.";
$l['acp_language'] = "Admin Control Panel Language";
$l['select_acp_language'] = "Please select a language to use in the Admin Control Panel.";
$l['notes_not_shared'] = "These notes are not shared with other Administrators.";
$l['save_notes_and_prefs'] = "Save Personal Notes & Preferences";
$l['personal_notes'] = "Personal Notes";
$l['codemirror'] = "Turn on / off Code Mirror";
$l['use_codemirror_desc'] = "This preference allows you to turn off Code Mirror (used in template editing and stylesheet editing for syntax highlighting) if you are experiencing issues / slow loading.";

$l['success_preferences_updated'] = "The preferences have been successfully updated.";

$l['use_2fa_desc'] = "Two-Factor Authentication is a method to secure your admin account. After you've enabled it you'll see a QR Code below which you need to scan with an app <a href=\"https://docs.mybb.com/1.8/administration/security/2fa/\" target=\"_blank\" rel=\"noopener\">like Google Authenticator or Authy</a>. Those apps will generate a token which you need to enter on every acp login.";
$l['my2fa_qr'] = "Two-Factor Authentication Code";
$l['recovery_codes_desc'] = "View your <a href=\"index.php?module=home-preferences&amp;action=recovery_codes\">recovery codes</a>.";
$l['recovery_codes'] = "Recovery Codes";
$l['recovery_codes_warning'] = "<b>Note:</b> the codes will be regenerated on every page visit and can be only used once.";
$l['print_recovery_codes'] = "Print Recovery Codes";