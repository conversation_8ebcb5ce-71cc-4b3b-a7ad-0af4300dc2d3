<?php

$l['pinpost_desc'] = 'Pin the important replies of a thread just after first post.';
$l['pinpost_forums_title'] = 'Affected Forums';
$l['pinpost_forums_desc'] = 'Select the forums where PinPost will be active.';
$l['pinpost_groups_title'] = 'Controller Groups';
$l['pinpost_groups_desc'] = 'Select the groups who are allowed to pin / unpin posts.';
$l['pinpost_limit_title'] = 'Pinnable Posts';
$l['pinpost_limit_desc'] = 'Choose how many posts can be pinned for a thread.';
$l['pinpost_author_title'] = 'Author Powered';
$l['pinpost_author_desc'] = 'Allow the author of the thread to manage pin / unpin posts.';
$l['pinpost_force_redirect_title'] = 'Force Redirect';
$l['pinpost_force_redirect_desc'] = 'Force the redirect page when pinning or unpinning a post.';
