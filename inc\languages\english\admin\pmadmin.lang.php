<?php
$l['pma'] = "Private Message Admin";
$l['delete_pm'] = "Delete";
$l['archive_pm'] = "Archive";
$l['unarchive_pm'] = "Un Archive";
$l['archiving_pm'] = "Archiving";
$l['unarchiving_pm'] = "Removing from Archive";
$l['deleting_pm'] = "Deleting";
$l['viewing_pm'] = "Viewing Private Message";
$l['searching'] = "Searching Private Messages";
$l['archive_home'] = "Archive Home";
$l['home'] = "Home";
$l['missingstrreplacement'] = "Missing string or replacement";
$l['pma_uninstalled'] = "Private Message Admin is currently not activated";
$l['updates_text_new'] = "You do not have the latest version of this plugin! Click <a href=\"http://mods.mybboard.com/view.php?did=218\">here</a> to retrieve the latest file";
$l['private_message'] = " Private Message";
$l['private_messages'] = " Private Messages";
$l['reported_messages_new'] = "There {1} <a href=\"{2}/pmreport.php?action=reports\" target=\"_blank\">{3}</a> unread reported {4}";
$l['show_advanced_statistics'] = "Advanced Statistics";
$l['dont_show_advanced_statistics'] = "Normal Statistics";
$l['show_pmarchive'] = "Archive";
$l['show_pmnormal'] = "Home";
$l['show_duplicate_pms'] = "Normal Mode";
$l['dont_show_duplicate_pms'] = "Smart Mode";
$l['search_error_no_text'] = "You must submit criteria for the search";
$l['pma_message'] = "Private Message Admin Message";
$l['return_to_pms'] = "Return to Private Messages";
$l['end_credits'] = "Created by <a href=\"http://www.forums.rct3x.net\" target=\"_blank\">Tikitiki</a> - Version {1}";
$l['profile_header'] = "Private Messages - Profile Information for '~1~'";
$l['no_avatar'] = "No Avatar";
$l['never_visited'] = "Never";
$l['never_posted'] = "Never";
$l['profile_table_heading_1'] = "<tr><th class=\"subheader\" width=\"20%\"><b>Avatar</b></th><th class=\"subheader\" width=\"20%\"><b>User Group</b></th><th class=\"subheader\" width=\"20%\"><b>User ID</b></th><th class=\"subheader\" width=\"20%\"><b>Posts</b></th><th class=\"subheader\" width=\"20%\"><b>E-Mail</b></th><th class=\"subheader\" width=\"20%\"><b>Usertitle</b></th><th class=\"subheader\" width=\"20%\"><b>Registered</b></th><th class=\"subheader\" width=\"20%\"><b>Last Visit</b></th><th class=\"subheader\" width=\"20%\"><b>Last Active</b></th><th class=\"subheader\" width=\"20%\"><b>Last Post</b></th>";
$l['profile_table_heading_2'] = "<tr><th class=\"subheader\" width=\"20%\"><b>Website</b></th><th class=\"subheader\" width=\"20%\"><b>ICQ</b></th><th class=\"subheader\" width=\"20%\"><b>AIM</b></th><th class=\"subheader\" width=\"20%\"><b>MSN</b></th><th class=\"subheader\" width=\"20%\"><b>Yahoo</b></th><th class=\"subheader\" width=\"20%\"><b>Birthday</th></tr>";
$l['profile_table_heading_3'] = "<tr><th class=\"subheader\" width=\"20%\"><b>Signature</b></th></tr>";
$l['profile'] = "profile";
$l['close_window'] = "Close Window";
$l['search_header'] = "<tr><th class=\"subheader\" width=\"20%\"><b>Username</b></th><th class=\"subheader\" width=\"20%\"><b>User Group</b></th><th class=\"subheader\" width=\"10%\"><b>Registration Date</b></th><th class=\"subheader\" width=\"10%\"><b>Last Active</b></th><th class=\"subheader\" width=\"10%\"><b>Last Visit</b></th><th class=\"subheader\" width=\"10%\"><b>Last Post</b></th><th class=\"subheader\" width=\"20%\"><b> ? </b></th></tr>\r\n";
$l['search_error_no_users'] = "No users in the database";
$l['view_pms'] = "View PM's";
$l['missing'] = "Missing";
$l['search_no_user_pms'] = "~1~ has no private messages";
$l['search_result'] = " result";
$l['search_results'] = " results";
$l['search_header_results_all'] = "Private Messages - Searching all users with Private Messages - ~1~ ~2~";
$l['search_header_results_user'] = "Private Messages - Search results returned for '~1~' (~2~ ~3~)";
$l['search_no_results'] = "No Search Results found for \"~1~\"";
$l['pm_no_private_messages'] = "There are no Private Messages in your ~1~ table";
$l['pm_search_no_user_pms'] = "No Private Messages found for \"~1~\"";
$l['pma_search_user_from_header'] = "Private Messages Admin - Viewing All Private Messages from \"~1~\" (~2~ total) ";
$l['pma_search_user_both_header'] = "Private Messages Admin - Viewing All Private Messages from \"~1~\" and to \"~2~\" (~3~ total) ";
$l['pma_search_user_both_error'] = "No Private Messages Found from \"~1~\" and to \"~2~\"";
$l['pma_search_user_to_error'] = "No Private Messages Found to \"~1~\"";
$l['pma_search_user_to_header'] = "Private Messages Admin - Viewing All Private Messages to \"~1~\" (~2~ total) ";
$l['pma_archive_header'] = "Private Messages Admin Archive - Viewing Private Messages ~1~ through ~2~ out of ~3~ Private Messages";
$l['pma_header'] = "Private Messages Admin - Viewing Private Messages ~1~ through ~2~ out of ~3~ Private Messages";
$l['pma_no_messages'] = "Error: No Private Messages";
$l['pma_subject_header'] = "Subject";
$l['pma_id_header'] = "PM ID";
$l['pma_message_header'] = "Message";
$l['pma_from_header'] = "From";
$l['pma_to_header'] = "To";
$l['pma_tofrom_title'] = "Note: Ordered by ID";
$l['pma_option_status'] = "Status";
$l['pma_option_include_sig'] = "Include Sig";
$l['pma_option_smilies_on'] = "Smilies On";
$l['pma_option_reciept'] = "Reciept";
$l['pma_option_read_time'] = "Read Time";
$l['pma_reciept_status_1'] = "Receipt not requested OR receipt denied";
$l['pma_reciept_status_2'] = "Receipt requested, message not read";
$l['pma_reciept_status_3'] = "Message read, receipt not denied";
$l['pma_pm_status_1'] = "Draft/New";
$l['pma_pm_status_2'] = "Sent";
$l['pma_pm_status_3'] = "Replied";
$l['pma_pm_status_4'] = "Forwarded";
$l['pma_pm_status_5'] = "Unknown";
$l['pma_search_text'] = "Search for all Private Messages <select name=\"type\"><option value=\"from\">from</option><option value=\"to\">to</option><option value=\"both\">From & To (Seperated by a dash)</option></select>:";
$l['pma_next'] = "Next";
$l['pma_previous'] = "Previous";
$l['pma_error'] = "Private Message Admin Error";
$l['pma_view_notfound'] = "Private Message \"~1~\" not found";
$l['x'] = "X";
$l['pma_view_header'] = "Viewing Private Message #~1~ (~2~ Total)";
$l['pma_deleted_success'] = "Private Message Deleted Succesfully";
$l['pma_archived_success'] = "Private Message Archived Succesfully";
$l['pma_unarchived_success'] = "Private Message Un Archived Succesfully";
$l['pma_off'] = "Private Message Admin is currently turned off";
$l['error_empty_search'] = "You cannot leave the Search Field empty!";
$l['error_xml'] = "Cannot create an XMLHTTP instance";
$l['deleted'] = "Deleted";
$l['unarchived'] = "Un Archived";
$l['archived'] = "Archived";
$l['pmnum'] = "Private Message #";
$l['successfully'] = "Successfully";
$l['error_request'] = "There was a problem with the request. Error Code:"; 
$l['delete_confirm'] = "Are you sure you want to delete this Private Message?";
$l['archive_confirm'] = "Are you sure you want to archive this Private Message?";
$l['unarchive_confirm'] = "Are you sure you want to un-archive this Private Message?";
$l['ajax_loading'] = "Loading. <br />Please Wait..";
$l['is'] = "is";
$l['are'] = "are";
$l['pruning'] = 'Prune PMs';
$l['pruning_confirm'] = 'Are you sure you want to prune PMs? (Specified in the settings)';
$l['pruning_confirm2'] = 'Are you REALLY sure you want to prune PMs? (Specified in the settings)';
$l['pruned'] = 'Pruned ~1~ private messages which were ~2~ days old.';
?>