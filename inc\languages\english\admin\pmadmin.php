<?php
error_reporting(E_ALL);

define('IN_MYBB', 1);

require "./global.php";
$lang->load('pmadmin');
require MYBB_ROOT."inc/class_parser.php";
$parser = new postParser;
$pma = new PrivateMessageAdmin;


class PrivateMessageAdmin
{

	var $internalver = "2.3.3";
	var $developver = "2303";
	var $type = "normal";
	var $this_debug_count = 0;
	var $input = array();

	function PrivateMessageAdmin()
	{
		global $db, $lang, $mybb, $plugins;
		
		// Are we turned off, or on?
		$this->check_status();
		
		$plugins->run_hooks("pma_start");
		
		$extrasql = ($mybb->input['unique'] == "true") ? "uid != fromid" : "";
	
		if(isset($_GET['phpinfo']))
		{
			phpinfo();
			exit;
		}
		
		// Are be being invisibly redirected somewhere?
		$this->check_redirect();
		
		// Are we using the archive?
		$table = (isset($mybb->input['action']) && $mybb->input['action'] == "archive") ? "pmarchive" : "privatemessages";
		$query = $db->simple_select(TABLE_PREFIX.$table, "COUNT(*) as rows", $extrasql);
		define("PMCOUNT", $db->fetch_field($query, 'rows'));
		
		// ALERT! SECURITY BREACH! ;)
		$this->get_secure_input();
		ksort($this->input);
		
		// Because we're using $_SERVER['PHP_SELF'] so much, lets just rap it up in a definition to make it faster
		define("PHP_SELF", $_SERVER['PHP_SELF']);
		
		ob_start();
		
		// Do we want to check the developer version?
		$this->developer();		
		
		$this->show_javascript();
		$this->show_css();
		
		$this->debug(__LINE__, __FILE__);
		
		// Are you an admin?
		checkadminpermissions("caneditpms");
		logadmin();
		
		//ob_start();
		
		if(isset($this->input['archivepid']))
		{
			$this->archive(intval($this->input['archivepid']));
		}
		
		if(isset($this->input['unarchivepid']))
		{
			$this->unarchive(intval($this->input['unarchivepid']));
		}
		
		if(isset($this->input['deletepid']))
		{
			$this->delete(intval($this->input['deletepid']));
		}
		
		$archive = (isset($this->input['archive']) && $this->input['archive'] != "") ? "&amp;archive=".$this->input['archive'] : "";
		
		// We have NAV!
		if(!isset($_POST['user']) && !isset($_POST['userfrom']) && $this->input['action'] == "profile" && isset($this->input['uid']))
		{
			cpheader();
			$this->profile();
			exit;
		}
		else
		{
			$this->setup_nav();
		}
		
		$this->debug(__LINE__, __FILE__);
		
		$this->check_for_updates();
		
		// Lets check if there are any reported pms
		$this->check_reported_pms();
		
		$this->debug(__LINE__, __FILE__);
		
		
		$tblname = (isset($this->input['archive']) && $this->input['archive'] == "true") ? $lang->private_message." ".$lang->show_pmarchive : $lang->private_message;		
		if(PMCOUNT < 1)
		{
			echo $this->get_hopto_menu(true);
			cperror("<div align=\"center\">".$this->sprintc("pm_no_private_messages", $tblname)."</div>", $lang->pma_message);
		}
		
		echo $this->get_hopto_menu();
		
		$this->debug(__LINE__, __FILE__);
		
		if(isset($_GET['action']) && $_GET['action'] == 'prunepms')
		{
			$this->debug(__LINE__, __FILE__);
			
			$this->return_to_pma();
			
			cpheader("", 0);
			starttable();
			tableheader($lang->pma_message);
			makelabelcode("<div align=\"center\"> {$lang->pruning_confirm} - <a href=\"pmadmin.php".($this->parse_http_query("action", "prunepms2"))."\">Yes</a></div>");
			endtable();
			exit;
		}
		
		if(isset($_GET['action']) && $_GET['action'] == 'prunepms2')
		{
			$this->debug(__LINE__, __FILE__);
			
			$this->return_to_pma();
			
			starttable();
			tableheader($lang->pma_message);
			makelabelcode("<div align=\"center\"> {$lang->pruning_confirm2} - <a href=\"pmadmin.php".($this->parse_http_query("action", "do_prunepms"))."\">Yes</a></div>");
			endtable();
			exit;
		}
		
		if(isset($_GET['action']) && $_GET['action'] == 'do_prunepms')
		{
			$this->debug(__LINE__, __FILE__);
			
			$this->return_to_pma();
			
			cpheader("", 0);
			starttable();
			tableheader($lang->pma_message);
			$deleted = $this->prune_pms();
			makelabelcode("<div align=\"center\">".$this->sprintc($lang->pruned, array($deleted, $mybb->settings['pmaprunedays']))."</div>");
			endtable();
			exit;
		}
		
		if(isset($this->input['id']) && $this->input['id'] >= 0)
		{
			$this->view();
		}
		
		$this->debug(__LINE__, __FILE__);
		
		if(isset($_POST['user']) && $_POST['user'] == "")
		{
			cperror("<div align=\"center\">".$lang->search_error_no_text."</div>", $lang->pma_message);
		}
		
		$this->debug(__LINE__, __FILE__);
		
		// Are we doing a search?
		$type = array('to', 'from');
		if($mybb->input['user'] != "" && in_array($mybb->input['type'], $type) && $mybb->request_method == "post")
		{
			$this->return_to_pma();
			$this->search_user();
		}
		else
		{
			$this->run();
		}
		
		$this->debug(__LINE__, __FILE__);
		
		printf("<div align=\"center\">".$lang->end_credits."</div>", $this->internalver);
		
		// Lets not forget the footer ;)
		cpfooter();
		$output = ob_get_contents();
		ob_end_clean();
		
		echo $output;
		
		$plugins->run_hooks("pma_end");		
	}

	function debug($line='', $file='')
	{
		if(isset($_GET['debug']))
		{
			$this->pma_debug_count++;

			if($line != '')
			{
				$line = "ON LINE $line";
			}
			if($file != '')
			{
				$file = "IN FILE ".strtoupper($file);
			}
			echo "<!-- PMA DEBUG CHECK ".$this->pma_debug_count." $line $file -->\r\n";
		}
	}

	function main_table_header()
	{
		global $lang;

		$pmlandt = $lang->pma_id_header;
		$sublandt = $lang->pma_subject_header;
		$messagelandt = $lang->pma_message_header;
		$frompersont = $lang->pma_from_header;
		$topersont = $lang->pma_to_header;

		$pmordert = $this->input['orderby'];
		$subordert = $this->input['orderby'];
		$messageordert = $this->input['orderby'];
		$fromordert = $this->input['orderby'];
		$toordert = $this->input['orderby'];

		if(!isset($this->input['orderbyn']) || $this->input['orderbyn'] == "")
		{
			$pmlandt = "<span class=\"green\">".$lang->pma_id_header." &uarr;</span>";
		}
		elseif($this->input['orderbyn'] == "pmid")
		{
			if($this->input['orderby'] == "desc")
			{
				$pmlandt = "<span class=\"green\">".$lang->pma_id_header." &uarr;</span>";
				$pmordert = "asc";
			}
			else
			{
				$pmlandt = "<span class=\"green\">".$lang->pma_id_header." &darr;</span>";
				$pmordert = "desc";
			}
		}
		elseif($this->input['orderbyn'] == "subject")
		{
			if($this->input['orderby'] == "desc")
			{
				$sublandt = "<span class=\"green\">".$lang->pma_subject_header." &uarr;</span>";
				$subordert = "asc";
			}
			else
			{
				$sublandt = "<span class=\"green\">".$lang->pma_subject_header." &darr;</span>";
				$subordert = "desc";
			}
		}
		elseif($this->input['orderbyn'] == "message")
		{
			if($this->input['orderby'] == "desc")
			{
				$messagelandt = "<span class=\"green\">".$lang->pma_message_header." &uarr;</span>";
				$messageordert = "asc";
			}
			else
			{
				$messagelandt = "<span class=\"green\">".$lang->pma_message_header." &darr;</span>";
				$messageordert = "desc";
			}
		}
		elseif($this->input['orderbyn'] == "fromid")
		{
			if($this->input['orderby'] == "desc")
			{
				$frompersont = "<span class=\"green\">".$lang->pma_from_header." &uarr;</span>";
				$fromordert = "asc";
			}
			else
			{
				$frompersont = "<span class=\"green\">".$lang->pma_from_header." &darr;</span>";
				$fromordert = "desc";
			}
		}
		elseif($this->input['orderbyn'] == "toid")
		{
			if($this->input['orderby'] == "desc")
			{
				$topersont = "<span class=\"green\">".$lang->pma_to_header." &uarr;</span>";
				$toordert = "asc";
			}
			else
			{
				$topersont = "<span class=\"green\">".$lang->pma_to_header." &darr;</span>";
				$toordert = "desc";
			}
		}
		else
		{
			$pmlandt = "<span class=\"green\">".$lang->pma_id_header." &uarr;</span>";
		}

		$postdata .= "<tr><th class=\"subheader\" width=\"50\"><b><a href=\"pmadmin.php".($this->parse_http_query(array("1" => "orderby", "2" => "orderbyn"), array("1" => $pmordert, "2" => "pmid")))."\">$pmlandt</a></b></th><th class=\"subheader\" width=\"200\"><b><a href=\"pmadmin.php?".($this->parse_http_query(array("1" => "orderby", "2" => "orderbyn"), array("1" => $subordert, "2" => "subject")))."\">$sublandt</a></b></th>";
		$postdata .= "<th class=\"subheader\" width=\"500\"><b><a href=\"pmadmin.php".($this->parse_http_query(array("1" => "orderby", "2" => "orderbyn"), array("1" => $messageordert, "2" => "message")))."\">$messagelandt</a></b></th><th class=\"subheader\" width=\"20\"><b><a href=\"pmadmin.php?".($this->parse_http_query(array("1" => "orderby", "2" => "orderbyn"), array("1" => $fromordert, "2" => "fromid")))."\" title=\"".$lang->pma_tofrom_title."\">$frompersont</a></b></th>\r\n";
		$postdata .= "<th class=\"subheader\" width=\"20\"><b><a href=\"pmadmin.php".($this->parse_http_query(array("1" => "orderby", "2" => "orderbyn"), array("1" => $toordert, "2" => "toid")))."\" title=\"".$lang->pma_tofrom_title."\">$topersont</a><br /></b></th><th class=\"subheader\" width=\"70\"><b>Send Time</b></th>\r\n";

		if(isset($this->input['advanced']) && $this->input['advanced'] == "true")
		{
			$postdata .= "<th class=\"subheader\" width=\"50\"><b>".$lang->pma_option_status."</b></th>\r\n";
			$postdata .= "<th class=\"subheader\" width=\"50\"><b>".$lang->pma_option_include_sig."</b></th>\r\n";
			$postdata .= "<th class=\"subheader\" width=\"5\"><b>".$lang->pma_option_smilies_on."</b></th>\r\n";
			$postdata .= "<th class=\"subheader\" width=\"5\"><b>".$lang->pma_option_reciept."</b></th>\r\n";
			$postdata .= "<th class=\"subheader\" width=\"5\"><b>".$lang->pma_option_read_time."</b></th>\r\n";
		}

		$postdata .= "</tr>\r\n";

		return $postdata;
	}

	function setup_nav()
	{
		global $lang, $mybb;

		addacpnav($lang->pma, "pmadmin.php?".SID);

		if(isset($_GET['archivepid']))
		{
			sprintf($lang->archiving_pm, intval($_GET['archivepid']));
			addacpnav($lang->archiving_pm);
		}
		elseif(isset($_GET['unarchivepid']) && is_numeric($_GET['unarchivepid']))
		{
			sprintf($lang->unarchiving_pm, intval($_GET['unarchivepid']));
			addacpnav($lang->unarchiving_pm);
		}
		elseif(isset($_GET['deletepid']) && is_numeric($_GET['deletepid']))
		{
			sprintf($lang->deleting_pm, intval($_GET['deletepid']));
			addacpnav($lang->deleting_pm);
		}
		elseif(isset($_GET['id']))
		{
			sprintf($lang->viewing_pm, intval($_GET['id']));
			addacpnav($lang->viewing_pm);
		}
		elseif($_POST['user'] != "" || $_POST['userto'] != "" || $_POST['userfrom'] != "")
		{
			addacpnav($lang->searching);
		}
		elseif($_GET['action'] == "prunepms" || $_GET['action'] == "prunepms2" || $_GET['action'] == "do_prunepms")
		{
			addacpnav($lang->pruning);
		}
		elseif($_GET['action'] == "archive")
		{
			addacpnav($lang->archive_home);
		}
		else
		{
			addacpnav($lang->home);
		}
		
		$action = "";
		if(isset($this->input['action']))
		{
			$action = "&amp;action=".$this->input['action'];
		}
		
		$extraheaders = '<link rel="stylesheet" type="text/css" href="pmadmin.php?'.SID.'&amp;get_css'.$action.'" />
<script type="text/javascript" src="'.$mybb->settings['bburl'].'/jscripts/prototype.lite.js?ver=121"></script>
<script type="text/javascript" src="'.$mybb->settings['bburl'].'/jscripts/moo.ajax.js?ver=121"></script>
<script type="text/javascript" src="'.$mybb->settings['bburl'].'/jscripts/general.js?ver=121"></script>
<script type="text/javascript" src="'.$mybb->settings['bburl'].'/jscripts/popup_menu.js?ver=121"></script>
<script type="text/javascript" src="pmadmin.php?'.SID.'&amp;get_javascript"></script>
';
		$extraheaders .= "
<script type=\"text/javascript\" language=\"Javascript\">
	var error_empty_search = \"{$lang->error_empty_search}\";
	var error_xml = \"{$lang->error_xml}\";
	var deleted = \"{$lang->deleted}\";
	var unarchived = \"{$lang->unarchived}\";
	var archived = \"{$lang->archived}\";
	var pmnum = \"{$lang->pmnum}\";
	var successfully = \"{$lang->successfully}\";
	var error_request = \"{$lang->error_request}\";
	var delete_confirm = \"{$lang->delete_confirm}\";
	var archive_confirm = \"{$lang->archive_confirm}\";
	var unarchive_confirm = \"{$lang->unarchive_confirm}\";
	var cOver = \"#EEB0B0\";
	var altbg2 = \"#EEEEEE\";
	var altbg1 = \"#F3F3F3\";
	var doajax = \"{$mybb->settings['pmaajax']}\";
	var profile = \"{$lang->profile}\";
	var loading_text = '{$lang->ajax_loading}';
</script>
";
		
		cpheader("", 1, "", $extraheaders);
	}

	function return_to_pma()
	{
		global $lang;
		
		cpheader("", 0);
		starttable("35%");
		tableheader($lang->pma_message);
		makelabelcode("<div align=\"center\"> - <a href=\"pmadmin.php?".SID."\">".$lang->return_to_pms."</a> - </div>");
		endtable();
	}

	function search_user()
	{
		global $db, $settings, $mybb, $lang, $config, $search_header;
		
		if(!isset($mybb->input['user']) || strtolower($mybb->request_method) != "post")
		{
			cperror("<div align=\"center\">".$lang->search_error_no_users."</div>", $lang->pma_message);
		}
		
		require MYBB_ROOT."inc/functions_search.php";

		$postdata = $search_header;

		$users = $db->simple_select(TABLE_PREFIX."users", "COUNT(*) as users");
		$usergroup = $db->simple_select(TABLE_PREFIX."usergroups");

		$search = 0;
		$rows = $db->fetch_field($users, "users");

		if($rows <= 0)
		{
			cperror("<div align=\"center\">".$lang->search_error_no_users."</div>", $lang->pma_message);
		}

		$query = $db->simple_select(TABLE_PREFIX."settings", "*", "name='pmasearchswitch'", array('limit' => 1));
		$casesesitive = $db->fetch_field($query, 'value');

		$usersearch = $db->escape_string($mybb->input['user']);
		
		if($config['dbtype'] == "mysql" || $config['dbtype'] == "mysqli" && !empty($usersearch))
		{
			$search_data = clean_keywords($usersearch);
			$userids = array();
			if($mybb->input['casesensitive'] == "yes")
			{
				$query = $db->simple_select(TABLE_PREFIX."users u LEFT JOIN ".TABLE_PREFIX."usergroups g ON (g.gid=u.usergroup)", "u.*, g.title as usertitle", "username='".$db->escape_string($search_data)."'");
			}
			else
			{
				$search_data = strtolower($search_data);
				$query = $db->simple_select(TABLE_PREFIX."users u LEFT JOIN ".TABLE_PREFIX."usergroups g ON (g.gid=u.usergroup)", "u.*, g.title as usertitle", "LOWER(username) LIKE '%".$db->escape_string($search_data)."%'");
			}
			
			if($db->num_rows($query) < 1)
			{
				cperror("<div align=\"center\">".$this->sprintc("search_no_results", $search_data)."</div>", $lang->pma_message);
			}
			else
			{
				while($user = $db->fetch_array($query))
				{
					$postdata .= "<tr><th class=\"$bgcolor\" width=\"20%\"><a href=\"pmadmin.php?".SID."&amp;action=profile&amp;uid=".$user['uid']."\" onclick=\"window.open('pmadmin.php?".SID."&amp;action=profile&amp;uid=".$user['uid']."','".$lang->profile."','width=900,height=500,toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,copyhistory=no,resizable=yes');";
					$postdata .= " return false;\"  target=\"_blank\">".$user['username']."</a></th><th class=\"$bgcolor\" width=\"20%\">".$user['usertitle']."</th><th class=\"$bgcolor\" width=\"12%\">".my_date($mybb->settings['dateformat'],$user['regdate'])."</th><th class=\"$bgcolor\" width=\"12%\">".my_date($mybb->settings['dateformat'],$user['lastactive'])."</th>";
					$postdata .= "<th class=\"$bgcolor\" width=\"12%\">".my_date($mybb->settings['dateformat'],$user['lastvisit'])."</th><th class=\"$bgcolor\" width=\"12%\">".my_date($mybb->settings['dateformat'],$user['lastpost'])."</th><th class=\"$bgcolor\" width=\"12%\"><a href=\"pmadmin.php?".SID."&amp;userid=".$user['uid']."\">".$lang->view_pms."</a></th></tr>";
				}
			}
		}
		else
		{
			//$lang->load("search");
			cperror("Error: No Support", $lang->pma_message);
		}

		$search1 = ($search == "1") ? $lang->search_result : $lang->search_results;

		starttable();
		tableheader($this->sprintc("search_header_results_user", array($usersearch, $db->num_rows($query), $search1)), "", "7");
		echo $postdata;
		endtable();
	}

	function check_for_updates()
	{
		global $mybb, $lang, $db, $cache;
		
		if($mybb->settings['pmacheckupdates'] == "yes")
		{
			// Fetch the last time an update check was run
			$update_check = $cache->read("pma_update_check");
	
			// If last update check was greater than two weeks ago (14 days) show an alert
			if($update_check['last_check'] <= time()-60*60*24)
			{
				// Do we have any updates?
				if(@file_get_contents("http://rct3x.net/tikitiki/plugin_updatecheck.php?codename=pma&checktype=script&vercode=".$this->internalver) == "yes")
				{
					?>
					<div align="center">
					<table border="0" cellspacing="1" cellpadding="4" class="alert">
					<tbody>
					<tr>
					<td><div align="center"><strong><?php echo $lang->updates_text_new ?></strong></div></td>
					</tr>
					</tbody>
					</table>
					</div>
					<br />
					<?php
				}
			}
	
			$update_cache = array(
				"last_check" => time()
			);
			$cache->update("pma_update_check", $update_cache);

		}
	}
	
	function get_secure_input()
	{
		$acceptable_gets = array(
			"orderbyn",
			"userid",
			"min",
			"action",
			"advanced",
			"unique",
			"orderby",
			"id",
			"useridto",
			"userid",
			"uid",
			"archivepid",
			"unarchivepid",
			"deletepid",
			"debug"
		);

		if(count($_GET) > 0)
		{
			foreach($acceptable_gets as $key => $value)
			{
				if(isset($_GET[$value]))
				{
					$this->input[strtolower($value)] = rawurlencode($_GET[$value]);
				}
			}
		}

		define("PHP_HEADERS",  $this->parse_http_query("", "", array('do' => 'false')));

		$orderbyn = array(
			"subject",
			"message",
			"toid",
			"fromid",
			"pmid"
		);

		$this->input['orderbyn'] = (isset($this->input['orderbyn']) && (in_array(strtolower($this->input['orderbyn']), $orderbyn))) ? strtolower($this->input['orderbyn']) : "pmid";

		if(isset($this->input['userid']))
		{
			$this->input['userid'] = intval($this->input['userid']);
		}

		if(isset($this->input['min']))
		{
			$this->input['min'] = intval($this->input['min']);
			
			if($this->input['min'] > PMCOUNT)
			{
				$this->input['min'] -= 20;
			}
		}
		else
		{
			$this->input['min'] = "0";
		}

		if(isset($this->input['action']))
		{
			if($this->input['action'] == "archive")
			{
				$this->input['action'] = "archive";
			}
			else
			{
				$this->input['action'] == "profile";
			}
		}

		if(isset($this->input['advanced']) && $this->input['advanced'] == "true")
		{
			$this->input['advanced'] = "true";
		}

		if(isset($this->input['unique']) && $this->input['unique'] == "true")
		{
			$this->input['unique'] = "true";
		}

		$this->input['orderby'] = (isset($this->input['orderby']) && ($this->input['orderby'] == "desc" || $this->input['orderby'] == "asc")) ? $this->input['orderby'] : "desc";

		if(isset($this->input['id']) && $this->input['id'] != "")
		{
			$this->input['id'] = intval($this->input['id']);
		}

		if(isset($this->input['uid']) && $this->input['uid'] != "")
		{
			$this->input['uid'] = intval($this->input['uid']);
		}

		if(isset($this->input['useridto']) && $this->input['useridto'] != "")
		{
			$this->input['useridto'] = intval($this->input['useridto']);
		}

		if(isset($this->input['userid']) && $this->input['userid'] != "")
		{
			$this->input['userid'] = intval($this->input['userid']);
		}
		
		if(isset($this->input['profileuid']) && $this->input['profileuid'] != "")
		{
			$this->input['profileuid'] = intval($this->input['profileuid']);
		}
		
		if(isset($this->input['archivepid']) && $this->input['archivepid'] != "")
		{
			$this->input['archivepid'] = intval($this->input['archivepid']);
		}
		
		if(isset($this->input['unarchivepid']) && $this->input['unarchivepid'] != "")
		{
			$this->input['unarchivepid'] = intval($this->input['unarchivepid']);
		}
		
		if(isset($this->input['deletepid']) && $this->input['deletepid'] != "")
		{
			$this->input['deletepid'] = intval($this->input['deletepid']);
		}
		
		if(isset($this->input['debug']))
		{
			$this->input['debug'] = "true";
		}		

		return $this->input;
	}
	
	function run()
	{
		global $db, $mybb, $lang, $parser, $table;
	
		$this->debug(__LINE__, __FILE__);	
	
		$postdata = startform("pmadmin.php?".SID, "pmform", "");
		$max = $this->input['min'] + 20;
		$mincnt = $this->input['min'];
	
		if(isset($this->input['userid']) && $this->input['userid'] != "" && !isset($this->input['useridto']) && $this->input['archive'] != "true")
		{
			$options = array(
				"order_by" => $this->input['orderbyn'],
				"order_dir" => $this->input['orderby']
			);
			$query = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "fromid = '".$this->input['userid']."'", $options);
			$rows = $db->num_rows($query);
			if($rows < 1)
			{
				$this->debug(__LINE__, __FILE__);
				cperror("<div align=\"center\">".$this->sprintc("pm_search_no_user_pms", $this->getuser($this->input['userid']))."</div>", $lang->pma_message);
			}
	
			$postdata .= starttable();
			$postdata .= tableheader($this->sprintc("pma_search_user_from_header", array($this->getuser($this->input['userid']), $rows)), "", "14");
		}
		elseif(isset($_POST['type']) && $_POST['type'] == "both" && !isset($this->input['userid']) && !isset($this->input['useridto']) && $this->input['archive'] != "true")
		{
	
			list($from, $to) = split('-', $_POST['user']);
			$options = array(
				"order_by" => $this->input['orderbyn'],
				"order_dir" => $this->input['orderby']
			);
			$query = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "fromid = '".$this->getid($from)."' AND toid = '".$this->getid($to)."'", $options);
			$rows = $db->num_rows($query);
			if($rows < 1)
			{
				cperror("<div align=\"center\">".$this->sprintc("pma_search_user_both_error", array($from, $to))."</div>", $lang->pma_message);
			}
	
			$postdata .= starttable();
			$postdata .= tableheader($this->sprintc("pma_search_user_both_header", array($from, $to, $useridrows)), "", "14");
		}
		elseif(isset($this->input['useridto']) && $this->input['useridto'] != "" && !isset($this->input['userid']) && $this->input['archive'] != "true")
		{
			$options = array(
				"order_by" => $this->input['orderbyn'],
				"order_dir" => $this->input['orderby']
			);
			$query = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "fromid = '".$this->input['useridto']."'", $options);
			$rows = $db->num_rows($query);
			if($rows < 1)
			{
				cperror("<div align=\"center\">".$this->sprintc("pma_search_user_to_error", $this->getuser($this->input['useridto']))."</div>", $lang->pma_message);
			}
	
			$postdata .= starttable();
			$postdata .= tableheader($this->sprintc("pma_search_user_to_header", array($this->getuser($this->input['useridto']), $useridrows)), "", "14");
		}
		elseif(isset($this->input['action']) && $this->input['action'] == "archive")
		{
			$allcntr = PMCOUNT;
			$cnt = $allcntr - 20;
	
			if($allcnt < $max)
			{
				$max = PMCOUNT;
			}
	
			$options = array(
				"order_by" => $this->input['orderbyn'],
				"order_dir" => $this->input['orderby'],
				"limit_start" => $this->input['min'],
				"limit" => "20"
			);
			$query = $db->simple_select(TABLE_PREFIX."pmarchive", "*", "", $options);
			$rows = $db->num_rows($query);
	
			if($rows > 0)
			{
				$postdata .= starttable();
				$postdata .= tableheader($this->sprintc("pma_archive_header", array($mincnt, $max, PMCOUNT)), "", "14");
			}
		}
		else
		{
	
			$cnt = PMCOUNT - 20;
			$allcntr = PMCOUNT;
	
			if($this->input['min'] > PMCOUNT)
			{
				$this->input['min'] = $cnt;
			}
	
			if($mincnt >= PMCOUNT)
			{
				$mincnt = $cnt;
			}
	
			if(PMCOUNT < $max)
			{
				$max = PMCOUNT;
			}
	
			if(isset($this->input['unique']) && $this->input['unique'] == "true")
			{
				$options = array(
					"order_by" => $this->input['orderbyn'],
					"order_dir" => $this->input['orderby'],
					"limit_start" => $mincnt,
					"limit" => "20"
				);
				$query = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "uid != fromid", $options);
			}
			else
			{
				$options = array(
					"order_by" => $this->input['orderbyn'],
					"order_dir" => $this->input['orderby'],
					"limit_start" => $mincnt,
					"limit" => "20"
				);
				$this->debug(__LINE__, __FILE__);
				$query = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "", $options);
			}
	
			$rows = $db->num_rows($query);
	
			if($rows > 0)
			{
				$postdata .= starttable();
				$postdata .= tableheader($this->sprintc("pma_header", array($mincnt, $max, PMCOUNT)), "", "14");
			}
		}
	
		if($rows < 1)
		{
			endtable();
			endform();
			cperror("<div align=\"center\">".$lang->pma_no_messages."</div>", $lang->pma_message);
		}
	
		$postdata .= $this->main_table_header();
	
		while($pm = $db->fetch_array($query))
		{
			if($pm['toid'] != "")
			{
				$toid = $this->getuser(intval($pm['toid']));
			}
			else
			{
				$toid = "-2";
			}
	
			if($pm['fromid'] != "")
			{
				$fromid = $this->getuser(intval($pm['fromid']));
			}
			else
			{
				$fromid = "-2";
			}
	
			$optionsarray = array(
				'allow_html' => $mybb->settings['pmaallowhtml'],
				'allow_smilies' => $mybb->settings['pmaallowsmilies'],
				'allow_imgcode' => $mybb->settings['pmaallowimgcode'],
				'filter_badwords' => $mybb->settings['pmaallowbadwords'],
				'allow_mycode' => $mybb->settings['pmaallowmycode'],
				'nl2br' => "yes"
			);
	
			$fullsubject = stripslashes($pm['subject']);
			$fullmessage = stripslashes($pm['message']);
	
			if($mybb->settings['pmastripmycode'] == "yes")
			{
				$fullsubject = $parser->strip_mycode($fullsubject);
				$fullmessage = $parser->strip_mycode($fullmessage, array('allow_html' => $mybb->settings['pmaallowhtml']));
			}
			else
			{
				$fullsubject = $parser->parse_message($fullsubject, array('filter_badwords' => $mybb->settings['pmaallowbadwords']));
				$fullmessage = $parser->parse_message($fullmessage, $optionsarray);
			}
	
			$subjectsml = $this->slimtext($fullsubject, "subject");
			$messagesml = $this->slimtext($fullmessage, "message");
	
			$bgcolor = getaltbg();
	
			$postdata .= "<tr id=\"column-".$pm['pmid']."\" class=\"$bgcolor\"";
			$mybb->settings['pmahighlight'] = "off";
			$postdata .= ($mybb->settings['pmahighlight'] == "on") ? " onmouseover=\"hl(this,cOver,".$pm['pmid'].");\" onmouseout=\"hl(this,$bgcolor,".$pm['pmid'].");\">\n" : ">\n";
	
			//$mybbfolderto = "pmadmin.php?".SID."&amp;action=profile&amp;uid=".$pm['toid'];
			//$mybbfolderfrom = "pmadmin.php?".SID."&amp;action=profile&amp;uid=".$pm['fromid'];
	
			$smilies = $db->simple_select(TABLE_PREFIX."icons", "*", "iid='".$pm['icon']."'");
			$smilierow = $db->fetch_array($smilies);
			$smilie = $smilierow['path'];
	
			if(is_file("./".$smilie))
			{
				$smiliepath = "<img src=\"../".$smilie."\">";
			}
			else
			{
				$smiliepath = "";
			}
			
			$un = "";
			if($this->input['action'] == "archive")
			{
				$lang->archive_pm = $lang->unarchive_pm;
				$un = "un";
			}
	
			$postdata .= "<td id=\"pid{$pm['pmid']}\">\n
			<a href=\"javascript:;\" id=\"pm_".$pm['pmid']."\">".$pm['pmid']."</a>\n
<script type=\"text/javascript\">
	document.write('<div id=\"pm_".$pm['pmid']."_popup\" class=\"popup_menu\" style=\"display: none;\"><div class=\"popup_item_container\"><a href=\"javascript:;\" onclick=\"PMA.".$un."archive(".$pm['pmid'].");\" class=\"popup_item\">".$lang->archive_pm."<\/a><\/div><div class=\"popup_item_container\"><a href=\"javascript:;\" onclick=\"PMA.deletepm(".$pm['pmid'].");\" class=\"popup_item\">".$lang->delete_pm."<\/a><\/div><\/div>');
	new PopupMenu(\"pm_".$pm['pmid']."\");
</script>\n<div align=\"center\">\n</div>\n</td>\n";
	
			if($subjectsml == "")
			{
				$postdata .= "<td id=\"s{$pm['pmid']}\">\n<div align=\"center\">$subjectsml</div>\n</td>\n";
			}
			else
			{
				$postdata .= "<td id=\"s{$pm['pmid']}\">\n<div align=\"center\">\n$smiliepath<a title=\"$fullsubject\" href=\"pmadmin.php".($this->parse_http_query("id", $pm['pmid']))."\">$subjectsml</a>\n</div>\n</td>\n";
			}
	
			if($messagesml == "")
			{
				$postdata .= "<td id=\"m{$pm['pmid']}\">\n<div align=\"center\">$messagesml</div>\n</td>\n";
			}
			else
			{
				$postdata .= "<td id=\"m{$pm['pmid']}\">\n<div align=\"center\">\n<a id=\"am{$pm['pmid']}\" title=\"$fullmessage\" href=\"pmadmin.php".($this->parse_http_query("id", $pm['pmid']))."\">$messagesml</a>\n</div>\n</td>\n";
			}
	
			if($fromid == "-2")
			{
				$postdata .= "<td>\n<div align=\"center\">\n<small>$fromid</small>\n</div>\n</td>\n";
			}
			else
			{
				$postdata .= "<td>\n<div align=\"center\">\n<a href=\"javascript:;\" onclick=\"PMA.profile(".$pm['fromid'].");\">$fromid</a>\n</div>\n</td>\n";
			}
	
			if($toid == "-2")
			{
				$postdata .= "<td>\n<div align=\"center\">\n<small>$toid</small>\n</div>\n</td>\n";
			}
			else
			{
				$postdata .= "<td>\n<div align=\"center\">\n<a href=\"javascript:;\" onclick=\"PMA.profile(".$pm['toid'].");\">$toid</a>\n</div>\n</td>\n";
			}
	
			$postdata .= "<td>\n<div align=\"center\">".my_date($mybb->settings['dateformat'], $pm['dateline'])."</div>\n</td>\n";
	
			if(isset($this->input['advanced']) && $this->input['advanced'] == "true")
			{
				$includesig = ($pm['includesig'] == "1") ? "yes" : "no";
				$smilieson = ($pm['smilieson'] == "no") ? "yes" : "no";
	
	
				if($pm['receipt'] == "0")
				{
					$reciept = $lang->pma_reciept_status_1;
				}
				elseif($pm['receipt'] == "1")
				{
					$receipt = $lang->pma_reciept_status_2;
				}
				elseif($pm['receipt'] == "2")
				{
					$receipt = $lang->pma_reciept_status_3;
				}
				else
				{
					$receipt = $pm['receipt'];
				}
	
				$readtime = ($pm['readtime'] != "0" && $pm['readtime'] != "") ? my_date($mybb->settings['dateformat'], $pm['readtime']) : "";
	
	
				if($pm['status'] == "0")
				{
					$status = $lang->pma_pm_status_1;
				}
				elseif($pm['status'] == "1")
				{
					$status = $lang->pma_pm_status_2;
				}
				elseif($pm['status'] == "2")
				{
					$status = $lang->pma_pm_status_3;
				}
				elseif($pm['status'] == "3")
				{
					$status = $lang->pma_pm_status_4;
				}
				else
				{
					$status = $lang->pma_pm_status_5;
				}
	
				$postdata .= "<td>\n<div align=\"center\">$status</div>\n</td>\n<td>\n<div align=\"center\">$includesig</div>\n</td>\n<td>\n<div align=\"center\">$smilieson</div>\n</td>\n<td>\n<div align=\"center\">$receipt</div>\n</td>\n<td>\n<div align=\"center\">$readtime</div>\n</td>\n</tr>\n";
			}
		}
	
		echo $postdata;
		endtable();
		endform();
	
		if($this->input['userid'] == "" && $this->input['useridto'] == "")
		{
	
			echo "<div align=\"center\">
				  <form name=\"search\" action=\"pmadmin.php?".SID."\" method=\"post\" onsubmit=\"return form_validate(this);\">
				  ".$lang->pma_search_text."
				  <input type=\"text\" name=\"user\" />
				  <input type=\"submit\" value=\"Submit\" />
				  </form>
				  </div>
				 ";
	
			if($this->input['orderby'] == "asc")
			{
				$savemin = $this->input['min'];
				$cnt = PMCOUNT - $this->input['min'];
				$this->input['min'] = PMCOUNT - $this->input['min'] - 20;
				$allmax = PMCOUNT - 20;
	
				if($savemin >= 20)
				{
					$lmin = $mincnt - 20;
					echo "<strong><a href=\"pmadmin.php".($this->parse_http_query("min", "0"))."\">&lt; </a></strong>  <a href=\"pmadmin.php".($this->parse_http_query("min", $lmin))."\">&lt; ".$lang->pma_previous."</a>\r\n";
				}
	
				if($savemin < $allmax)
				{
					$min = $mincnt + 20;
					echo "<span style=\"float: right\"><a href=\"pmadmin.php".($this->parse_http_query("min", $min))."\">".$lang->pma_next." &gt;</a>  <strong><a href=\"pmadmin.php".($this->parse_http_query("min", $allmax))."\"> &gt;</a></strong></span>\r\n";
				}
			}
			else
			{
				if($this->input['min'] >= 20)
				{
					$lmin = $this->input['min'] - 20;
					echo "<strong><a href=\"pmadmin.php".($this->parse_http_query("min", "0"))."\">&lt; </a></strong>  <a href=\"pmadmin.php".($this->parse_http_query("min", $lmin))."\">&lt; ".$lang->pma_previous."</a>\r\n";
				}
	
				if($this->input['min'] < $cnt)
				{
					$min = $this->input['min'] + 20;
					echo "<span style=\"float: right\"><a href=\"pmadmin.php".($this->parse_http_query("min", $min))."\">".$lang->pma_next." &gt;</a>  <strong><a href=\"pmadmin.php".($this->parse_http_query("min", $cnt))."\"> &gt;</a></strong></span>\r\n";
				}
			}
		}
	}

	function view()
	{
		global $db, $mybb, $lang, $bgcolor, $parser;

		$id = $this->input['id'];
		$table = ($this->input['action'] == "archive") ? "pmarchive" : "privatemessages";
		
		$count = $db->simple_select(TABLE_PREFIX.$table, "COUNT(*) as rows");
		$pms = $db->simple_select(TABLE_PREFIX.$table, "COUNT(*) as rows", "pmid='".intval($id)."'");

		$num = $db->fetch_field($count, "rows");
		$pmscount = $db->fetch_field($pms, "rows");
		$pms = $db->simple_select(TABLE_PREFIX.$table, "*", "pmid='".intval($id)."'");

		if($pmscount <= 0)
		{
			cpheader("", 0);
			starttable("65%");
			tableheader($lang->pma_error);
			makelabelcode("<div align=\"center\"> ".$this->sprintc("pma_view_notfound", $id)." </div> <span align=\"right\"><b><a href=\"pmadmin.php".($this->parse_http_query("id", ""))."\">".$lang->x."</a></b></span>");
			endtable();
		}
		else
		{
			$pm = $db->fetch_array($pms);
			$toid = $this->parseoutput($this->getuser($pm['toid']));
			$fromid = $this->parseoutput($this->getuser($pm['fromid']));

			$message = $parser->parse_badwords($pm['message']);
			$subject = $parser->parse_badwords($pm['subject']);

			$smilies = $db->simple_select(TABLE_PREFIX."icons", "*", "iid='".intval($pm['icon'])."'");
			$smilierow = $db->fetch_array($smilies);
			$smilie = $smilierow['path'];

			if($smilie != "" && file_exists(MYBB_ROOT.$smilie))
			{
				$smiliepath = "<img src=\"../".$smilie."\">";
			}
			else
			{
				$smiliepath = "";
			}

			if($toid == "")
			{
				$toid = "<strike>".$lang->missing."</strike>";
			}
			else
			{
				$toid = "<a href=\"pmadmin.php?".SID."&amp;action=profile&amp;uid=".$pm['toid']."\" onclick=\"window.open('pmadmin.php?".SID."amp;action=profile&amp;uid=". $pm['toid'] ."','".$lang->profile."','width=900,height=500,toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,copyhistory=no,resizable=yes'); return false;\" target=\"_blank\" >".$toid."</a>";
			}

			if($fromid == "")
			{
				$fromid = "<strike>".$lang->missing."</strike>";
			}
			else
			{
				$fromid = "<a href=\"pmadmin.php?".SID."&amp;action=profile&amp;uid=".$pm['fromid']."\" onclick=\"window.open('pmadmin.php?".SID."&amp;action=profile&amp;uid=".$pm['fromid']."','".$lang->profile."','width=900,height=500,toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,copyhistory=no,resizable=yes'); return false;\"  target=\"_blank\" >".$fromid."</a>";
			}

			$optionsarray = array(
				'allow_html' => $mybb->settings['pmaallowhtml'],
				'allow_smilies' => $mybb->settings['pmaallowsmilies'],
				'allow_imgcode' => $mybb->settings['pmaallowimgcode'],
				'filter_badwords' => $mybb->settings['pmaallowbadwords'],
				'allow_mycode' => $mybb->settings['pmaallowmycode']
			);

			starttable();
			tableheader($this->sprintc("pma_view_header", array($this->input['id'], $num))." <span align=\"right\"><b><a href=\"pmadmin.php".($this->parse_http_query("id", ""))."\">".$lang->x."</a></b></span>", "", "4");
			print "<tr><th class=\"subheader\" width=\"200\"><b>".$lang->pma_subject_header."</b></th><th class=\"subheader\" width=\"400\"><b>".$lang->pma_message_header."</b></th><th class=\"subheader\" width=\"100\"><b>".$lang->pma_from_header."</b></th><th class=\"subheader\" width=\"100\"><b>".$lang->pma_to_header."<br /></b></th></tr>\n";
			print "<tr><th class=\"$bgcolor\" width=\"200\">".$smiliepath." ".$this->parseoutput($subject)."</th><th class=\"$bgcolor\" width=\"400\">".stripslashes($parser->parse_message($message, $optionsarray))."</th><th class=\"$bgcolor\" width=\"100\">".$fromid."</th><th class=\"$bgcolor\" width=\"100\">".$toid."</th></tr>";
			endtable();
		}
	}

	function get_hopto_menu($disabled=false)
	{
		global $lang;

		if((isset($this->input['userid']) && $this->input['userid'] != "") || isset($this->input['prunepms']) || isset($this->input['prunepms2']) || isset($this->input['do_prunepms']) || (isset($this->input['useridto']) && $this->input['useridto'] != "") || isset($this->input['user']) || isset($this->input['profileuid']))
		{
			return "";
		}
		
		if($disabled == true)
		{
			$disabled = " disabled=\"disabled\"";
		}
		else
		{
			$disabled = "";
		}
		
		ob_start();
		
		if(!isset($this->input['useridto']) && !isset($this->input['userid']))
		{
			if(isset($this->input['advanced']) && $this->input['advanced'] == "true")
			{
				$hopto[] = "<input type=\"button\" value=\"$lang->dont_show_advanced_statistics\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("advanced", ""))."');\" class=\"hoptobutton\"$disabled />";
			}
			else
			{
				$hopto[] = "<input type=\"button\" value=\"$lang->show_advanced_statistics\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("advanced", "true"))."');\" class=\"hoptobutton\"$disabled />";
			}
		}
		
		if(isset($this->input['action']) && $this->input['action'] == "archive")
		{
			$hopto[] = "<input type=\"button\" value=\"$lang->show_pmnormal\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("action", ""))."');\" class=\"hoptobutton\" />";
		}
		else
		{
			$hopto[] = "<input type=\"button\" value=\"$lang->show_pmarchive\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("action", "archive"))."');\" class=\"hoptobutton\" />";
		}

		if(!isset($this->input['useridto']) && !isset($this->input['userid']))
		{
			if(isset($this->input['unique']) && $this->input['unique'] == "true" && !isset($this->input['useridto']) && !isset($this->input['userid']))
			{
				$hopto[] = "<input type=\"button\" value=\"$lang->show_duplicate_pms\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("unique", ""))."');\" class=\"hoptobutton\"$disabled />";
			}
			else
			{
				$hopto[] = "<input type=\"button\" value=\"$lang->dont_show_duplicate_pms\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("unique", "true"))."');\" class=\"hoptobutton\"$disabled />";
			}
		}
		
		if($this->input['action'] != "prunepms" && $this->input['action'] != "do_prunepms" && $this->input['action'] != "prunepms2")
		{
			$hopto[] = "<input type=\"button\" value=\"$lang->pruning\" onclick=\"hopto('pmadmin.php".($this->parse_http_query("action", "prunepms"))."');\" class=\"hoptobutton\"$disabled />";
		}
		
		makehoptolinks($hopto);
		
		$postdata = ob_get_contents();
		ob_end_clean();

		return $postdata;
	}

	function profile()
	{
		global $db, $lang, $parser;

		$username = $this->getuser($this->input['uid']);

		// Lets start displaying our content!
		starttable();
		tableheader($this->sprintc("profile_header", $username), "", "17");

		// Lets get the users profile
		$user = $db->simple_select(TABLE_PREFIX."users", "*", "uid = '".$this->input['uid']."'", array('limit' => 1));
		$profile = $db->fetch_array($user);

		// Lets count the total number of pm's
		$usergroup = $db->simple_select(TABLE_PREFIX."usergroups", "*", "gid='".$profile['usergroup']."'");

		$bgcolor = getaltbg();

		// Does our user have an avatar?
		if($profile['avatar'] == "")
		{
			$profileav = $lang->no_avatar;
		}
		else
		{
			// Link fix for the users avatar
			if(!preg_match("/^(http:\/\/)/i", $profile['avatar']))
			{
				$profileav = str_replace(array("./", "../"), "", $profile['avatar']);
				$profileav = MYBB_ROOT.$profileav;
			}
			else
			{
				$profileav = $profile['avatar'];
			}
			$profileav = "<img src=\"".$profileav."\"></img>";
		}

		// Has our user ever visited?
		if($profile['lastvisit'] == "0" || $profile['lastvisit'] == "")
		{
			$profile['lastvisit'] = $lang->never_visited;
		}
		else
		{
			$profile['lastvisit'] = my_date($mybb->settings['dateformat'], $profile['lastvisit']);
		}

		// Has our user ever posted?
		if($profile['lastpost'] == "0" || $profile['lastpost'] == "")
		{
			$profile['lastpost'] = $lang->never_posted;
		}
		else
		{
			$profile['lastpost'] = my_date($mybb->settings['dateformat'], $profile['lastpost']);
		}

		// The table, we shall print! & Lets send out the data!
		echo $lang->profile_table_heading_1."<tr><th class=\"$bgcolor\" width=\"20%\">".$profileav."</th><th class=\"$bgcolor\" width=\"20%\">".$db->fetch_field($usergroup, 'title')."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['uid']."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['postnum']."</th><th class=\"$bgcolor\" width=\"20%\"><a href=\"mailto:".$profile['email']."\" target=\"_blank\">".$profile['email']."</a></th><th class=\"$bgcolor\" width=\"20%\">".$profile['usertitle']."</th>";
		echo "<th class=\"$bgcolor\" width=\"20%\">".my_date($mybb->settings['dateformat'],$profile['regdate'])."</th><th class=\"$bgcolor\" width=\"20%\">".my_date($mybb->settings['dateformat'],$profile['lastactive'])."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['lastvisit']."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['lastpost']."</th></tr>";
		endtable();

		if($profile['website'] != "" || $profile['msn'] != "" || $profile['icq'] != "" || $profile['yahoo'] != "" || $profile['birthday'] != "")
		{
			// New Row....Makes it look better on smaller windows
			starttable();
			echo $lang->profile_table_heading_2."<tr><th class=\"$bgcolor\" width=\"20%\"><a href=".$profile['website'].">".$profile['website']."</a></th><th class=\"$bgcolor\" width=\"20%\">".$profile['icq']."</th>";
			echo "<th class=\"$bgcolor\" width=\"20%\">".$profile['aim']."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['msn']."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['yahoo']."</th><th class=\"$bgcolor\" width=\"20%\">".$profile['birthday']."</th></tr>";
			endtable();
		}

		if($profile['signature'] != "")
		{
			// Special row for our signature, because it may become really long
			starttable();
			echo $lang->profile_table_heading_3."<tr><th class=\"$bgcolor\" width=\"20%\">".$parser->parse_message($profile['signature'])."</th></tr>";
			endtable();
			echo '<br /><div align="center"><input type="button" value="'.$lang->close_window.'" onclick="window.close()"></div>';
		}
	}


	function sprintc($string="", $replacement="", $patterns='%~1~%')
	{
		global $lang;

		if($string == "" || $replacement == "")
		{	
			return $lang->missingstrreplacement;
		}

		if(is_array($replacement))
		{
			$i = 0;
			$patterns = array();
			foreach($replacement as $replace)
			{
				$i++;
				$patterns[$i] = "%~".$i."~%";
			}
		}

		return preg_replace($patterns, $replacement, $lang->$string);
	}

	function get_javascript()
	{
		global $mybb, $lang;

		header('Content-type: text/javascript');

		$js = "var PMA = {
		
	init: function()
	{
	},
	
	archive: function(pid)
	{
		var confirm_archive = confirm(archive_confirm+' ('+pid+')');
		if(confirm_archive)
		{
			if(doajax == \"on\")
			{
				PMA.loadPMAAjax(\"pmadmin.php?".SID."&archivepid=\"+pid, pid);
			}
			else
			{
				window.location = \"pmadmin.php?".SID."&archivepid=\"+pid;
			}
		}
		return true;
	},
	
	unarchive: function(pid)
	{
		var confirm_unarchive = confirm(unarchive_confirm+\" (\"+pid+\")\");
		if(confirm_unarchive)
		{
			if(doajax == \"on\")
			{
				PMA.loadPMAAjax(\"pmadmin.php?".SID."&unarchivepid=\"+pid, pid);
			}
			else
			{
				window.location = \"pmadmin.php?".SID."&unarchivepid=\"+pid;
			}
		}
		return true;
	},
	
	deletepm: function(pid)
	{
		var confirm_delete = confirm(delete_confirm+\" (\"+pid+\")\");
		if(confirm_delete)
		{
			if(doajax == \"on\")
			{
				PMA.loadPMAAjax(\"pmadmin.php?".SID."&deletepid=\"+pid, pid);
			}
			else
			{
				window.location = \"pmadmin.php?".SID."&deletepid=\"+pid;
			}
		}
		return true;
	},
	
	loadPMAAjax: function(url, pid)
	{
		this.spinner = new ActivityIndicator(\"body\", {image: \"../images/spinner_big.gif\"});
		new ajax(url, {method: 'get', onComplete: function(request) {PMA.PMAAjaxLoaded(request, pid); }});		
	},
	
	PMAAjaxLoaded: function(request, pid)
	{
		if(request.responseText.match(/<error>(.*)<\/error>/))
		{
			message = request.responseText.match(/<error>(.*)<\/error>/);
			if(!message[1])
			{
				message[1] = 'An unknown error occurred.';
			}
			alert(error_request+message[1]+request.status);
		}
		else if(request.responseText)
		{
			Element.remove('column-'+pid);
		}
		
		this.spinner.destroy();	
		this.spinner = \"\";
	},
	
	profile: function(uid)
	{
		MyBB.popupWindow('pmadmin.php?".SID."&action=profile&uid='+uid, profile, 900, 500);
	}
}

Event.observe(window, 'load', PMA.init);";

		return $js;
	}

	function get_css()
	{
		header('Content-type: text/css');
echo '
tr.odd
{
	background-color: #EEEEEE;
}

tr.even
{
	background-color: #F3F3F3;
}

#green
{
	color: green;
}

#dropmenudiv
{
	position: absolute;
	border: 1px solid black;
	border-bottom-width: 0;
	font: normal 12px Verdana;
	line-height: 18px;
	z-index: 100;
}

#dropmenudiv a
{
	width: 100%;
	display: block;
	text-indent: 3px;
	border-bottom: 1px solid black;
	padding: 1px 0;
	text-decoration: none;
	font-weight: bold;
}

#dropmenudiv a:hover
{
	background-color: lightyellow;
}

/* Notice Elements */
.alert
{
	background: #FFF6BF;
	border-top: 2px solid #FFD324;
	border-bottom: 2px solid #FFD324;
	text-align: center;
	margin: 10px auto;
	padding: 5px 20px;
	width: 100%;
}

.notice
{
	background: #f7f7f7;
	border-top: 2px solid #CCCCCC;
	border-bottom: 2px solid #CCCCCC;
	text-align: center;
	margin: 10px auto;
	padding: 5px 20px;
	width: 100%;
}

.important
{
	background: #FFCCCC;
	border-top: 2px solid #990000;
	border-bottom: 2px solid #990000;
	text-align: center;
	color: #000000;
	margin: 10px auto;
	padding: 5px 20px;
	width: 100%;
}
.popup_menu {
	background: #ccc;
	border: 1px solid #000;
}
.popup_menu .popup_item {
	background: #fff;
	color: #000;
}
.popup_menu .popup_item:hover {
	background: #C7DBEE;
	color: #000;
}
.popup_menu .popup_item_container {
	margin: 1px;
	text-align: left;
}

.popup_menu .popup_item {
	display: block;
	padding: 3px;
	text-decoration: none;
	white-space: nowrap;
}

.popup_menu a.popup_item:hover {
	text-decoration: none;
}

.quote_header {
	background: #adcbe7;
	border: 1px solid #81A2C4;
	color: #000000;
	font-weight: bold;
	margin: 4px;
	margin-left: 0px;
	margin-bottom:0px;
	padding: 4px;
}

.quote_body {
	background: #ffffff;
	border: 1px solid #81A2C4;
	border-top: 0px;
	color: #000;
	padding: 4px;
	margin: 4px;
	margin-left: 0px;
	margin-top: 0px;
}
';
	}
	
	function parse_http_query($table, $hash, $options=array())// $do="true",
	{
		// $temp as to not overwrite it
		$temp_input = $this->input;

		if(!is_array($options) || count($options) < 1)
		{
			$options['do'] = "true";

			if(empty($options['?']))
			{
				$options['?'] = "";
			}
		}

		if($table != "" && $hash != "" && $options['do'] == "true")
		{

			// Add the table and hash to the url
			if(is_array($table) && is_array($hash))
			{
				foreach($table as $table1 => $tvalue)
				{
					$temp_input[$tvalue] = $hash[$count];
				}
			}
			else
			{
				$temp_input[$table] = $hash;
			}
		}

		$count = count($this->input);

		if($hash == "" && $count > 0)
		{
			unset($temp_input[$table]);
		} 

		if(version_compare(phpversion(), "5.0.0", ">="))
		{
			$handler = http_build_query($temp_input);
		}
		else
		{
			$handlers = array();

			// Build the url in a browser safe format
			foreach($temp_input as $query => $value)
			{
				if(preg_match("/".$query."/i", PHP_HEADERS) || $query == $table)
				{
					$handlers[] = $query."=".$value;
				}
			}
			$handler = implode($handlers, "&");
		}

		unset($temp_input);
		if(isset($options['?']) && count($handler) > 0)
		{
			$handler = "?".SID."&amp;".$handler;
		}
		return $handler;
	}

	function slimtext($text, $type)
	{
		global $mybb;

		if($mybb->settings['subjectcutoff'] == "")
		{
			return $text;
		}

		if($type == "subject")
		{
			if(my_strlen($text) >= intval($mybb->settings['subjectcutoff']))
			{
				return my_substr($text, 0, intval($mybb->settings['subjectcutoff'])+1)."...";
			}
		}
		elseif($type == "message")
		{
			if(my_strlen($text) >= intval($mybb->settings['messagecutoff']))
			{
				return my_substr($text, 0, intval($mybb->settings['messagecutoff'])+1)."...";
			}
		}

		return $text;
	}

	function select_css($type='css')
	{
		global $db;

		$query = $db->simple_select(TABLE_PREFIX."themes", "*", "def='1'");
		$theme = $db->fetch_array($query);

		return $theme[$type];
	}

	function parseoutput($message, $options=array())
	{
		/*
		if($options['intval'] == "true")
		{
			$message = intval($message);
		}

		if($options['stripslashes'] == "true")
		{
			$message = stripslashes($message);
		}
		elseif($options['addslashes'] == "true")
		{
			$message = $db->escape_string($message);
		}
		elseif($options['stripslashes_array'] == "true")
		{
		}
		elseif($options['addslashes_array'] == "true")
		{
		}

		if($options['htmlspecialchars_uni'] == "true")
		{
			$message = htmlspecialchars_uni($message);
		}
		elseif($options['htmlspecialchars'] == "true")
		{
			$message = htmlspecialchars($message);
		}
		elseif($options['htmlspecialchars_array'] == "true")
		{
		}
		elseif($options['htmlentities'] == "true")
		{
		}
		elseif($options['htmlentities_array'] == "true")
		{
		}

		*/

		$message = stripslashes($message);
		$message = htmlspecialchars_uni($message);
		return $message;
	}

	function getuser($uid)
	{		
		$user = get_user($uid);
		return $user['username'];
	}

	function getid($user)
	{
		global $db;

		$users = $db->simple_select(TABLE_PREFIX."users", "*", "username='".$user."'", array('limit' => 1));
		if($db->num_rows($users) <= 0)
		{
			$user = "-2";
		}
		else
		{
			$row = $db->fetch_array($users);
			$user = $row['uid'];
		}		

		return $user;
	}

	function developer()
	{
		if(isset($_GET['developver']))
		{
			echo "<b>Version Code:</b> ".$this->developver."<br /> <b>Version:</b> ".$this->internalver."<br /> <b>Type:</b> ".$this->type;
			exit;
		}
	}

	function show_css()
	{
		if(isset($_GET['get_css']))
		{
			echo $this->get_css();
			exit;
		}
	}

	function check_status()
	{
		global $mybb, $lang;
		if(!isset($mybb->settings['pmaswitch']))
		{
			cperror("<div align=\"center\">".$lang->pma_uninstalled."</div>", $lang->pma_message);
			exit;
		}
		elseif($mybb->settings['pmaswitch'] == "off")
		{
			cperror("<div align=\"center\">".$lang->pma_off."</div>", $lang->pma_message);
			exit;
		}

	}

	function show_javascript()
	{
		if(isset($_GET['get_javascript']))
		{
			echo $this->get_javascript();
			exit;
		}
	}

	function check_reported_pms()
	{
		global $lang, $mybb, $db;

		if($db->table_exists(TABLE_PREFIX."reportedpms") === TRUE)
		{
			$newreports = $db->simple_select(TABLE_PREFIX."reportedpms", "*", "status='1'");
			$newreports_rows = $db->num_rows($newreports);

			if($newreports_rows > 0)
			{
				$pmtext = ($newreports_rows == 1) ? $lang->private_message : $lang->private_messages;
				$is_are = ($newreports_rows == 1) ? $lang->is : $lang->are;

				echo '
				<div align="center">
				<table border="0" cellspacing="1" cellpadding="4" class="important">
				<tbody>
				<tr>
				<td>
				<div align="center">
				<b>';

				printf($lang->reported_messages_new, $is_are, $mybb->settings['bburl'], $newreports_rows, $pmtext);

				echo '
				</b>
				</div>
				</td>
				</tr>
				</tbody>
				</table>
				</div>
				<br />';
			}
		}
	}

	function check_redirect()
	{
		if(isset($_GET['redirect']) && $_GET['redirect'] != "")
		{
			$redirect = str_replace($_SERVER['QUERY_STRING'], $this->parse_http_query("", "", array('do' => 'false')), $_GET['redirect']);
			if(!headers_sent())
			{
				header("Location: $redirect");
				exit;
			}
			else
			{
				echo "<meta HTTP-EQUIV=\"REFRESH\" content=\"0; url=".$redirect."\">";
				exit;
			}
		}
	}

	function delete($pid)
	{
		global $db, $lang;
		
		if(strstr($_SERVER['HTTP_REFERER'], 'action=archive'))
		{
			$table = "pmarchive";
		}
		else
		{
			$table = "privatemessages";
		}
		$db->delete_query(TABLE_PREFIX.$table, "pmid='".intval($pid)."'", '1');
		
		if($_SERVER['HTTP_REFERER'])
		{
			cpredirect($_SERVER['HTTP_REFERER'], "<div align=\"center\">".$lang->pma_deleted_success."</div>");
		}
		else
		{
			cpmessage($lang->pma_deleted_success);
		}
		exit;
		return true;
	}

	function archive($pid)
	{
		global $db, $lang;
		
		$pid = intval($pid);
		$pidrow = $db->simple_select(TABLE_PREFIX."privatemessages", "*", "pmid = '{$pid}'", array('limit' => '1'));
		$pidrow = $db->fetch_array($pidrow);
		$pidrow['message'] = $db->escape_string($pidrow['message']);
		$pidrow['subject'] = $db->escape_string($pidrow['subject']);

		$db->insert_query(TABLE_PREFIX."pmarchive", $pidrow);
		$db->delete_query(TABLE_PREFIX."privatemessages", "pmid = '{$pid}'", '1');
		cpredirect($_SERVER['HTTP_REFERER'], "<div align=\"center\">".$lang->pma_archived_success."</div>");
		exit;
		return true;
	}

	function unarchive($pid)
	{
		global $db, $lang;
		
		$pid = intval($pid);
		$pidrow = $db->simple_select(TABLE_PREFIX."pmarchive", "*", "pmid = '{$pid}'", array('limit' => '1'));
		$pidrow = $db->fetch_array($pidrow);
		$pidrow['message'] = $db->escape_string($pidrow['message']);
		$pidrow['subject'] = $db->escape_string($pidrow['subject']);

		$db->insert_query(TABLE_PREFIX."privatemessages", $pidrow);
		$db->delete_query(TABLE_PREFIX."pmarchive", "pmid = '{$pid}'", '1');
		cpredirect($_SERVER['HTTP_REFERER'], "<div align=\"center\">".$lang->pma_unarchived_success."</div>");
		exit;
		return true;
	}
	
	function prune_pms()
	{	
		global $db, $mybb;

		$db->delete_query(TABLE_PREFIX."privatemessages", "dateline < '".(time() - ((60*60*24)*$mybb->settings['pmaprunedays']))."'");
		return $db->affected_rows();
	}
}

?>