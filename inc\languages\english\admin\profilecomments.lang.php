<?php

/**
 * ProfileComments v0.9.2 English Language Pack by Santiago Dimattia
 * http://www.teleportz.com.ar
 */

/***************** New in v0.9.2 *****************/

// ACP
$l['profile_comments_ignored_can_send_comments'] = 'Ignored users can send comments?';
$l['profile_comments_ignored_can_send_comments_info'] = 'If <b>User A</b> has <b>User B</b> in his ignore list, can <b>User B</b> send comments to <b>User A</b>?';

// Reasons
$l['pf_reason_you_are_being_ignored'] = 'the user has you in his/her ignored list';

/***************** Updated in v0.9.2 *****************/

// Reasons
$l['pf_reason_message'] = 'You can\'t send a comment to this user because {1}.';

// UCP
$l['who_can_leave_comments'] = 'Who is allowed to leave comments on my profile?';

/***************** New in v0.9.0 *****************/

// ACP
$l['profile_comments_close_on_banned'] = 'Close comments submission on banned users profile?';
$l['profile_comments_close_on_banned_info'] = '';
$l['profilecomments_show_statistics'] = 'Show comments count on user statistics?';
$l['profilecomments_show_statistics_info'] = '';
$l['profilecomments_ajax'] = 'Send comments via AJAX? (If supported)';
$l['profilecomments_ajax_info'] = '';

// User Control Panel
$l['comment_notify'] = 'Notify me by email when I receive a new comment on my profile.';
$l['anyone_can_leave_comments'] = 'Anyone';
$l['only_friends_can_leave_comments'] = 'Only friends';
$l['nobody_can_leave_comments'] = 'Nobody';

// Messages
$l['profilecomments_no_permission'] = 'You don\'t have permissions to do that!';
$l['profilecomments_invalid_uid'] = 'Invalid User ID';
$l['profilecomments_replying'] = 'You are replying to {1}. <a href="#" id="linkcancel">Cancel</a>.';
$l['profilecomments_confirm_delete'] = 'Are you sure you want to delete the comment?';

// New comment notification: Email content
$l['comment_notify_subject'] = 'New comment at {1}';
$l['comment_notify_message'] = '{1},

You have received a new comment on your profile from {2}. You can read it by entering the URL below in your web browser.

{4}/member.php?action=profile&uid={5}

Please note that you will not receive additional emails until you visit your profile.

Thank you,
{3} Staff';

// Reasons
$l['pf_reason_user_is_banned'] = 'the user is banned';
$l['pf_reason_nobody_can_comment'] = 'the user has closed all comments';
$l['pf_reason_only_friends_can_comment'] = 'the user only accept comments from his/her buddys';
$l['pf_reason_you_cant_comment'] = 'your usergroup can\'t send comments';
$l['pf_reason_logged_out'] = 'you are logged out';

/***************** End of new translations *****************/

// AdminCP - Install
$l['profile_comments'] = "Profile Comments";
$l['profile_comments_info'] = "Comment on users profile without limit!";
$l['profile_comments_perpage'] = "Comments per page";
$l['profile_comments_perpage_info'] = "Number of comments per page (Pagination)";
$l['profile_comments_alertbar'] = "Alert bar";
$l['profile_comments_alertbar_info'] = "Show an alert bar when the user have a new message?";
$l['profile_comments_alertbar_text'] = "Alert bar text";
$l['profile_comments_alertbar_text_info'] = "The text you want to show in the alert bar. Use <strong>{1}</strong> to show the number of new comments.";
$l['profile_comments_alertbar_text_content'] = "You have {1} new comment(s) in your profile!";
$l['profile_comments_alertbar_style'] = "Alert Bar Style";
$l['profile_comments_alertbar_style_info'] = "The CSS Style for the Alert Bar.";
$l['profile_comments_default_avatar'] = 'Default Avatar';
$l['profile_comments_default_avatar_info'] = 'Image to show when the user dosn\'t have an avatar';
$l['profile_comments_allow_html'] = 'Allow HTML in comments?';
$l['profile_comments_allow_html_info'] = '';
$l['profile_comments_allow_smilies'] = 'Allow Smilies in comments?';
$l['profile_comments_allow_smilies_info'] = '';
$l['profile_comments_allow_bbcode'] = 'Allow BBCode in comments?';
$l['profile_comments_allow_bbcode_info'] = '';
$l['profile_comments_allow_imgcode'] = 'Allow [IMG] BBCode in comments?';
$l['profile_comments_allow_imgcode_info'] = '';
$l['profile_comments_filter_badwords'] = 'Filter badwords in comments?';
$l['profile_comments_filter_badwords_info'] = '';
$l['profile_comments_show_editor'] = 'Show BBCode editor?';
$l['profile_comments_show_editor_info'] = '';

// Admin Page
$l['profile_comments_can_manage'] = "Is an admin? (Can edit & delete all comments)";
$l['profile_comments_can_send'] = "Can send comments?";
$l['profile_comments_can_selfedit'] = "Can edit own comments?";
$l['profile_comments_can_selfdelete'] = "Can delete own comments?";

// Header - Alert
$l['profile_comments_new_inmenu'] = "Profile Comments";
$l['profile_comments_new_inmenu_count'] = "({1})";

// Member page
$l['profile_comments_list'] = "User Comments";
$l['profile_comments_add_button'] = "Add comment";
$l['profile_comments_edit_button'] = "Edit comment";
$l['profile_comments_editing'] = "Editing the comment #{1} made by {2}";
$l['profile_comments_received'] = 'Comments received:';
$l['profile_comments_given'] = 'Comments given:';

// Messages
$l['profile_comments_added'] = "The comment has been added.";
$l['profile_comments_none_found'] = "There is no comments to show :(";
$l['profile_comments_insert_comment'] = "You need to insert a comment.";
$l['profile_comments_invalid_id'] = "The Comment ID is not valid";
$l['profile_comments_not_edited'] = "An error has ocurred, the comment has not been edited";
$l['profile_comments_edited'] = "The comment has been edited.";
$l['profile_comments_deleted'] = "The comment has been deleted.";

// Member links options
$l['profile_comments_reply'] = "Reply";
$l['profile_comments_reply_to_profile'] = "Reply to Profile";
$l['profile_comments_delete_comment'] = "Delete comment";
$l['profile_comments_edit_comment'] = "Edit comment";
$l['profile_comments_edit'] = "Edit";
$l['profile_comments_delete'] = "Delete";

/* File end */