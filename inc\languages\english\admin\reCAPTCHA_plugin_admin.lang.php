<?php
	// MyBB reCAPTCHA plugin U.S. English language file.
	// (C) 2010 CubicleSoft.  All Rights Reserved.

	$l["reCAPTCHA_plugin_Name"] = "reCAPTCHA Plugin";
	$l["reCAPTCHA_plugin_Desc"] = "Implements reCAPTCHA support for MyBB.  Overrides the default CAPTCHA with reCAPTCHA.<br /><a href=\"https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=244381\" target=\"_blank\" title=\"Donations are highly appreciated and money goes toward further development.\">Donate $5 toward further development</a> - A great way to say thanks or place a feature request.";
	$l["reCAPTCHA_plugin_Author"] = "Thomas Hruska, CubicleSoft Core";

	$l["setting_group_reCAPTCHA_plugin"] = "reCAPTCHA Configuration";
	$l["setting_group_reCAPTCHA_plugin_desc"] = "Sets up the basic reCAPTCHA options";
	$l["setting_reCAPTCHA_PublicKey"] = "Public Key";
	$l["setting_reCAPTCHA_PublicKey_desc"] = "You get a public key when you <a href=\"https://www.google.com/recaptcha/admin/list\" target=\"_blank\">sign up for the reCAPTCHA service</a>.  reCAPTCHA will not work without a public key!";
	$l["setting_reCAPTCHA_PrivateKey"] = "Private Key";
	$l["setting_reCAPTCHA_PrivateKey_desc"] = "You get a private key when you <a href=\"https://www.google.com/recaptcha/admin/list\" target=\"_blank\">sign up for the reCAPTCHA service</a>.  reCAPTCHA will not work without a private key!";
	$l["setting_reCAPTCHA_Style"] = "Style";
	$l["setting_reCAPTCHA_Style_desc"] = "Select the style to use.  The default style works well with most themes.";
	$l["setting_reCAPTCHA_Style_red"] = "Red";
	$l["setting_reCAPTCHA_Style_white"] = "White";
	$l["setting_reCAPTCHA_Style_blackglass"] = "Black Glass";
	$l["setting_reCAPTCHA_Style_clean"] = "Clean";
?>