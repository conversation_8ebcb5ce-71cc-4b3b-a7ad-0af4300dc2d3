<?php 

$l['setting_group_simpleckeditor'] = 'Settings for Simple CKEditor';
$l['setting_group_simpleckeditor_desc'] = 'Settings for Simple CKEditor plugin';
$l['setting_simpleckeditor_skin'] = 'Skin';
$l['setting_simpleckeditor_skin_desc'] = 'If you want to get other skins, please go to <a href="http://ckeditor.com/addons/skins/all" target="_blank">CKEditor addones site</a> and download a skin, the upload it to "jscripts/ckeditor-plugins/skins" directory.';
$l['setting_simpleckeditor_toolbar'] = 'Toolbar';
$l['setting_simpleckeditor_toolbar_desc'] = 'Enter the JS array of config.toolbar configuration.
<br>
For more details please see <a href="http://docs.ckeditor.com" target="_blank">CKEditor docs sit</a>.
<style>
#setting_simpleckeditor_toolbar {
	direction: ltr;
	width: 100%;
	height: 200px;
	box-sizing: border-box;
}
</style>';

$l['setting_simpleckeditor_disallowed_pages'] = 'Disallowed pages';
$l['setting_simpleckeditor_disallowed_pages_desc'] = 'Enter the pages which you want to disallow CKEditor to load on it.<br>
seperate pages with "\n" ( new line).<br>
Exp:<br>
member.php<br>
member.php?action=profile';