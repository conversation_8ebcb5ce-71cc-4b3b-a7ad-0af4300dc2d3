<?php
/**
 * Staff Page English language pack
 * mrnu <<EMAIL>>
 *
 */

$l['staff'] = 'Staff';
$l['staff_page'] = 'Staff Page';
$l['no_members'] = 'No members found.';
$l['no_groups'] = 'No groups found.';

$l['staff_page_admin_permission'] = 'Can manage Staff Page';

$l['add_member'] = 'Add a member';
$l['add_group'] = 'Add a group';
$l['edit_member'] = 'Edit member';
$l['edit_group'] = 'Edit group';

$l['staff_page_description'] = 'Here you can see all of the groups and members displayed on the Staff Page.';
$l['add_member_description'] = 'Add a member to the Staff Page.';
$l['add_group_description'] = 'Add a group to the Staff Page';

$l['name'] = 'Name';
$l['description'] = 'Description';
$l['order'] = 'Order';
$l['action'] = 'Action';

$l['save'] = 'Save';

$l['empty_name'] = 'You must provide the name.';
$l['wrong_group'] = 'Choose group again.';

$l['group_deleted'] = 'Group has been deleted.';
$l['group_not_exist'] = 'Group does not exist.';
$l['do_you_want_to_delete_group'] = 'Do you want to delete "{1}"?';
$l['group_saved'] = 'Group has been updated.';

$l['user_not_exist'] = 'User does not exist.';
$l['do_you_want_to_delete_member'] = 'Do you want to member "{1}"?';
$l['member_deleted'] = 'Member has been deleted.';
$l['member_saved'] = 'Member has been updated.';
$l['add_group_first'] = 'First you need to add a group.';

$l['staff_page_settings'] = 'Staff Page plugin settings';
$l['avatar_size'] = 'Avatar size';
$l['avatar_size_description'] = 'What size of avatar will be displayed on the staff page. Format: widthxheight, Example: 100x100';
$l['enable_showteam_redirection'] = 'Redirect showteam.php to the staff page.';
$l['enable_showteam_redirection_description'] = 'You can switch off or switch on the redirection from showteam.php here.';

$l['can_see_staff_page'] = 'Can see Staff Page?';
