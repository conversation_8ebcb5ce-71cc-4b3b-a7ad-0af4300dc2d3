<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['template_sets'] = "Template Sets";
$l['template_set'] = "Template Set";
$l['templates'] = "{1} Templates";

$l['manage_template_sets'] = "Manage Template Sets";
$l['manage_template_sets_desc'] = "Here you can manage template sets, view the templates using these sets, and begin customizing your board's layout.";
$l['add_set'] = "Add Set";
$l['add_set_desc'] = "Here you can create a new template set. A new template set creates a copy of the master MyBB templates and allows you to edit them without interfering with changes in any other template sets you may already have";
$l['add_template'] = "Add Template";
$l['add_template_desc'] = "Here you can create a new template.";
$l['add_template_group'] = "Add Template Group";
$l['add_template_group_desc'] = "Add a template group to collate templates together.";
$l['search_replace'] = "Search/Replace";
$l['search_replace_desc'] = "This tool will allow you to search for template titles or templates that contain certain text, and optionally replace it with another text automatically.";
$l['find_updated'] = "Find Updated Templates";
$l['find_updated_desc'] = "Allows you to find changed templates in new versions of MyBB so you can properly apply the changes.";
$l['edit_template'] = "Edit Template";
$l['editing_template'] = "Editing Template: {1}";
$l['edit_template_desc'] = "Here you can edit the template's code, title or set.";
$l['edit_set'] = "Edit Set";
$l['edit_set_desc'] = "Here you can edit the properties for this template set.";
$l['manage_templates'] = "Manage Templates";
$l['manage_templates_desc'] = "Here you can manage the templates for this template set. To edit a template click on it's link or select \"Full Edit\" to edit all properties on a separate page.";
$l['diff_report'] = "Diff Report";
$l['diff_report_desc'] = "Performs a difference analysis between the templates and shows you exactly what changes have been made between your customized copy and the latest master copy.";

$l['title'] = "Title";
$l['save'] = "Save";
$l['search_for'] = "Search For";
$l['replace_with'] = "Replace With (Optional)";
$l['reset'] = "Reset";
$l['find_templates'] = "Find Templates";
$l['find_and_replace'] = "Find and Replace";
$l['search_template_names'] = "Search Template Titles";
$l['ungrouped_templates'] = "Ungrouped Templates";

$l['search_noneset'] = "You did not enter a search string.";
$l['search_results'] = "Template Search Results";
$l['search_header'] = "Searching For \"{1}\" in {2}";
$l['search_updated'] = "Updated {1}";
$l['search_found'] = "Found in {1}";
$l['search_created_custom'] = "Created custom template for {1}";
$l['search_edit'] = "edit";
$l['search_change_original'] = "change original";
$l['search_noresults'] = "No templates were found containing the string '<strong>{1}</strong>'";
$l['search_noresults_title'] = "No templates were found with the title '<strong>{1}</strong>'";
$l['default_templates'] = "Default Templates";

$l['edit_template_breadcrumb'] = "Edit Template: ";

$l['global_templates'] = "Global Templates";
$l['master_templates'] = "Master Templates";

$l['not_used_by_any_themes'] = "Not used by any themes";
$l['used_by'] = "Used by: ";
$l['used_by_all_themes'] = "Used by all themes";

$l['expand_templates'] = "Expand Templates";
$l['edit_template_set'] = "Edit Template Set";
$l['delete_template_set'] = "Delete Template Set";
$l['empty_template_set'] = "<em>There are no templates in this set.</em>";

$l['inline_edit'] = "Inline Edit";
$l['full_edit'] = "Full Edit";
$l['revert_to_orig'] = "Revert to Original";
$l['delete_template'] = "Delete Template";
$l['edit_in'] = "Edit in";

$l['group_calendar'] = "Calendar";
$l['group_forumdisplay'] = "Forum Display";
$l['group_index'] = "Index Page";
$l['group_error'] = "Error Message";
$l['group_memberlist'] = "Member List";
$l['group_multipage'] = "Multipage Pagination";
$l['group_private'] = "Private Messaging";
$l['group_portal'] = "Portal";
$l['group_postbit'] = "Post Bit";
$l['group_posticons'] = "Post Icon";
$l['group_showthread'] = "Show Thread";
$l['group_usercp'] = "User Control Panel";
$l['group_online'] = "Who's Online";
$l['group_forumbit'] = "Forum Bit";
$l['group_editpost'] = "Edit Post";
$l['group_forumjump'] = "Forum Jump";
$l['group_moderation'] = "Moderation";
$l['group_nav'] = "Navigation";
$l['group_search'] = "Search";
$l['group_showteam'] = "Show Forum Team";
$l['group_reputation'] = "Reputation";
$l['group_newthread'] = "New Thread";
$l['group_newreply'] = "New Reply";
$l['group_member'] = "Member";
$l['group_warning'] = "Warning System";
$l['group_global'] = "Global";
$l['group_header'] = "Header";
$l['group_managegroup'] = "Manage Group";
$l['group_misc'] = "Miscellaneous";
$l['group_modcp'] = "Moderator Control Panel";
$l['group_announcement'] = "Announcement";
$l['group_polls'] = "Poll";
$l['group_post'] = "Post";
$l['group_printthread'] = "Print Thread";
$l['group_report'] = "Report";
$l['group_smilieinsert'] = "Smilie Inserter";
$l['group_stats'] = "Statistics";
$l['group_xmlhttp'] = "XMLHTTP";
$l['group_footer'] = "Footer";
$l['group_video'] = "Video MyCode";
$l['group_sendthread'] = "Send Thread";
$l['group_mycode'] = "MyCode";

$l['expand'] = "Expand";
$l['collapse'] = "Collapse";

$l['save_continue'] = "Save and Continue Editing";
$l['save_close'] = "Save and Return to Listing";

$l['template_name'] = "Template Name";
$l['template_name_desc'] = "Name of the template. If you change this on the default template, it will save the template as a custom template under the new name.";
$l['template_set_desc'] = "Which template set should this template be in?";

$l['template_group_prefix'] = "Template Group Prefix";
$l['template_group_prefix_desc'] = "The prefix name to group templates. This must not already exist. For example, to group templates <em>hello_world</em>, <em>hello_foobar</em> and <em>hello_foo</em>, enter <strong>hello</strong> here.";
$l['template_group_title'] = "Template Group Title";
$l['template_group_title_desc'] = "The title of the prefix group. This will be shown in the templates list. For example, for our <em>hello</em> templates, enter <strong>Hello</strong> here.";

$l['edit_template_group'] = "Edit Template Group";
$l['editing_template_group'] = "Editing Template Group {1}";
$l['delete_template_group'] = "Delete Template Group";
$l['save_template_group'] = "Save Template Group";

$l['templates_the_same'] = "The two templates you've selected are both the same and cannot be compared.";
$l['master_updated_ins'] = "Changes that have been made between your previous version and this one are highlighted like this.";
$l['master_updated_del'] = "Any customizations you've made to your templates (the old ones) are highlighted like this.";
$l['template_diff_analysis'] = "Template Difference Analysis";
$l['search_names_header'] = "Searching template names containing \"{1}\"";

$l['updated_template_welcome1'] = "Edit - Allows you to edit the current template for this template set to incorporate updates made between the versions.";
$l['updated_template_welcome2'] = "Revert - Will revert the customized template back to the master revision, however you'll lose any custom changes you have made.";
$l['updated_template_welcome3'] = "Diff - Performs a difference analysis between the templates and shows you exactly what changes have been made between your customized copy and the latest master copy.";

$l['no_global_templates'] = "There are currently no global templates.";
$l['no_updated_templates'] = "There are currently no templates which have been updated since you last upgraded.";

$l['confirm_template_set_deletion'] = "Are you sure you want to delete this template set?";
$l['confirm_template_group_delete'] = "Are you sure you want to delete this template group? This action does not remove the templates in the group.";
$l['confirm_template_deletion'] = "Are you sure you want to delete this template?";
$l['confirm_template_revertion'] = "Are you sure you want to revert this template?";

$l['error_security_problem'] = "A potential security issue was found in the template. Please review your changes or contact the MyBB Group for support.";
$l['error_missing_input'] = "Please make sure you have all the input required to edit this template (tid and sid)";
$l['error_already_exists'] = "The template title is already in use. Please use a different title.";
$l['error_invalid_template'] = "Please select a valid template.";
$l['error_missing_set_title'] = "Please select a template set title.";
$l['error_invalid_input'] = "Please make sure you have the correct template set ID.";
$l['error_invalid_set'] = "Please select a valid template set.";
$l['error_invalid_template_set'] = "Invalid template set selected.";
$l['error_themes_attached_template_set'] = "This template set cannot be deleted as there are themes attached to this template set.";
$l['error_missing_group_prefix'] = "Please enter a prefix for the template group.";
$l['error_invalid_group_title'] = "As underscores (_) are used as delimiter those are forbidden in template group prefixes. Please select another prefix.";
$l['error_missing_group_title'] = "Please enter a title for the template group.";
$l['error_duplicate_group_prefix'] = "A template group already exists with this prefix. Please enter another prefix.";
$l['error_missing_template_group'] = "The template group could not be found.";
$l['error_default_template_group'] = "You cannot edit or remove a default template group.";

$l['success_template_saved'] = "The selected template has successfully been saved.";
$l['success_template_deleted'] = "The selected template has successfully been deleted.";
$l['success_template_reverted'] = "The selected template has successfully been reverted.";
$l['success_template_set_saved'] = "The selected template set has successfully been saved.";
$l['success_template_set_deleted'] = "The selected template set has successfully been deleted.";
$l['success_template_group_saved'] = "The selected template group has successfully been saved.";
$l['success_template_group_deleted'] = "The selected template group has successfully been deleted.";
