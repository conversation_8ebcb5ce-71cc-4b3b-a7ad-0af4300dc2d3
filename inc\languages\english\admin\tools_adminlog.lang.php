<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */


$l['admin_logs'] = "Administrator Logs";
$l['admin_logs_desc'] = "Here you can view, prune, and search the actions administrators have taken in the control panel.";
$l['prune_admin_logs'] = "Prune Administrator Logs";
$l['prune_admin_logs_desc'] = "Here you can prune the administrator logs matching a specified criteria.";

$l['no_adminlogs'] = "There are no log entries with the selected criteria.";

$l['username'] = "Username";
$l['date'] = "Date";
$l['information'] = "Information";
$l['ipaddress'] = "IP Address";

$l['filter_administrator_logs'] = "Filter Administrator Logs";
$l['administrator'] = "Administrator:";
$l['sort_by'] = "Sort By:";
$l['results_per_page'] = "Results Per Page:";
$l['all_administrators'] = "All Administrators";
$l['all_modules'] = "All Modules";
$l['older_than'] = "Older than ";
$l['days'] = "days";

$l['prune_administrator_logs'] = "Prune Administrator Logs";
$l['date_range'] = "Date range:";
$l['module'] = "Module:";

$l['asc'] = "Ascending";
$l['desc'] = "Descending";

$l['in'] = "in";
$l['order'] = "order";

$l['success_pruned_admin_logs'] = "The administrator logs have been pruned successfully.";
$l['note_logs_locked'] = "For security reasons, logs less than 24 hours old cannot be pruned.";

$l['admin_log_config_attachment_types_add'] = "Added attachment extension #{1} ({2})";
$l['admin_log_config_attachment_types_edit'] = "Edited attachment extension #{1} ({2})";
$l['admin_log_config_attachment_types_delete'] = "Deleted attachment extension #{1} ({2})";
$l['admin_log_config_attachment_types_toggle_status'] = "Toggled attachment status #{1} ({2})";

$l['admin_log_config_badwords_add'] = "Added word filter #{1} ({2})";
$l['admin_log_config_badwords_edit'] = "Edited word filter #{1} ({2})";
$l['admin_log_config_badwords_delete'] = "Deleted word filter #{1} ({2})";

$l['admin_log_config_banning_add_ip'] = "Added IP ban #{1} ({2})";
$l['admin_log_config_banning_add_username'] = "Added disallowed username #{1} ({2})";
$l['admin_log_config_banning_add_email'] = "Added disallowed email #{1} ({2})";
$l['admin_log_config_banning_delete_ip'] = "Removed IP ban #{1} ({2})";
$l['admin_log_config_banning_delete_username'] = "Removed disallowed username #{1} ({2})";
$l['admin_log_config_banning_delete_email'] = "Removed disallowed email #{1} ({2})";

$l['admin_log_config_calendars_add'] = "Added calendar #{1} ({2})";
$l['admin_log_config_calendars_permissions'] = "Modified permissions for calendar #{1} ({2})";
$l['admin_log_config_calendars_edit'] = "Edited calendar #{1} ({2})";
$l['admin_log_config_calendars_delete'] = "Deleted calendar #{1} ({2})";
$l['admin_log_config_calendars_update_order'] = "Updated calendar display order";

$l['admin_log_config_help_documents_add_section'] = "Added help section #{1} ({2})";
$l['admin_log_config_help_documents_add_document'] = "Added help document #{1} ({2})";
$l['admin_log_config_help_documents_edit_section'] = "Edited help section #{1} ({2})";
$l['admin_log_config_help_documents_edit_document'] = "Edited help document #{1} ({2})";
$l['admin_log_config_help_documents_delete_section'] = "Deleted help section #{1} ({2})";
$l['admin_log_config_help_documents_delete_document'] = "Deleted help document #{1} ({2})";

$l['admin_log_config_languages_edit_properties'] = "Edited properties for language pack: {1}";
$l['admin_log_config_languages_edit'] = "Edited {2} in language pack: {1}";
$l['admin_log_config_languages_edit_admin'] = "Edited admin/{2} in language pack: {1}";
$l['admin_log_config_languages_quick_phrases'] = "Edited quick phrases in language pack: {1}";

$l['admin_log_config_mod_tools_delete_post_tool'] = "Deleted post moderation tool #{1} ({2})";
$l['admin_log_config_mod_tools_delete_thread_tool'] = "Deleted thread moderation tool #{1} ({2})";
$l['admin_log_config_mod_tools_edit_post_tool'] = "Edited post moderation tool #{1} ({2})";
$l['admin_log_config_mod_tools_edit_thread_tool'] = "Edited thread moderation tool #{1} ({2})";
$l['admin_log_config_mod_tools_add_post_tool'] = "Added post moderation tool #{1} ({2})";
$l['admin_log_config_mod_tools_add_thread_tool'] = "Added thread moderation tool #{1} ({2})";

$l['admin_log_config_mycode_toggle_status_enabled'] = "Enabled custom MyCode #{1} ({2})";
$l['admin_log_config_mycode_toggle_status_disabled'] = "Disabled custom MyCode #{1} ({2})";
$l['admin_log_config_mycode_add'] = "Added custom MyCode #{1} ({2})";
$l['admin_log_config_mycode_edit'] = "Edited custom MyCode #{1} ({2})";
$l['admin_log_config_mycode_delete'] = "Deleted custom MyCode #{1} ({2})";

$l['admin_log_config_plugins_activate'] = "Activated plugin: {1}";
$l['admin_log_config_plugins_activate_install'] = "Activated and installed plugin: {1}";
$l['admin_log_config_plugins_deactivate'] = "Deactivated plugin: {1}";
$l['admin_log_config_plugins_deactivate_uninstall'] = "Deactivated and uninstalled plugin: {1}";

$l['admin_log_config_post_icons_add'] = "Added post icon #{1} ({2})";
$l['admin_log_config_post_icons_add_multiple'] = "Added multiple post icons";
$l['admin_log_config_post_icons_edit'] = "Edited post icon #{1} ({2})";
$l['admin_log_config_post_icons_delete'] = "Deleted post icon #{1} ({2})";

$l['admin_log_config_profile_fields_add'] = "Added custom profile field #{1} ({2})";
$l['admin_log_config_profile_fields_edit'] = "Edited custom profile field #{1} ({2})";
$l['admin_log_config_profile_fields_delete'] = "Deleted custom profile field #{1} ({2})";

$l['admin_log_config_questions_add'] = "Added question #{1} ({2})";
$l['admin_log_config_questions_edit'] = "Edited question #{1} ({2})";
$l['admin_log_config_questions_delete'] = "Deleted question #{1} ({2})";
$l['admin_log_config_questions_enable'] = "Enabled question #{1} ({2})";
$l['admin_log_config_questions_disable'] = "Disabled question #{1} ({2})";

$l['admin_log_config_report_reasons_add'] = "Added report reason #{1} ({2})";
$l['admin_log_config_report_reasons_edit'] = "Edited report reason #{1} ({2})";
$l['admin_log_config_report_reasons_delete'] = "Deleted report reason #{1} ({2})";

$l['admin_log_config_settings_delete_duplicates'] = "Deleted duplicate settings and setting groups";
$l['admin_log_config_settings_addgroup'] = "Added setting group #{1} ({2}) properties ";
$l['admin_log_config_settings_editgroup'] = "Edited setting group #{1} ({2}) properties ";
$l['admin_log_config_settings_deletegroup'] = "Deleted setting group #{1} ({2})";
$l['admin_log_config_settings_add'] = "Added setting #{1} ({2}) properties ";
$l['admin_log_config_settings_edit'] = "Edited setting #{1} ({2}) properties ";
$l['admin_log_config_settings_delete'] = "Deleted setting #{1} ({2})";
$l['admin_log_config_settings_manage'] = "Updated setting and setting group orders";
$l['admin_log_config_settings_change'] = "Changed board settings";

$l['admin_log_config_smilies_add'] = "Added smilie #{1} ({2})";
$l['admin_log_config_smilies_edit'] = "Edited smilie #{1} ({2})";
$l['admin_log_config_smilies_delete'] = "Deleted smilie #{1} ({2})";
$l['admin_log_config_smilies_add_multiple'] = "Added multiple smilies";
$l['admin_log_config_smilies_mass_edit'] = "Edited multiple smilies";

$l['admin_log_config_spiders_add'] = "Added spider #{1} ({2})";
$l['admin_log_config_spiders_edit'] = "Edited spider #{1} ({2})";
$l['admin_log_config_spiders_delete'] = "Deleted spider #{1} ({2})";

$l['admin_log_config_thread_prefixes_add_prefix'] = 'Added thread prefix #{1} ({2})';
$l['admin_log_config_thread_prefixes_edit_prefix'] = 'Edited thread prefix #{1} ({2})';
$l['admin_log_config_thread_prefixes_delete_prefix'] = 'Deleted thread prefix #{1} ({2})';

$l['admin_log_config_warning_add_level'] = "Added warning level #{1} at {2}%";
$l['admin_log_config_warning_edit_level'] = "Edited warning level #{1} at {2}%";
$l['admin_log_config_warning_delete_level'] = "Deleted warning level #{1} at {2}%";
$l['admin_log_config_warning_add_type'] = "Added warning type #{1} ({2})";
$l['admin_log_config_warning_edit_type'] = "Edited warning type #{1} ({2})";
$l['admin_log_config_warning_delete_type'] = "Deleted warning type #{1} ({2})";

$l['admin_log_forum_announcements_add'] = "Added announcement #{1} ({2})";
$l['admin_log_forum_announcements_edit'] = "Edited announcement #{1} ({2})";
$l['admin_log_forum_announcements_delete'] = "Deleted announcement #{1} ({2})";

$l['admin_log_forum_attachments_delete_post'] = "Deleted attachment #{1} ({2}) from post #{3}";
$l['admin_log_forum_attachments_delete'] = "Deleted attachment #{1} ({2})";
$l['admin_log_forum_attachments_delete_orphans'] = "Deleted orphaned attachments";

$l['admin_log_forum_management_copy'] = "Copied settings from forum #{1} ({2}) to forum #{3} ({4})";
$l['admin_log_forum_management_copy_with_permissions'] = "Copied settings and group permissions for usergroups #{5} from forum #{1} ({2}) to forum #{3} ({4})";
$l['admin_log_forum_management_editmod'] = "Edited moderator #{3} ({4}) on forum #{1} ({2})";
$l['admin_log_forum_management_permissions'] = "Edited group permissions for forum #{1} ({2})";
$l['admin_log_forum_management_add'] = "Added forum #{1} ({2})";
$l['admin_log_forum_management_edit'] = "Edited forum #{1} ({2})";
$l['admin_log_forum_management_deletemod'] = "Deleted moderator #{1} ({2}) from forum #{3} ({4})";
$l['admin_log_forum_management_delete'] = "Deleted forum #{1} ({2})";
$l['admin_log_forum_management_orders'] = "Updated root forum orders";
$l['admin_log_forum_management_orders_sub'] = "Updated forum orders within forum #{2} ({3})";
$l['admin_log_forum_management_addmod'] = "Added moderator #{2} ({3}) to forum #{4} ({5})";
$l['admin_log_forum_management_quickpermissions'] = "Updated quick forum permissions for forum #{2} ({3})";

$l['admin_log_forum_moderation_queue_threads'] = "Moderated unapproved threads";
$l['admin_log_forum_moderation_queue_posts'] = "Moderated unapproved posts";
$l['admin_log_forum_moderation_queue_attachments'] = "Moderated unapproved attachments";

$l['admin_log_home_preferences_enabled'] = "Enabled Two-Factor Authentication";
$l['admin_log_home_preferences_disabled'] = "Disabled Two-Factor Authentication";

$l['admin_log_style_templates_delete_set'] = "Deleted template set #{1} ({2})";
$l['admin_log_style_templates_delete_template'] = "Deleted template #{1} ({2}) from template set #{3} ({4})";
$l['admin_log_style_templates_delete_template_global'] = "Deleted template #{1} ({2}) from the global template set";
$l['admin_log_style_templates_add_set'] = "Added template set #{1} ({2})";
$l['admin_log_style_templates_add_template'] = "Added template #{1} ({2}) from template set #{3} ({4})";
$l['admin_log_style_templates_edit_set'] = "Edited template set #{1} ({2})";
$l['admin_log_style_templates_edit_template'] = "Edited template #{1} ({2}) from template set #{3} ({4})";
$l['admin_log_style_templates_edit_template_global'] = "Edited template #{1} ({2}) from the global template set";
$l['admin_log_style_templates_search_replace'] = "Searched templates for '{1}' and replaced with '{2}'";
$l['admin_log_style_templates_revert'] = "Reverted template #{1} ({2}) in template set #{3} ({4})";
$l['admin_log_style_templates_add_template_group'] = "Added template group #{1} ({2})";
$l['admin_log_style_templates_edit_template_group'] = "Edited template group #{1} ({2})";
$l['admin_log_style_templates_delete_template_group'] = "Deleted template group #{1} ({2})";

$l['admin_log_style_themes_import'] = "Imported theme #{1}";
$l['admin_log_style_themes_duplicate'] = "Duplicated theme #{2} to #{1}";
$l['admin_log_style_themes_add'] = "Created theme #{2} ({1})";
$l['admin_log_style_themes_edit_stylesheet'] = "Edited stylesheet {2} in {1}";
$l['admin_log_style_themes_delete_stylesheet'] = "Deleted / Reverted stylesheet #{1} ({2}) in theme #{3} ({4})";
$l['admin_log_style_themes_force'] = "Forced theme #{1} ({2}) on all users";
$l['admin_log_style_themes_set_default'] = "Set theme #{1} ({2}) as default";
$l['admin_log_style_themes_add_stylesheet'] = "Added stylesheet #{1} ({2}) in theme #{3} ({4})";
$l['admin_log_style_themes_stylesheet_properties'] = "Edited the properties for stylesheet #{1} ({2}) in theme #{3} ({4})";
$l['admin_log_style_themes_edit'] = "Edited theme #{1} ({2})";
$l['admin_log_style_themes_delete'] = "Deleted theme #{1} ({2})";
$l['admin_log_style_themes_export'] = "Exported theme #{1} ({2})";

$l['admin_log_tools_system_health_utf8_conversion'] = "Converted table {1} to UTF-8 Character Set.";

$l['admin_log_tools_adminlog_prune'] = "Pruned {4} administrator logs older than {1} days";
$l['admin_log_tools_adminlog_prune_user'] = "Pruned {4} administrator logs older than {1} days for user #{2}";
$l['admin_log_tools_adminlog_prune_module'] = "Pruned {4} administrator logs older than {1} days for module {3}";
$l['admin_log_tools_adminlog_prune_user_module'] = "Pruned {4} administrator logs older than {1} days for user #{2} and module {3}";

$l['admin_log_tools_modlog_prune'] = "Pruned {4} moderator logs older than {1} days";
$l['admin_log_tools_modlog_prune_user'] = "Pruned {4} moderator logs older than {1} days for user #{2}";
$l['admin_log_tools_modlog_prune_forum'] = "Pruned {4} moderator logs older than {1} days for forum #{3} ({5})";
$l['admin_log_tools_modlog_prune_user_forum'] = "Pruned {4} moderator logs older than {1} days for user #{2} and forum #{3} ({5})";

$l['admin_log_tools_backupdb_dlbackup'] = "Downloaded an existing backup: {1}";
$l['admin_log_tools_backupdb_delete'] = "Deleted a backup: {1}";
$l['admin_log_tools_backupdb_backup'] = "Created a backup: {2}";
$l['admin_log_tools_backupdb_backup_download'] = "Downloaded a backup of the current database";

$l['admin_log_tools_cache_rebuild'] = "Rebuilt cache ({1})";
$l['admin_log_tools_cache_reload'] = "Reload cache ({1})";
$l['admin_log_tools_cache_rebuild_all'] = "Rebuilt & reloaded all caches";

$l['admin_log_tools_index_utf8_conversion'] = "Converted table {1} to UTF-8";

$l['admin_log_tools_mailerrors_prune'] = "Pruned {1} email error logs";

$l['admin_log_tools_maillogs_prune'] = "Pruned {1} email logs";

$l['admin_log_tools_optimizedb_'] = "Optimized database tables: {1}";

$l['admin_log_tools_php_info_phpinfo'] = "Viewed PHP Info";

$l['admin_log_tools_recount_rebuild_'] = "Recounted and rebuilt ({1})";
$l['admin_log_tools_recount_rebuild_stats'] = "Recounted and rebuilt statistics";
$l['admin_log_tools_recount_rebuild_forum'] = "Recounted and rebuilt forum counters";
$l['admin_log_tools_recount_rebuild_thread'] = "Recounted and rebuilt thread counters";
$l['admin_log_tools_recount_rebuild_poll'] = "Recounted and rebuilt poll votes";
$l['admin_log_tools_recount_rebuild_userposts'] = "Recounted and rebuilt user post counts";
$l['admin_log_tools_recount_rebuild_userthreads'] = "Recounted and rebuilt user thread counts";
$l['admin_log_tools_recount_rebuild_attachmentthumbs'] = "Recounted and rebuilt attachment thumbnails";
$l['admin_log_tools_recount_rebuild_reputation'] = "Recounted and rebuilt user reputation";
$l['admin_log_tools_recount_rebuild_warning'] = "Recounted and rebuilt warning points";
$l['admin_log_tools_recount_rebuild_privatemessages'] = "Recounted and rebuilt private messages";
$l['admin_log_tools_recount_rebuild_referral'] = "Recounted and rebuilt user referral count";
$l['admin_log_tools_recount_rebuild_threadrating'] = "Recounted and rebuilt thread ratings";

$l['admin_log_tools_spamlog_prune'] = "Pruned {4} spam logs older than {1} days";
$l['admin_log_tools_spamlog_prune_user'] = "Pruned {4} spam logs older than {1} days with username {2}";
$l['admin_log_tools_spamlog_prune_email'] = "Pruned {4} spam logs older than {1} days with email {3}";
$l['admin_log_tools_spamlog_prune_user_email'] = "Pruned {4} spam logs older than {1} days with username {2} and email {3}";

$l['admin_log_tools_tasks_add'] = "Added task #{1} ({2})";
$l['admin_log_tools_tasks_edit'] = "Edited task #{1} ({2})";
$l['admin_log_tools_tasks_delete'] = "Deleted task #{1} ({2})";
$l['admin_log_tools_tasks_enable'] = "Enabled task #{1} ({2})";
$l['admin_log_tools_tasks_disable'] = "Disabled task #{1} ({2})";
$l['admin_log_tools_tasks_run'] = "Executed task #{1} ({2})";

$l['admin_log_user_awaiting_activation_activate_activated'] = "Activated {2} user account(s)";
$l['admin_log_user_awaiting_activation_activate_deleted'] = "Deleted {2} user account(s)";

$l['admin_log_user_admin_permissions_delete_user'] = "Deleted administrator permissions for user #{1} ({2})";
$l['admin_log_user_admin_permissions_delete_group'] = "Deleted group administrator permissions for usergroup #{1} ({2})";
$l['admin_log_user_admin_permissions_edit_user'] = "Edited administrator permissions for user #{1} ({2})";
$l['admin_log_user_admin_permissions_edit_group'] = "Edited group administrator permissions for usergroup #{1} ({2})";
$l['admin_log_user_admin_permissions_edit'] = "Edited default administrator permissions";

$l['admin_log_user_banning_lift'] = "Lifted ban for user #{1} ({2})";
$l['admin_log_user_banning_edit'] = "Edited ban for user #{1} ({2})";
$l['admin_log_user_banning_prune'] = "Pruned posts and threads for user #{1} ({2})";
$l['admin_log_user_banning_add_permanent'] = "Banned user #{1} ({2}) permanently";
$l['admin_log_user_banning_add_temporary'] = "Banned user #{1} ({2}) until {3}";

$l['admin_log_user_group_promotions_disable'] = "Disabled group promotion #{1} ({2})";
$l['admin_log_user_group_promotions_delete'] = "Deleted group promotion #{1} ({2})";
$l['admin_log_user_group_promotions_enable'] = "Enabled group promotion #{1} ({2})";
$l['admin_log_user_group_promotions_edit'] = "Edited group promotion #{1} ({2})";
$l['admin_log_user_group_promotions_add'] = "Added group promotion #{1} ({2})";

$l['admin_log_user_groups_add_leader'] = "Added user #{1} ({2}) as a leader for usergroup #{3} ({4})";
$l['admin_log_user_groups_delete_leader'] = "Removed user #{1} ({2}) as a leader for usergroup #{3} ({4})";
$l['admin_log_user_groups_edit_leader'] = "Edited user #{1} ({2}) as a leader for usergroup #{3} ({4})";
$l['admin_log_user_groups_add'] = "Added usergroup #{1} ({2})";
$l['admin_log_user_groups_edit'] = "Edited usergroup #{1} ({2})";
$l['admin_log_user_groups_delete'] = "Deleted usergroup #{1} ({2})";
$l['admin_log_user_groups_disporder'] = "Updated usergroup display orders";
$l['admin_log_user_groups_join_requests_approve'] = "Approved selected join requests for usergroup #{3} ({2})";
$l['admin_log_user_groups_join_requests_deny'] = "Denied selected join requests for usergroup #{3} ({2})";

$l['admin_log_user_titles_add'] = "Added default user title #{1} ({2}) at {3} posts";
$l['admin_log_user_titles_edit'] = "Edited default user title #{1} ({2}) at {3} posts";
$l['admin_log_user_titles_delete'] = "Deleted default user title #{1} ({2}) at {3} posts";

$l['admin_log_user_users_avatar_gallery'] = "Selected a new avatar for user #{1} ({2})";
$l['admin_log_user_users_activate_user'] = "Activated user #{1} ({2})";
$l['admin_log_user_users_add'] = "Created user #{1} ({2})";
$l['admin_log_user_users_edit'] = "Edited user #{1} ({2})";
$l['admin_log_user_users_delete'] = "Deleted user #{1} ({2})";
$l['admin_log_user_users_ipaddresses'] = "Viewed IP addresses associated with user #{1} ({2})";
$l['admin_log_user_users_merge'] = "Merged user #{1} ({2}) into user #{3} ({4})";

$l['admin_log_user_users_inline_usergroup'] = "Edited {1} user(s) primary / additional / display usergroup";
$l['admin_log_user_users_inline_delete'] = "Deleted {1} user(s)";
$l['admin_log_user_users_inline_banned_perm'] = "Banned {1} user(s) permanently";
$l['admin_log_user_users_inline_banned_temp'] = "Banned {1} user(s) until {2}";
$l['admin_log_user_users_inline_activated'] = "Activated {1} user(s)";
$l['admin_log_user_users_inline_lift'] = "Lifted {1} user(s) bans";

$l['admin_log_user_mass_mail_delete'] = "Deleted mass mail #{1} ({2})";

$l['admin_log_admin_locked_out'] = 'Administrator login attempt for user #{1} ({2}) locked out.';


