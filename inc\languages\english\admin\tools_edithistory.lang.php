<?php
/**
 * Edit History Log
 * Copyright 2010 Starpaul20
 */

// Recount tool
$l['recount_editcount'] = "Recount Edit Count";
$l['recount_editcount_desc'] = "This will recount the edit count of each post.";
$l['success_rebuilt_editcount'] = "The edit count have been recounted successfully.";

// Admin Permissions
$l['can_manage_edit_history'] = "Can manage edit history?";

// Admin Log
$l['admin_log_tools_edithistory_prune'] = "Pruned {4} edit histories older than {1} days";
$l['admin_log_tools_edithistory_prune_user'] = "Pruned {4} edit histories older than {1} days for user #{2}";
$l['admin_log_tools_edithistory_prune_thread'] = "Pruned {4} edit histories older than {1} days for thread #{3}";
$l['admin_log_tools_edithistory_prune_user_thread'] = "Pruned {4} edit histories older than {1} days for user #{2} and thread #{3}";
$l['admin_log_tools_recount_rebuild_editcount'] = "Recounted and rebuilt edit count";

// Edit history page
$l['edit_history_log'] = "Edit History Log";

$l['edit_history'] = "Edit History";
$l['prune_edit_history'] = "Prune Edit History";
$l['edit_history_desc'] = "Here you can view and search all edits made to posts.";
$l['prune_edit_history_desc'] = "Here you can prune edit histories matching a specified criteria.";
$l['success_pruned_edit_history'] = "The edit history have been pruned successfully.";
$l['note_history_locked'] = "For security reasons, edit histories less than 24 hours old cannot be pruned.";

$l['all_users'] = "All Users";
$l['all_threads'] = "All Threads";
$l['user'] = "Username:";
$l['thread'] = "Thread:";
$l['post'] = "Post:";

$l['username'] = "Username";
$l['na_deleted'] = "N/A - Been Deleted";
$l['edit_date'] = "Edit Date";
$l['ipaddress'] = "IP Address";
$l['information'] = "Information";
$l['reason'] = "Edit Reason";
$l['thread_subject'] = "Thread Subject";
$l['date_range'] = "Date range:";
$l['older_than'] = "Older than ";
$l['days'] = "days";

$l['no_edit_history'] = "There are no log entries with the selected criteria.";
$l['asc'] = "Ascending";
$l['desc'] = "Descending";
$l['filter_edit_history'] = "Filter Edit History";
$l['sort_by'] = "Sort By:";
$l['results_per_page'] = "Results Per Page:";
$l['from_user'] = "From User:";
$l['in'] = "in";
$l['order'] = "order";
$l['order_added'] = "Order Added";
