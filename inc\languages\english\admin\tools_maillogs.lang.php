<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['user_email_log'] = "User Email Log";
$l['user_email_log_desc'] = "All emails sent from one member to another member as well as emails sent by the 'Send Thread to a Friend' feature are logged and shown below. Here you can identify potential abusers of this function.";
$l['prune_user_email_log'] = "Prune User Email Log";

$l['close_window'] = "Close Window";
$l['user_email_log_viewer'] = "User Email Log Viewer";
$l['to'] = "To";
$l['from'] = "From";
$l['ip_address'] = "IP Address";
$l['subject'] = "Subject";
$l['date'] = "Date";
$l['email'] = "Email";
$l['date_sent'] = "Date Sent";
$l['deleted'] = "Deleted";
$l['sent_using_send_thread_feature'] = "Sent using the Send Thread to Friend feature";
$l['thread'] = "Thread:";
$l['find_emails_by_user'] = "Find all emails sent by this user";
$l['find'] = "Find";
$l['deleted_user'] = "Deleted User";
$l['email_sent_to_user'] = "Email sent to user";
$l['email_sent_using_contact_form'] = "Email sent using the contact form";
$l['no_logs'] = "There are no log entries with the selected criteria.";
$l['filter_user_email_log'] = "Filter User Email Log";
$l['username_is'] = "Username is";
$l['email_contains'] = "Email Address contains";
$l['subject_contains'] = "Subject contains";
$l['find_emails_to_user'] = "Find all emails sent to this user";

$l['error_invalid_user'] = "The username you entered does not exist.";

