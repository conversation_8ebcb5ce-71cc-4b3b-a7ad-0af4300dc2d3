<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */


$l['mod_logs'] = "Moderator Logs";
$l['mod_logs_desc'] = "Here you can view, prune, and search the moderator logs. These logs contain the actions any user may take (deletion of their own posts/threads) and all other actions by moderators.";
$l['prune_mod_logs'] = "Prune Moderator Logs";
$l['prune_mod_logs_desc'] = "Here you can prune the moderator logs matching a specified criteria.";

$l['no_modlogs'] = "There are no log entries with the selected criteria.";

$l['username'] = "Username";
$l['na_deleted'] = "N/A - Been Deleted";
$l['date'] = "Date";
$l['action'] = "Action";
$l['information'] = "Information";
$l['ipaddress'] = "IP Address";

$l['forum'] = "Forum:";
$l['thread'] = "Thread:";
$l['post'] = "Post:";
$l['user_info'] = "User:";
$l['announcement'] = "Announcement:";

$l['filter_moderator_logs'] = "Filter Moderator Logs";
$l['forum_moderator'] = "Forum Moderator:";
$l['sort_by'] = "Sort By:";
$l['results_per_page'] = "Results Per Page:";
$l['all_moderators'] = "All Moderators";
$l['older_than'] = "Older than ";

$l['forum_name'] = "Forum Name";
$l['thread_subject'] = "Thread Subject";

$l['asc'] = "Ascending";
$l['desc'] = "Descending";

$l['in'] = "in";
$l['order'] = "order";
$l['days'] = "days";

$l['prune_moderator_logs'] = "Prune Moderator Logs";
$l['date_range'] = "Date range:";

$l['success_pruned_mod_logs'] = "The moderator logs have been pruned successfully.";
$l['note_logs_locked'] = "For security reasons, logs less than 24 hours old cannot be pruned.";

