<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['recount_rebuild'] = "Recount &amp; Rebuild";
$l['recount_rebuild_desc'] = "Here you can recount &amp; rebuild data to fix any synchronization errors in your forum.";

$l['data_per_page'] = "Data Entries Per Page";
$l['recount_stats'] = "Recount Statistics";
$l['recount_stats_desc'] = "This will recount and update your forum statistics on the forum index and statistics pages.";
$l['recount_reputation'] = "Recount Reputation";
$l['recount_reputation_desc'] = "This will recount the reputation of each user.";
$l['recount_warning'] = "Recount Warning Points";
$l['recount_warning_desc'] = "This will recount the active warning points of each user.";
$l['recount_private_messages'] = "Recount Private Messages";
$l['recount_private_messages_desc'] = "This will recount the private message count for each user.";
$l['recount_referrals'] = "Recount Referrals";
$l['recount_referrals_desc'] = "This will recount the referral count of each user.";
$l['recount_thread_ratings'] = "Recount Thread Ratings";
$l['recount_thread_ratings_desc'] = "This will recount the rating of each thread.";
$l['rebuild_forum_counters'] = "Rebuild Forum Counters";
$l['rebuild_forum_counters_desc'] = "When this is run, the post/thread counters and last post of each forum will be updated to reflect the correct values.";
$l['rebuild_thread_counters'] = "Rebuild Thread Counters";
$l['rebuild_thread_counters_desc'] = "When this is run, the post/view counters and last post of each thread will be updated to reflect the correct values.";
$l['rebuild_poll_counters'] = "Rebuild Poll Counters";
$l['rebuild_poll_counters_desc'] = "When this is run, the vote counters and total number of votes of each poll will be updated to reflect the correct values.";
$l['recount_user_posts'] = "Recount User Post Counts";
$l['recount_user_posts_desc'] = "When this is run, the post count for each user will be updated to reflect its current live value based on the posts in the database, and forums that have post count disabled.";
$l['recount_user_threads'] = "Recount User Thread Counts";
$l['recount_user_threads_desc'] = "When this is run, the thread count for each user will be updated to reflect its current live value based on the threads in the database, and forums that have thread count disabled.";
$l['rebuild_attachment_thumbs'] = "Rebuild Attachment Thumbnails";
$l['rebuild_attachment_thumbs_desc'] = "This will rebuild attachment thumbnails to ensure they're using the current width and height dimensions and will also rebuild missing thumbnails.";

$l['success_rebuilt_forum_counters'] = "The forum counters have been rebuilt successfully.";
$l['success_rebuilt_thread_counters'] = "The thread counters have been rebuilt successfully.";
$l['success_rebuilt_poll_counters'] = "The poll counters have been rebuilt successfully.";
$l['success_rebuilt_user_post_counters'] = "The user posts count have been recounted successfully.";
$l['success_rebuilt_user_thread_counters'] = "The user threads count have been recounted successfully.";
$l['success_rebuilt_attachment_thumbnails'] = "The attachment thumbnails have been rebuilt successfully.";
$l['success_rebuilt_forum_stats'] = "The forum statistics have been rebuilt successfully.";
$l['success_rebuilt_reputation'] = "The user reputation has been rebuilt successfully.";
$l['success_rebuilt_warning'] = "The user warning points have been rebuilt successfully.";
$l['success_rebuilt_private_messages'] = "The user private message count has been recounted successfully.";
$l['success_rebuilt_referral'] = "The user referral count have been recounted successfully.";
$l['success_rebuilt_thread_ratings'] = "The thread ratings have been recounted successfully.";

$l['confirm_proceed_rebuild'] = "Click \"Proceed\" to continue the recount and rebuild process.";
$l['automatically_redirecting'] = "Automatically Redirecting&hellip;";

