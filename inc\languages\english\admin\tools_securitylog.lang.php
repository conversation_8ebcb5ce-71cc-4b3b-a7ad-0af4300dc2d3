<?php
/**
 * Security Log
 * Copyright 2016 Starpaul20
 */

$l['securitylog_info_name'] = "Security Log";
$l['securitylog_info_desc'] = "Logs all failed attempts to log into an account.";

$l['can_manage_security_log'] = "Can manage security log?";

$l['security_log'] = "Security Log";
$l['security_log_desc'] = "Here you can view, prune, and search the security logs. These logs contain all failed attempts to log into an account.";
$l['prune_security_log'] = "Prune Security Log";
$l['prune_security_log_desc'] = "Here you can prune the security logs matching a specified criteria.";

$l['username'] = "Username";
$l['date'] = "Date";
$l['ipaddress'] = "IP Address";
$l['admin_attempt'] = "Admin CP Attempt";

$l['yes_pin'] = "Yes, wrong secret pin";
$l['yes_password'] = "Yes, wrong password";
$l['yes'] = "Yes";
$l['no'] = "No";

$l['filter_security_logs'] = "Filter Security Logs";
$l['all_users'] = "All Users";
$l['asc'] = "Ascending";
$l['desc'] = "Descending";
$l['username_colon'] = "Username:";
$l['sort_by'] = "Sort By:";
$l['results_per_page'] = "Results Per Page:";
$l['in'] = "in";
$l['order'] = "order";

$l['date_range'] = "Date range:";
$l['older_than'] = "Older than ";
$l['days'] = "days";

$l['no_security_logs'] = "There are no log entries with the selected criteria.";

$l['success_pruned_security_logs'] = "The security logs have been pruned successfully.";
$l['note_logs_locked'] = "For security reasons, logs less than 24 hours old cannot be pruned.";

$l['na_deleted'] = "N/A - Been Deleted";
