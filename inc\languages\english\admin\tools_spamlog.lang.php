<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 */


$l['spam_logs'] = 'Spam Logs';
$l['spam_logs_desc'] = 'This section allows you to view a history of users blocked by the spam filters.';
$l['prune_spam_logs'] = 'Prune Spam Logs';
$l['prune_spam_logs_desc'] = 'Here you can prune the spam logs matching a specified criteria.';
$l['spam_username'] = 'Username';
$l['spam_email'] = 'Email Address';
$l['spam_ip'] = 'IP Address';
$l['spam_date'] = 'Date';
$l['spam_confidence'] = 'Confidence';
$l['no_spam_logs'] = 'No users have been blocked by the spam filters yet.';
$l['success_pruned_spam_logs'] = 'The spam logs have been pruned successfully.';
$l['note_logs_locked'] = 'For security reasons, logs less than 24 hours old cannot be pruned.';
$l['all_usernames'] = 'All usernames';
$l['all_emails'] = ' All Email Addresses';
$l['date_range'] = "Date range:";
$l['days'] = "days";
$l['filter_spam_logs'] = 'Filter Spam Logs';
$l['asc'] = "Ascending";
$l['desc'] = "Descending";
$l['search_ip_on_sfs'] = "Search this IP on Stop Forum Spam";
$l['search'] = "Search";

$l['in'] = "in";
$l['order'] = "order";
$l['sort_by'] = "Sort By";
$l['results_per_page'] = "Results Per Page";
