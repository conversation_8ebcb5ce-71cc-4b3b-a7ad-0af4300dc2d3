<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */


$l['warning_logs'] = "Warning Logs";
$l['warning_logs_desc'] = "This section allows you to view a history of warnings issued to users.";
$l['warned_user'] = "Warned User";
$l['warning'] = "Warning";
$l['date_issued'] = "Date Issued";
$l['expires'] = "Expires";
$l['expiry_date'] = "Expiry Date";
$l['issued_date'] = "Issued Date";
$l['issued_by'] = "Issued By";
$l['details'] = "Details";
$l['filter_warning_logs'] = "Filter Warning Logs";
$l['filter_warned_user'] = "Warned user:";
$l['filter_issued_by'] = "Warning issued by:";
$l['filter_reason'] = "Reason contains:";
$l['sort_by'] = "Sort By:";
$l['results_per_page'] = "Results Per Page:";
$l['view'] = "View";
$l['no_warning_logs'] = "There are no warning logs to view.";
$l['revoked'] = "Revoked ";
$l['post'] = "Post";

$l['asc'] = "Ascending";
$l['desc'] = "Descending";

$l['in'] = "in";
$l['order'] = "order";

$l['warning_details'] = "Warning Details";
$l['warning_note'] = "Administrative Notes";
$l['already_expired'] = "Expired";
$l['warning_revoked'] = "Revoked";
$l['warning_active'] = "Active";
$l['error_invalid_warning'] = "An invalid warning was specified.";

$l['revoke_warning'] = "Revoke this Warning";
$l['revoke_warning_desc'] = "To revoke this warning please enter a reason below. This will not remove any bans or suspensions imposed by this warning.";
$l['reason'] = "Reason:";
$l['warning_is_revoked'] = "This warning has been revoked";
$l['revoked_by'] = "Revoked by:";
$l['date_revoked'] = "Date Revoked:";
$l['error_already_revoked'] = "This warning has already been revoked.";
$l['error_no_revoke_reason'] = "You did not enter a reason as to why you want to revoke this warning.";
$l['redirect_warning_revoked'] = "This warning has been revoked and the users warning points decreased successfully.";

$l['warning_points'] = "({1} points)";
