<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['admin_permissions'] = "Admin Permissions";
$l['user_permissions'] = "User Permissions";
$l['user_permissions_desc'] = "Here you can manage the administrator permissions for individual users. This effectively allows you to lock certain administrators out of different areas of the Admin CP.";
$l['group_permissions'] = "Group Permissions";
$l['group_permissions_desc'] = "Administrator permissions can also be applied to user groups that have permission to access the Admin CP. Similarly you can use this tool to lock out entire administrative groups from accessing the different areas of the Admin CP.";
$l['default_permissions'] = "Default Permissions";
$l['default_permissions_desc'] = "The default administrative permissions are those applied to users who do not have custom administrator permissions set for them or are not inheriting group administrator permissions.";

$l['admin_permissions_updated'] = "The admin permissions have been updated successfully.";
$l['revoke_permissions'] = "Revoke Permissions";
$l['edit_permissions'] = "Edit Permissions";
$l['set_permissions'] = "Set Permissions";
$l['edit_permissions_desc'] = "Here you can restrict access to entire tabs or individual pages. Be aware that the \"Home\" tab is accessible to all administrators.";
$l['update_permissions'] = "Update Permissions";
$l['view_log'] = "View Log";
$l['permissions_type_group'] = "Permission type of the group";
$l['permissions_type_user'] = "Permission type of the user";
$l['no_group_perms'] = "There are currently no set group permissions.";
$l['no_user_perms'] = "There are currently no set user permissions.";
$l['edit_user'] = "Edit User Profile";
$l['using_individual_perms'] = "Using Individual Permissions";
$l['using_custom_perms'] = "Using Custom Permissions";
$l['using_group_perms'] = "Using Group Permissions";
$l['using_default_perms'] = "Using Default Permissions";
$l['last_active'] = "Last Active";
$l['user'] = "User";
$l['edit_group'] = "Edit Group";
$l['default'] = "Default";
$l['group'] = "Group";

$l['error_super_admin'] = 'Sorry, but you cannot perform this action on the specified user as they are a super administrator.';
$l['error_delete_no_uid'] = 'You did not enter a admin user/usergroup permission id';
$l['error_delete_invalid_uid'] = 'You did not enter a valid admin user/usergroup permission id';

$l['success_perms_deleted'] = 'The admin user/usergroup permissions has been revoked successfully.';

$l['confirm_perms_deletion'] = "Are you sure you wish to revoked this admin user/usergroup permissions?";
$l['confirm_perms_deletion2'] = "Are you sure you wish to revoke this user\'s permissions?";
$l['confirm_perms_deletion3'] = "Are you sure you wish to revoke this group\'s permissions?";
