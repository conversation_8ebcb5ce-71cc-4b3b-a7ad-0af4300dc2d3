<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['user_group_promotions'] = "User Group Promotions";
$l['user_group_promotions_desc'] = "Here you can manage User Group Promotions.";
$l['edit_promotion'] = "Edit Promotion";
$l['edit_promotion_desc'] = "Here you can edit promotions which are automatically run on your board.";
$l['add_new_promotion'] = "Add New Promotion";
$l['add_new_promotion_desc'] = "Here you can create new promotions which are automatically run on your board.";

$l['title'] = "Title";
$l['short_desc'] = "Short Description";
$l['post_count'] = "Post Count";
$l['thread_count'] = "Thread Count";
$l['reputation'] = "Reputation";
$l['referrals'] = "Referrals";
$l['time_registered'] = "Time Registered";
$l['time_online'] = "Time Online";
$l['promo_requirements'] = "Promotion Requirements";
$l['promo_requirements_desc'] = "Select which criteria must be met for this promotion. Holding down CTRL selects multiple criteria.";
$l['greater_than_or_equal_to'] = "Greater than or equal to";
$l['greater_than'] = "Greater than";
$l['equal_to'] = "Equal to";
$l['less_than_or_equal_to'] = "Less than or equal to";
$l['less_than'] = "Less than";
$l['reputation_count'] = "Reputation Count";
$l['reputation_count_desc'] = "Enter the amount of reputation to be required. Reputation must be selected as a required value for this to be included. Select the type of comparison for reputation.";
$l['referral_count'] = "Referral Count";
$l['referral_count_desc'] = "Enter the number of referrals required. Referral count must be selected as a required value for this to be included. Select the type of comparison for referrals.";
$l['warning_points'] = "Warning Points";
$l['warning_points_desc'] = "Enter the number of warning points required. Warning Points must be selected as a required value for this to be included. Select the type of comparison for warning points.";
$l['post_count_desc'] = "Enter the number of posts required. Post count must be selected as a required value for this to be included. Select the type of comparison for posts.";
$l['thread_count_desc'] = "Enter the number of threads required. Thread count must be selected as a required value for this to be included. Select the type of comparison for threads.";
$l['hours'] = "Hours";
$l['days'] = "Days";
$l['weeks'] = "Weeks";
$l['months'] = "Months";
$l['years'] = "Years";
$l['time_registered_desc'] = "Enter the number of hours, days, weeks, months, or years that this user must have been registered for. Time registered must be selected as a required value for this to be included. Select whether the time registered should be counted in hours, days, weeks, months, or years.";
$l['time_online_desc'] = "Enter the number of hours, days, weeks, months, or years that this user must have been online for. Time online must be selected as a required value for this to be included. Select whether the time spend online should be counted in hours, days, weeks, months, or years.";
$l['all_user_groups'] = 'All User Groups';
$l['orig_user_group'] = 'Original User Group';
$l['orig_user_group_desc'] = "Select which user group or user groups that the user must be in for the promotion to run. Holding down CTRL selects multiple groups.";
$l['new_user_group'] = 'New User Group';
$l['new_user_group_desc'] = "Select the user group that the user will be moved into after this promotion.";
$l['primary_user_group'] = 'Primary User Group';
$l['secondary_user_group'] = 'Secondary User Group';
$l['user_group_change_type'] = 'User Group Change Type';
$l['user_group_change_type_desc'] = "Select 'Primary User Group' if the user should have their primary user group changed to the new user group. Select 'Secondary User Group' if the user should have the new user group added as an secondary user group to their profile.";
$l['enabled'] = "Enabled?";
$l['enable_logging'] = "Enable Logging?";
$l['promotion_logs'] = "Promotion Logs";
$l['view_promotion_logs'] = "View Promotion Logs";
$l['view_promotion_logs_desc'] = 'Here you can view logs of promotions previously run.';
$l['promoted_user'] = "Promoted User";
$l['time_promoted'] = "Time Promoted";
$l['no_promotion_logs'] = "There are currently no promotions logged.";
$l['promotion_manager'] = "Promotions Manager";
$l['promotion'] = "Promotion";
$l['disable_promotion'] = "Disable Promotion";
$l['enable_promotion'] = "Enable Promotion";
$l['delete_promotion'] = "Delete Promotion";
$l['no_promotions_set'] = "There are currently no set promotions.";
$l['update_promotion'] = "Save Promotion";
$l['multiple_usergroups'] = "Multiple User Groups";
$l['secondary'] = "Secondary";
$l['primary'] = "Primary";

$l['error_no_promo_id'] = 'You did not enter a promotion id';
$l['error_invalid_promo_id'] = 'You did not enter a valid promotion id';

$l['error_no_title'] = "You did not enter a title for this promotion";
$l['error_no_desc'] = "You did not enter a description for this promotion";
$l['error_no_requirements'] = "You did not select at least one requirement for this promotion";
$l['error_no_orig_usergroup'] = "You did not select at least one original user group for this promotion";
$l['error_no_new_usergroup'] = "You did not select at least one new user group for this promotion";
$l['error_no_usergroup_change_type'] = "You did not select at least one user group change type for this promotion";

$l['success_promo_disabled'] = 'The selected group promotion has been disabled successfully.';
$l['success_promo_deleted'] = 'The selected group promotion has been deleted successfully.';
$l['success_promo_enabled'] = 'The selected group promotion has been enabled successfully.';
$l['success_promo_updated'] = 'The selected group promotion has been updated successfully.';
$l['success_promo_added'] = 'The promotion has been created successfully.';

$l['confirm_promo_disable'] = "Are you sure you want to disable this promotion?";
$l['confirm_promo_deletion'] = "Are you sure you wish to delete this promotion?";

