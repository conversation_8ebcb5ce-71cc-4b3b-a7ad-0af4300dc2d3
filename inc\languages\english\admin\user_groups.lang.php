<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['user_groups'] = "User Groups";
$l['manage_user_groups'] = "Manage User Groups";
$l['manage_user_groups_desc'] = "Here you can manage the various user groups on your board. In addition, for staff groups, you can manage their display order on the forum team page. Leave all as \"0\" sorts the group alphabetically. If you change the order of these groups, be sure to submit the form at the bottom of the page.";
$l['add_user_group'] = "Add New User Group";
$l['add_user_group_desc'] = "Here you can create a new user group and optionally copy the permissions from another user group. After saving the user group you will be taken to the full edit page for this user group.";
$l['group_join_requests'] = "Group Join Requests";
$l['group_join_requests_desc'] = "Below is a list of users who are requesting access to this user group. From here, you can either approve or deny their request.";
$l['manage_group_leaders'] = "Manage Group Leaders";
$l['manage_group_leaders_desc'] = "Here you can manage the group leaders for this user group. Depending on the options you enable for the leader, these users can manage the users who are a member of this group and can moderate any join requests if this group is publicly joinable.";
$l['edit_user_group'] = "Edit User Group";
$l['edit_user_group_desc'] = "Here you can edit an existing user group.";
$l['edit_group_leader'] = "Edit Group Leader";
$l['edit_group_leader_desc'] = "Here you can change the permissions for this group leader in regards to what actions they're allowed to perform.";
$l['group_leaders'] = "Group Leaders";

$l['delete_group'] = "Delete Group";
$l['edit_group'] = "Edit Group";
$l['no_groups'] = "There are currently no user groups on your forum";
$l['join_requests'] = "Moderate Join Requests";
$l['update_groups_order'] = "Update Groups Order";
$l['reset'] = "Reset";
$l['number_of_users'] = "# of Users";
$l['order'] = "Order";
$l['group'] = "Group";
$l['custom_user_group'] = "Custom User Group";
$l['default_user_group'] = "Default User Group";
$l['join_requests_for'] = "Join Requests for";
$l['users'] = "Users";
$l['reason'] = "Reason";
$l['date_requested'] = "Date Requested";
$l['approve'] = "Approve";
$l['deny'] = "Deny";
$l['approve_selected_requests'] = "Approve Selected Requests";
$l['deny_selected_requests'] = "Deny Selected Requests";
$l['group_leaders_for'] = "Group leaders for";
$l['can_manage_members'] = "Can manage members?";
$l['can_manage_join_requests'] = "Can manage join requests?";
$l['can_invite_members'] = "Can invite members?";
$l['user'] = "User";
$l['can_manage_group_members'] = "Can manage group members?";
$l['can_manage_group_members_desc'] = "If this user should be able to manage the members within a group, set to Yes.";
$l['can_manage_group_join_requests'] = "Can manage group join requests?";
$l['can_manage_group_join_requests_desc'] = "Should this user be able to approve or deny new membership requests for this group?";
$l['can_invite_group_members'] = "Can invite group members?";
$l['can_invite_group_members_desc'] = "Should this user be able to invite new members to join this group?";
$l['make_user_member'] = "Make user member?";
$l['make_user_member_desc'] = "Should this user be added as a member of this group if they're not already?";
$l['add_group_leader'] = "Add Group Leader to";
$l['save_group_leader'] = "Save Group Leader";
$l['edit_leader'] = "Edit Leader:";
$l['title'] = "Title";
$l['short_description'] = "Short Description";
$l['username_style'] = "Username Style";
$l['username_style_desc'] = "This option allows you to set a custom username style for users who have this user group as their display group. <strong>Use {username} to represent the user's name.</strong>";
$l['user_title'] = "Default User Title";
$l['user_title_desc'] = "If the user has nothing entered in their custom user title field, the user title entered here will be displayed. If you leave this option blank, users will have their title and stars taken from the User Titles configuration.";
$l['do_not_copy_permissions'] = "Don't copy permissions from another group";
$l['copy_permissions_from'] = "Copy Permissions From&hellip;";
$l['copy_permissions_from_desc'] = "If you wish, you can copy the forum and group permissions from another group. To make use of this, select a group to copy permissions from.";
$l['save_user_group'] = "Save User Group";
$l['list_users'] = "List Users";

$l['general'] = "General";
$l['forums_posts'] = "Forums and Posts";
$l['users_permissions'] = "Users and Permissions";
$l['misc'] = "Miscellaneous";
$l['mod_cp'] = "Moderator CP";
$l['stars'] = "# of Stars";
$l['star_image'] = "Star Image";
$l['user_stars'] = "User Stars";
$l['user_stars_desc'] = "If you enter a number of stars and the location of a star image, this star image will be shown for this particular user group. If you want to use different star images for different themes, please use <strong>{theme}</strong> to represent the image directory of each theme. The number of stars only takes effect if Default User Title is not blank.";
$l['group_image'] = "Group Image";
$l['group_image_desc'] = "Here you can set a group image which will show on each post made by users in this group. Please use <strong>{lang}</strong> to represent the user's chosen language if translated group images are available";
$l['general_options'] = "General Options";
$l['member_list'] = "Yes, show users of this group on the member list";
$l['forum_team'] = "Yes, show this group on the 'forum team' page";
$l['is_banned_group'] = "Yes, this is a banned group<br /><small>If this group is a 'banned' user group, users will be able to be 'banned' in to this user group.</small>";
$l['publicly_joinable_options'] = "Publicly Joinable Options";
$l['user_joinable'] = "Yes, users can freely join and leave this group";
$l['moderate_join_requests'] = "Yes, all new join requests need to be moderated first<br /><small>Users must be able to freely join and leave this group for this to take effect.</small>";
$l['invite_only'] = "Yes, users must be invited in order to join this group<br /><small>Users must be able to freely join and leave this group for this to take effect.</small>";
$l['can_set_as_display_group'] = "Yes, users can set this group as their display group<br /><small>If set to yes, users will be able to set this user group as their display group for user titles, stars, name style and group images.</small>";
$l['moderation_administration_options'] = "Moderation/Administration Options";
$l['is_super_mod'] = "Yes, users of this group are super moderators";
$l['can_access_mod_cp'] = "Yes, users of this group can access the moderator CP";
$l['can_access_admin_cp'] = "Yes, users of this group can access the admin CP";
$l['viewing_options'] = "Viewing Options";
$l['can_view_board'] = "Can view board?";
$l['can_view_threads'] = "Can view threads?";
$l['can_search_forums'] = "Can search forums?";
$l['can_view_profiles'] = "Can view user profiles?";
$l['can_download_attachments'] = "Can download attachments?";
$l['can_view_board_closed'] = "Can view board when closed?";
$l['posting_rating_options'] = "Posting/Rating Options";
$l['can_post_threads'] = "Can post new threads?";
$l['can_post_replies'] = "Can post replies to threads?";
$l['can_rate_threads'] = "Can rate threads?";
$l['moderation_options'] = "Moderation Options";
$l['mod_new_posts'] = "Moderate new posts?";
$l['mod_new_threads'] = "Moderate new threads?";
$l['mod_new_attachments'] = "Moderate new attachments?";
$l['mod_after_edit'] = "Moderate posts after they've been edited?";
$l['poll_options'] = "Poll Options";
$l['max_posts_per_day'] = "Maximum Posts Per Day";
$l['max_posts_per_day_desc'] = "This is the total number of posts allowed per user per day. 0 for unlimited.";
$l['can_post_polls'] = "Can post polls?";
$l['can_vote_polls'] = "Can vote in polls?";
$l['can_undo_votes'] = "Can undo votes in polls?";
$l['attachment_options'] = "Attachment Options";
$l['can_post_attachments'] = "Can post attachments?";
$l['attach_quota'] = "Attachment Quota:";
$l['attach_quota_desc'] = "Here you can set the attachment quota that each user in this group will receive. If set to 0, there is no limit.";
$l['editing_deleting_options'] = "Editing/Deleting Options";
$l['can_edit_posts'] = "Can edit own posts?";
$l['can_delete_posts'] = "Can delete own posts?";
$l['can_delete_threads'] = "Can delete own threads?";
$l['can_edit_attachments'] = "Can update own attachments?";
$l['can_view_deletion_notices'] = "Can view deletion notices?";
$l['account_management'] = "Account Management";
$l['edit_time_limit'] = "Edit Time Limit";
$l['edit_time_limit_desc'] = "The number of minutes until regular users cannot edit their own posts (if they have the permission). Enter 0 (zero) for no limit.";
$l['can_be_reported'] = "Can be reported (profile, posts and reputation)?";
$l['can_be_invisible'] = "Can this group hide their Online Status?";
$l['can_access_usercp'] = "Can access User CP?";
$l['can_change_username'] = "Can change username?";
$l['can_change_website'] = "Can change website?";
$l['can_use_usertitles'] = "Can use custom user titles?";
$l['can_upload_avatars'] = "Can upload avatars?";
$l['can_use_signature'] = "Can add a signature?";
$l['can_use_signature_posts'] = "Can add a signature after x posts?";
$l['required_posts'] = "Required Post Count Before Signature Can Be Added:";
$l['required_posts_desc'] = "Here you can enter the minimum number of posts a user must have before they can add a signature. If set to 0, users can add a signature at any post count.";
$l['uses_no_follow'] = "Signature links have nofollow enabled?";
$l['reputation_system'] = "Reputation System";
$l['can_use_pms'] = "Can use private messaging?";
$l['can_send_pms'] = "Can send private messages?";
$l['can_track_pms'] = "Can track sent private messages?";
$l['can_deny_reciept'] = "Can deny message receipt notifications?";
$l['can_override_pms'] = "Can send private messages even if recipients have them disabled?";
$l['message_quota'] = "Message Quota:";
$l['message_quota_desc'] = "Maximum number of private messages that can be stored by users in this group. If empty, users can store unlimited messages.";
$l['max_recipients'] = "Maximum Recipients Per Message:";
$l['max_recipients_desc'] = "Maximum number of recipients a user can send a private message to at one time. If empty, users can send private messages to an unlimited number of recipients.";
$l['show_reputations'] = "Show reputations for users in this group?";
$l['can_give_reputation'] = "Can give reputations to users?";
$l['can_delete_own_reputation'] = "Can delete own given reputations?";
$l['points_to_award_take'] = "Points to Award/Take Away:";
$l['points_to_award_take_desc'] = "Here you need to enter the number of points to give or take away on each reputation given by users of this group.";
$l['max_reputations_daily'] = "Maximum Reputations Allowed Per Day:";
$l['max_reputations_daily_desc'] = "Here you can enter the maximum number of reputations that users in this group can give per day. To allow unlimited reputations per day, enter 0.";
$l['max_reputations_perthread'] = "Maximum Reputations Allowed Per Thread:";
$l['max_reputations_perthread_desc'] = "When 'Allow Post Reputation' is allowed, you can set the maximum amount of reputations that users in this group can give to the same user, in the same thread, per day, in the box below. To allow unlimited reputations for a user, per thread, enter 0.";
$l['max_reputations_peruser'] = "Maximum Reputations Allowed Per User:";
$l['max_reputations_peruser_desc'] = "Along with a per thread maximum, you can enter a maximum number of reputations that users in this group can give to the same user per day. To allow unlimited reputations for a user, enter 0.";
$l['warning_system'] = "Warning System";
$l['can_send_warnings'] = "Can send warnings to other users?";
$l['can_receive_warnings'] = "Can receive warnings from other users?";
$l['warnings_per_day'] = "Maximum Warnings Allowed Per Day. If set to 0, there is no limit.";
$l['private_messaging'] = "Private Messaging";
$l['calendar'] = "Calendar";
$l['can_view_calendar'] = "Can view calendar?";
$l['can_post_events'] = "Can post calendar events?";
$l['can_bypass_event_moderation'] = "Can bypass calendar event moderation queue?";
$l['can_moderate_events'] = "Can moderate calendar events?";
$l['whos_online'] = "Who's Online";
$l['can_view_whos_online'] = "Can view who's online?";
$l['can_view_invisible'] = "Can view invisible users?";
$l['can_view_ips'] = "Can view IP addresses on who's online?";
$l['can_view_member_list'] = "Can view member list?";
$l['show_in_birthday_list'] = "Can be shown in the birthday list?";
$l['can_email_users'] = "Can send threads to friends and email users?";
$l['can_email_users_override'] = "Can email users even if they appear on their ignore list?";
$l['max_emails_per_day'] = "Maximum Emails Per Day:";
$l['max_emails_per_day_desc'] = "The maximum number of emails users can send using the 'Email User' and 'Send Thread to Friend' features. If set to 0, there is no limit.";
$l['email_flood_time'] = "Email Flood Time:";
$l['email_flood_time_desc'] = "The number of minutes a user must wait after sending an email before they can send another. If set to 0, there is no wait.";
$l['forum_post_options'] = "Forums & Posts";
$l['user_options'] = "Users";
$l['can_manage_announce'] = "Can manage announcements?<br /><small>Please note that forum moderators must be assigned to at least one forum in order to manage announcements.</small>";
$l['can_manage_mod_queue'] = "Can manage moderator queue?<br /><small>Please note that forum moderators must be assigned to at least one forum in order to manage the moderator queue.</small>";
$l['can_manage_reported_content'] = "Can manage reported content?<br /><small>Please note that forum moderators must be assigned to at least one forum in order to manage reported content.</small>";
$l['can_view_mod_logs'] = "Can view moderator logs?<br /><small>Please note that forum moderators must be assigned to at least one forum in order to view the moderator logs.</small>";
$l['can_edit_profiles'] = "Can edit profiles?<br /><small>Please note that forum moderators cannot edit the profiles of super moderators or administrators regardless of this permission.</small>";
$l['can_ban_users'] = "Can ban users?<br /><small>Please note that forum moderators cannot ban super moderators or administrators regardless of this permission.</small>";
$l['can_view_warnlogs'] = "Can view warning logs?";
$l['can_use_ipsearch'] = "Can use IP search?";
$l['outstanding_join_request'] = "outstanding join requests";

$l['no_join_requests'] = "There are no outstanding join requests for this user group.";
$l['no_assigned_leaders'] = "You haven't assigned any users as leaders of this group yet. To create a leader for this group, fill in the form below.";

$l['error_missing_title'] = "You did not enter a title for this new user group.";
$l['error_invalid_user_group'] = "You have selected an invalid user group.";
$l['error_invalid_join_request'] = "You have selected an invalid join request.";
$l['error_invalid_username'] = "The username you entered is invalid.";
$l['error_already_leader'] = "The user is already a leader of this user group.";
$l['error_invalid_group_leader'] = "You specified an invalid group leader.";
$l['error_missing_namestyle_username'] = "The username style must contain {username}";
$l['error_disallowed_namestyle_username'] = "You can't use script, meta or base tags in the username style.";
$l['error_default_group_delete'] = "Default groups cannot be deleted";
$l['error_cannot_have_both_types'] = "You cannot have a joinable group that is both moderated and invite only. Please choose one or the other.";

$l['success_group_created'] = "The new user group has been created successfully.";
$l['success_group_updated'] = "The selected user group has been updated successfully.";
$l['success_group_created_duplicate_title'] = "The new user group has been created successfully. Please note that there are multiple user groups named \"{1}\".";
$l['success_group_updated_duplicate_title'] = "The selected user group has been updated successfully. Please note that there are multiple user groups named \"{1}\".";
$l['success_group_deleted'] = "The selected user group has been deleted successfully.";
$l['success_groups_disporder_updated'] = "The user group display orders have been updated successfully.";
$l['success_join_request_approved'] = "The selected join request has been approved successfully. The user is now a member of this user group.";
$l['success_join_request_denied'] = "The selected join request has been denied successfully.";
$l['success_selected_requests_approved'] = "The selected join requests have been approved successfully. The users are now part of this group.";
$l['success_selected_requests_denied'] = "The selected join requests have been denied successfully.";
$l['success_user_made_leader'] = "was successfully made a group leader for this user group.";
$l['success_group_leader_updated'] = "The selected group leader has been updated successfully.";
$l['success_group_leader_deleted'] = "The selected user has been removed from the group leaders list for this group successfully.";

$l['confirm_group_deletion'] = "Are you sure you want to delete this user group?";
$l['confirm_group_leader_deletion'] = "Are you sure you want to delete this group leader?";

