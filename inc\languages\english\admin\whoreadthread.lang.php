<?php
/*
 * MyBB: Who Read Thread
 *
 * File: whoreadthread.lang.php
 * 
 * Authors: <AUTHORS>
 *
 * MyBB Version: 1.8
 *
 * Plugin Version: 1.2
 * 
 *
 */


// whoreathread_plugin_info

$l['whoreadthread_Name'] = 'Who view thread';
$l['whoreadthread_Desc'] = 'Show lasts users who have visited a thread.';
$l['whoreadthread_Web'] = 'http://community.mybb.com/user-6029.html';
$l['whoreadthread_Auth'] = 'Mirko T. & Vintagedaddyo';
$l['whoreadthread_AuthSite'] = 'http://community.mybb.com/user-6029.html';
$l['whoreadthread_Ver'] = '1.2';
$l['whoreadthread_GUID'] = 'bf68597452d5a59ee0def578e2cba4fe';
$l['whoreadthread_Compat'] = '18*';


// Settings Group

$l['whoreadthread_option_0_Title'] = 'Who Read Thread';
$l['whoreadthread_option_0_Description'] = 'Shows users who have visited the thread.';

// Settings

$l['whoreadthread_option_1_Title'] = 'Enable?';
$l['whoreadthread_option_1_Description'] = 'Enable or disable the plugin.';

$l['whoreadthread_option_2_Title'] = 'Default Sort Field';
$l['whoreadthread_option_2_Description'] = 'Select the field that you want members to be sorted by default.';

$l['whoreadthread_option_3_Title'] = 'Default Sort Order';
$l['whoreadthread_option_3_Description'] = 'Select the order that you want members to be sorted by default.';

$l['whoreadthread_option_4_Title'] = 'Maximum Users To Display?';
$l['whoreadthread_option_4_Description'] = 'Enter the maximum number of users you want to display.';

$l['whoreadthread_option_5_Title'] = 'Show read time?';
$l['whoreadthread_option_5_Description'] = 'Select Yes if you want to display username & time, No if you want to display only Username.';

?>