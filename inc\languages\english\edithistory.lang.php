<?php
/**
 * Edit History Log
 * Copyright 2010 Starpaul20
 */

$l['edithistory_info_name'] = "Edit History Log";
$l['edithistory_info_desc'] = "Allows you to log all edits made to posts.";

$l['edit_history_pruning_ran'] = "The edit history pruning task successfully ran.";

$l['edited_time_total'] = " Edited 1 time in total.";
$l['edited_times_total'] = " Edited {1} times in total.";

$l['error_invalidpost'] = "The specified post does not exist.";
$l['error_invalidforum'] = "Invalid forum";
$l['nav_edithistory'] = "Edit History";
$l['nav_compareposts'] = "Compare Posts";
$l['edit_history'] = "Edit History of {1}";
$l['no_history'] = "This post has no edit history associated with it.";
$l['view_edit_history'] = "View Edit History";
$l['error_no_log'] = "There is no edit history available.";
$l['error_cannot_compare_other_posts'] = "You cannot compare the edit history of this post with another post.";

$l['edit_reason'] = "Edit Reason";
$l['edit_reason_postbit'] = "Edit Reason:";
$l['edited_by'] = "Edited By";
$l['date'] = "Time Edited";
$l['subject'] = "Subject";
$l['original_text'] = "Original Text";
$l['ip_address'] = "IP Address";
$l['read_more'] = "Read More";
$l['compare_posts'] = "Compare text to current post";
$l['view_original_text'] = "View Original Text";
$l['view_original_text_post'] = "View original text of this post";
$l['revert_current_post'] = "Revert current post to this version";
$l['options'] = "Options";
$l['view'] = "View";
$l['compare'] = "Compare";
$l['revert'] = "Revert";

$l['edit_as_of'] = "Edit as of {1}";
$l['post_same'] = "<em>This edit is exactly the same as the current post.</em>";

$l['comparing_edit_history'] = "Comparing Edit History of a Post";
$l['viewing_edit_history'] = "Viewing Edit History";
$l['highlight_added'] = "Text that has been added is hightlighted in <ins>green</ins>";
$l['highlight_deleted'] = "Text that has been deleted is hightlighted in <del>red</del>";

$l['revert_post_confirm'] = "Are you sure you wish to revert this post?";
$l['redirect_postreverted'] = "Thank you, this post has been reverted.<br />You will now be taken to the post.";

$l['na_deleted'] = "N/A - Been Deleted";
