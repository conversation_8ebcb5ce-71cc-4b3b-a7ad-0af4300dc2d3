<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_editpost'] = "Edit Post";
$l['edit_post'] = "Edit This Post";
$l['delete_post'] = "Delete Post";
$l['delete_q'] = "Delete?";
$l['delete_1'] = "To delete this post, check the checkbox to the left and then click the button to the right.";
$l['delete_2'] = "<b>Note:</b> If this post is the first post in a thread deleting it will result in deletion of the whole thread.";
$l['subject'] = "Subject:";
$l['your_message'] = "Your Message:";
$l['post_options'] = "Post Options:";
$l['editreason'] = "Edit Reason:";
$l['options_sig'] = "<strong>Signature:</strong> include your signature. (registered users only)";
$l['options_emailnotify'] = "<strong>Email Notification:</strong> receive an email whenever there is a new reply. (registered users only)";
$l['options_disablesmilies'] = "<strong>Disable Smilies:</strong> disable smilies from showing in this post.";
$l['preview_post'] = "Preview Post";
$l['update_post'] = "Update Post";
$l['poll'] = "Poll:";
$l['poll_desc'] = "Optionally you may attach a poll to this thread.";
$l['poll_check'] = "I want to post a poll";
$l['num_options'] = "Number of options:";
$l['max_options'] = "(Maximum: {1})";
$l['delete_now'] = "Delete Now";
$l['edit_time_limit'] = "Sorry but you cannot edit your post. The Administrator has set it so that posts can only be edited within {1} minutes of posting.";
$l['no_prefix'] = "No Prefix";

$l['redirect_nodelete'] = "The post was not deleted because you didn't check the \"Delete\" checkbox.";
$l['redirect_norestore'] = "The post was not restored because you didn't check the \"Restore\" checkbox.";
$l['redirect_postedited'] = "Thank you, this post has been edited.<br />";
$l['redirect_postedited_redirect'] = "You will now be returned to the thread.";
$l['redirect_postedited_poll'] = "Thank you, this post has been edited. <br />Because you opted to post a poll, you'll now be taken to the poll creation page.";
$l['error_invalidpost'] = "Sorry, but you seem to have followed an invalid address. Please be sure the specified post exists and try again.";
$l['redirect_threaddeleted'] = "Thank you, the thread has been deleted.<br />You will now be returned to the forum.";
$l['redirect_postdeleted'] = "Thank you, the post has been deleted.<br />You will now be returned to the thread.";
$l['redirect_threadrestored'] = "Thank you, the thread has been restored.<br />You will now be returned to the forum.";
$l['redirect_postrestored'] = "Thank you, the post has been restored.<br />You will now be returned to the thread.";
$l['redirect_threadclosed'] = "You cannot edit existing posts in this thread because it has been closed by a moderator.";
$l['redirect_post_moderation'] = "The administrator has specified that all editing of posts require moderation. You will now be returned to the thread.";
$l['redirect_thread_moderation'] = "The administrator has specified that all editing of threads require moderation. You will now be returned to the forum index.";
$l['error_already_delete'] = "Sorry, but this post is already deleted.";

$l['thread_deleted'] = "Deleted Thread Permanently";
$l['post_deleted'] = "Deleted Post Permanently";
$l['thread_soft_deleted'] = "Soft Deleted Thread";
$l['post_soft_deleted'] = "Soft Deleted Post";
$l['thread_restored'] = "Restored Thread";
$l['post_restored'] = "Restored Post";

$l['error_already_deleted'] = 'The selected post has already been deleted.';
