<?php
/*
ezGallery Lite Edition
Version 1.0
by:vbgamer45 
http://www.mybbhacks.com

############################################
License Information:
ezGallery is NOT free software.
This software may not be redistributed.

Links to http://www.mybbhacks.com must remain unless
branding free option is purchased.
#############################################

Gallery English Text Strings
*/


//Title string
$l['gallery_text_title'] = 'ezGallery';

$l['ezgallery_title'] = 'ezGallery';
$l['ezgallery_menu'] = 'Gallery';
$l['ezgallery_version'] = '1.0 Lite';
$l['ezgallery_admin'] = 'Gallery Configuration';
$l['gallery_whoonline'] = ' the gallery';

//Permissions
$l['permissiongroup_ezgallery'] = 'ezGallery';
$l['gallery_membergroup'] = 'Membergroup';

$l['gallery_update_permissions'] = 'Update Permissions';

$l['permissionname_ezgallery_view'] = 'View Gallery';
$l['permissionhelp_ezgallery_view'] = 'Allows the user to view the Gallery';
$l['cannot_ezgallery_view'] = 'You are not allowed to view the Gallery';

$l['permissionname_ezgallery_add'] = 'Add Picture';
$l['permissionhelp_ezgallery_add'] = 'Allows the user to add a picture.';
$l['cannot_ezgallery_add'] = 'You are not allowed to add a picture.';

$l['permissionname_ezgallery_edit'] = 'Edit own Picture';
$l['permissionhelp_ezgallery_edit'] = 'Allows the user to edit a picture.';
$l['cannot_ezgallery_edit'] = 'You are not allowed to edit a picture.';

$l['permissionname_ezgallery_delete'] = 'Delete own Picture';
$l['permissionhelp_ezgallery_delete'] = 'Allows the user to delete a picture.';
$l['cannot_ezgallery_delete'] = 'You are not allowed to delete a picture.';

$l['permissionname_ezgallery_comment'] = 'Leave Comments';
$l['permissionhelp_ezgallery_comment'] = 'Allows the user to leave comments on a picture.';
$l['cannot_ezgallery_comment'] = 'You are not allowed to leave comments.';

$l['permissionname_ezgallery_report'] = 'Report Pictures';
$l['permissionhelp_ezgallery_report'] = 'Allows the user to report pictures and comments.';
$l['cannot_ezgallery_report'] = 'You are not allowed to report content.';

$l['permissionname_ezgallery_autoapprove'] = 'Auto Approve Pictures';
$l['permissionhelp_ezgallery_autoapprove'] = 'Pictures do not need to wait for approval.';

$l['permissionname_ezgallery_manage'] = 'Admin Gallery';
$l['permissionhelp_ezgallery_manage'] = 'Allows the user to add/delete/edit all catagories. Delete Comments, Delete Pictures, Approve Pictures';
$l['cannot_ezgallery_manage'] = 'You are not allowed to manage gallery.';

//Main gallery table strings
$l['gallery_text_galleryname'] = 'Gallery Name';
$l['gallery_text_gallerydescription'] = 'Gallery Description';
$l['gallery_text_totalimages'] = 'Total Images';
$l['gallery_text_reorder'] = 'Reorder';
$l['gallery_text_options'] = 'Options';

$l['gallery_text_adminpanel'] = 'Gallery Admin Panel';
$l['gallery_text_addcategory'] = 'Add Category';
$l['gallery_text_editcategory'] = 'Edit Category';
$l['gallery_text_delcategory'] = 'Delete Category';
$l['gallery_text_settings'] = 'Settings';
$l['gallery_text_permissions'] = 'Permissions';
$l['gallery_text_imgwaitapproval'] = 'Images waiting approval: ';
$l['gallery_text_imgcheckapproval'] = 'Check Image Approval List';

$l['gallery_text_imgreported'] = 'Images reported: ';
$l['gallery_text_imgcheckreported'] = 'Check Reported Images';
$l['gallery_at'] = ' at ';

$l['gallery_write_error'] = 'Warning Gallery path is not writable! ';
$l['gallery_text_myimages'] = '[MyImages]';
$l['gallery_text_search'] = '[Search]';
$l['gallery_text_edit'] = '[Edit]';
$l['gallery_text_delete'] = '[Delete]';
$l['gallery_text_unapprove'] = '[UnApprove]';
$l['gallery_text_approve'] = '[Approve]';
$l['gallery_text_up'] = '[Up]';
$l['gallery_text_down'] = '[Down]';
$l['gallery_text_reportpicture'] = '[Report Picture]';
$l['gallery_text_delcomment'] = '[Delete Comment]';


$l['gallery_text_filesize'] = 'Filesize: ';
$l['gallery_text_by'] = 'By:';
$l['gallery_text_date'] = 'Date: ';
$l['gallery_text_comments'] = 'Comments';
$l['gallery_text_views'] = 'Views: ';

$l['gallery_text_addpicture'] = 'Add a picture';
$l['gallery_text_returngallery'] = 'Return to Gallery';

//Online strings
$l['gallery_who_viewgallery'] = ' are viewing this gallery.';
$l['gallery_who_viewpicture'] = ' are viewing this picture.';
$l['gallery_who_members'] = 'Members';
$l['gallery_who_hidden'] = 'Hidden';

//Form Strings
$l['gallery_form_title'] = 'Title:';
$l['gallery_form_description'] = 'Description:';
$l['gallery_form_icon'] = 'Gallery Icon Url:';

$l['gallery_warn_category'] = 'Warning this WILL DELETE this category and ALL pictures, comments, ratings that category contains...';

$l['gallery_form_addpicture'] = 'Add Picture';
$l['gallery_form_category'] =	'Category:';
$l['gallery_form_keywords'] =	'Keywords:';
$l['gallery_form_uploadpic'] = 'Upload Picture:';
$l['gallery_form_maxwidth'] = 'Max width ';
$l['gallery_form_maxheight'] = 'Max height ';
$l['gallery_form_pixels'] = ' pixels';
$l['gallery_form_additionaloptions'] = 'Additional Options:';
$l['gallery_form_allowcomments'] = 'Allow comments to be posted on this picture.';
$l['gallery_form_notapproved'] = 'Your picture will not appear in the gallery to others until it is approved.';
$l['gallery_form_editpicture'] = 'Edit Picture';
//View picture
$l['gallery_text_picstats'] = 'Picture Stats:';
$l['gallery_text_height'] = 'Height:';
$l['gallery_text_width'] = 'Width:';
$l['gallery_text_postedby'] = 'Posted by:  ';
$l['gallery_text_addcomment'] = 'Add Comment';
//Delete picture
$l['gallery_warn_deletepicture'] = 'Warning this will delete your picture and you can not restore it.';
$l['gallery_form_delpicture'] = 'Delete Picture';


$l['gallery_form_comment'] = 'Comment:';

$l['gallery_form_reportpicture'] = 'Report Picture';

//Gallery admin pics
$l['gallery_form_managecats'] = 'Manage Categories';
$l['gallery_form_approveimages'] = 'Approve Images';
$l['gallery_form_reportimages'] = 'Reported Images';

//Settings page

$l['gallery_set_maxheight'] = 'Max allowed image height: ';
$l['gallery_set_maxwidth'] = 'Max allowed image width: ';
$l['gallery_set_filesize'] = 'Max image filesize: ';

$l['gallery_set_thumb_height'] = 'Max thumbnail height:';
$l['gallery_set_thumb_width'] = 'Max thumbnail width:';


$l['gallery_set_path'] = 'Gallery Path: ';
$l['gallery_set_url'] = 'Gallery Url: ';
$l['gallery_set_whoonline'] = 'Display who is viewing a picture';
$l['gallery_set_images_per_page'] = 'Images per page:';
$l['gallery_set_images_per_row'] = 'Images per row:';


// BBC and Image Links code
$l['gallery_txt_image_linking'] = 'Image Linking Codes';
$l['gallery_set_showcode_bbc_image'] = 'Show BBC Code';
$l['gallery_set_showcode_directlink'] = 'Show Direct Link Code';
$l['gallery_set_showcode_htmllink'] = 'Show Html Link Code';


$l['gallery_txt_bbcimage'] = 'BB Code';
$l['gallery_txt_directlink'] = 'Direct Link';
$l['gallery_txt_htmllink'] = 'Html Link';


$l['gallery_set_commentschoice'] = 'Allow the user to toggle if comments can be shown on a picture or not.';
$l['gallery_set_permissionnotice'] = 'Lastly! Do not forget to make the permissions are set for each group, in order for them to view and add to the gallery.';
$l['gallery_set_editpermissions'] = 'Edit Permissions';

//Approve list
$l['gallery_app_image'] = 'Image';
$l['gallery_app_title'] = 'Title';
$l['gallery_app_description'] = 'Description';
$l['gallery_app_date'] = 'Date';
$l['gallery_app_membername'] = 'Member Name';

//Report list
$l['gallery_rep_piclink'] = 'Picture Link';
$l['gallery_rep_reportby'] = 'Reported by';
$l['gallery_rep_comment'] = 'Comment';
$l['gallery_rep_viewpic'] = 'View Picture';
$l['gallery_rep_deletepic'] = '[Delete Picture]';
$l['gallery_rep_delete'] = '[Delete Report]';

//Search Page
$l['gallery_search_pic'] = 'Search for picture';
$l['gallery_search_for'] = 'Search For:';
$l['gallery_search_title'] = 'Search Picture Title';
$l['gallery_search_description'] = 'Search Picture Description';
$l['gallery_search_keyword'] = 'Search Keywords';
$l['gallery_search'] = 'Search';

$l['gallery_searchresults'] = 'Search Results';

//My Images
$l['gallery_myimages'] = 'MyImages';
$l['gallery_myimages_app'] = 'Picture Approved';
$l['gallery_myimages_notapp'] = 'Picture Not Approved';

$l['gallery_save_settings'] = 'Save Settings';

$l['gallery_error_cat_title'] = 'You need to enter a category title!';
$l['gallery_error_no_pic_selected'] = 'No picture selected.';
$l['gallery_error_pic_notapproved'] = 'This picture is not approved yet and you do not have permission to view it.';
$l['gallery_error_no_title'] = 'You need to enter a picture title.';
$l['gallery_error_no_cat'] = 'You need to select a category.';
$l['gallery_error_invalid_picture'] = 'Not a valid picture file.';
$l['gallery_error_img_size_height'] = 'Image exceeds size requirements. Your height was:  ';
$l['gallery_error_img_size_width'] = ' Your width was: ';
$l['gallery_error_img_filesize'] = 'Picture exceeds max filesize. Max Filesize is ';
$l['gallery_error_no_picture'] = 'No uploaded picture found.';
$l['gallery_error_noedit_permission'] = 'You do not have permission to edit that picture.';
$l['gallery_error_nodelete_permission'] = 'You do not have permission to delete that picture.';
$l['gallery_error_no_comment'] = 'You did not enter a comment!';
$l['gallery_error_not_allowcomment'] = 'Wait a second...This picture does not allow comments...';
$l['gallery_error_no_com_selected'] = 'No comment selected.';
$l['gallery_error_no_user_selected'] = 'No user selected.';
$l['gallery_error_no_report_selected'] = 'No report selected.';
$l['gallery_error_no_search'] = 'You did not enter anything to search for...';
$l['gallery_error_search_small'] = 'Search string is too small needs to be greater than three characters.';

$l['gallery_guest'] = 'Guest';
$l['gallery_text_oldpicture'] = 'Old Picture';

$l['gallery_nocatabove'] = 'There is no category above the current one.';
$l['gallery_nocatbelow'] = 'There is no category below the current one.';

$l['gallery_nopicsincategory'] = 'There are no pictures inside this category';

$l['gallery_text_posts'] = 'Posts:';
$l['gallery_text_pages'] = 'Pages: ';

// Latest Version: 
$l['gallery_txt_latestversion'] = 'Latest Version: ';
$l['gallery_txt_yourversion'] = 'Your Version: ';
$l['gallery_txt_version_outofdate'] = 'Your gallery version is not up to date!'; 

$l['gallery_error_category'] = 'Category not found';

$l['gallery_upload_max_filesize'] = 'PHP: Upload Max Filesize ';
$l['gallery_post_max_size'] = 'PHP: Post Max Filesize ';
$l['gallery_upload_limits_notes'] = 'The above PHP settings control how large a file will be accepeted by the gallery. These are settings are controlled by the php.ini or your webhost';


// Tabs description
$l['gallery_set_description'] = 'Allows you to edit the important settings for your gallery.';
$l['gallery_managecats_description'] = 'This area allows you to create and manage gallery categories';
$l['gallery_approveimages_description'] = 'Approve/unapprove images that are waiting for approval';
$l['gallery_reportimages_description'] = 'View reported images';
$l['gallery_permissions_description'] = 'Setup membergroup level permissions for the gallery';

$l['gallery_txt_goback'] = 'Go Back';


$l['guest'] = 'Guest';
$l['guests'] = 'Guests';
$l['who_and'] = ' and ';

?>