<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['post_thread'] = "Post Thread";
$l['moderated_by'] = "Moderated By:";
$l['nothreads'] = "Sorry, but there are currently no threads in this forum with the specified date and time limiting options.";
$l['nopermission'] = "Sorry, but you do not have permission to view threads in this forum.";
$l['search_forum'] = "Search this Forum:";
$l['thread'] = "Thread";
$l['author'] = "Author";
$l['replies'] = "Replies";
$l['views'] = "Views";
$l['lastpost'] = "Last Post";
$l['rating'] = "Rating";
$l['prefix'] = "Prefix:";
$l['prefix_all'] = "Prefix: Any/No Prefix";
$l['prefix_any'] = "Prefix: Any Prefix";
$l['prefix_none'] = "Prefix: No Prefix";
$l['markforum_read'] = "Mark this forum read";
$l['subscribe_forum'] = "Subscribe to this forum";
$l['unsubscribe_forum'] = "Unsubscribe from this forum";
$l['clear_stored_password'] = "Clear stored forum password";
$l['sort_by_subject'] = "Sort by: Subject";
$l['sort_by_lastpost'] = "Sort by: Last Post";
$l['sort_by_starter'] = "Sort by: Author";
$l['sort_by_started'] = "Sort by: Creation Time";
$l['sort_by_rating'] = "Sort by: Rating";
$l['sort_by_replies'] = "Sort by: Replies";
$l['sort_by_views'] = "Sort by: Views";
$l['sort_order_asc'] = "Order: Ascending";
$l['sort_order_desc'] = "Order: Descending";
$l['datelimit_1day'] = "From: Today";
$l['datelimit_5days'] = "From: 5 Days Ago";
$l['datelimit_10days'] = "From: 10 Days Ago";
$l['datelimit_20days'] = "From: 20 Days Ago";
$l['datelimit_50days'] = "From: 50 Days Ago";
$l['datelimit_75days'] = "From: 75 Days Ago";
$l['datelimit_100days'] = "From: 100 Days Ago";
$l['datelimit_lastyear'] = "From: The Last Year";
$l['datelimit_beginning'] = "From: The Beginning";
$l['new_thread'] = "New Posts";
$l['new_hot_thread'] = "Hot Thread (New)";
$l['posts_by_you'] = "Contains Posts by You";
$l['no_new_thread'] = "No New Posts";
$l['hot_thread'] = "Hot Thread (No New)";
$l['closed_thread'] = "Closed Thread";
$l['goto_first_unread'] = "Go to first unread post";
$l['pages'] = "Pages:";
$l['pages_last'] = "last";
$l['users_browsing_forum'] = "Users browsing this forum:";
$l['users_browsing_forum_guests'] = "{1} Guest(s)";
$l['users_browsing_forum_invis'] = "{1} Invisible User(s)";
$l['delayed_moderation'] = "Delayed Moderation";
$l['inline_thread_moderation'] = "Inline Thread Moderation:";
$l['close_threads'] = "Close Threads";
$l['open_threads'] = "Open Threads";
$l['stick_threads'] = "Stick Threads";
$l['unstick_threads'] = "Unstick Threads";
$l['soft_delete_threads'] = "Soft Delete Threads";
$l['restore_threads'] = "Restore Threads";
$l['delete_threads'] = "Delete Threads Permanently";
$l['move_threads'] = "Move / Copy Threads";
$l['approve_threads'] = "Approve Threads";
$l['unapprove_threads'] = "Unapprove Threads";
$l['inline_go'] = "Go";
$l['clear'] = "Clear";
$l['sub_forums_in']  = "Forums in '{1}'";
$l['forum_rules'] = "{1} - Rules";
$l['subforums'] = "Sub Forums:";
$l['asc'] = "asc";
$l['desc'] = "desc";
$l['forum_announcements'] = "Forum Announcements";
$l['sticky_threads'] = "Important Threads";
$l['normal_threads'] = "Normal Threads";
$l['icon_dot'] = "Contains posts by you. "; // The spaces for the icon labels are strategically placed so that there should be no extra space at the beginning or end of the resulting label and that spaces separate each 'status' ;)
$l['icon_no_new'] = "No new posts.";
$l['icon_new'] = "New posts.";
$l['icon_hot'] = " Hot thread.";
$l['icon_close'] = " Closed thread.";
$l['attachment_count'] = "This thread contains 1 attachment.";
$l['attachment_count_multiple'] = "This thread contains {1} attachments.";
$l['rss_discovery_forum'] = "Latest Threads in {1}";
$l['forum_unapproved_posts_count'] = "There are currently {1} unapproved posts in this forum.";
$l['forum_unapproved_post_count'] = "There is currently 1 unapproved post in this forum.";
$l['forum_unapproved_threads_count'] = "There are currently {1} unapproved threads in this forum.";
$l['forum_unapproved_thread_count'] = "There is currently 1 unapproved thread in this forum.";
$l['thread_unapproved_posts_count'] = "There are currently {1} unapproved posts in this thread.";
$l['thread_unapproved_post_count'] = "There is currently 1 unapproved post in this thread.";
$l['page_selected'] = "All <strong>{1}</strong> threads on this page are selected.";
$l['all_selected'] = "All <strong>{1}</strong> threads in this forum are selected.";
$l['select_all'] = "Select all <strong>{1}</strong> threads in this forum.";
$l['clear_selection'] = "Clear Selection.";
$l['deleted_thread'] = "Deleted Thread";
$l['select_a_tool'] = "Select A Tool";
$l['error_containsnoforums'] = "Sorry, but the forum you are currently viewing does not contain any child forums.";
$l['inline_no_tool_selected'] = "Please select a tool to perform moderation action.";
$l['inline_no_post_selected'] = "You need to select one or more posts to perform the moderation action on.";
$l['inline_edit_description'] = '(Click and hold to edit)';