<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['redirect_width'] = "50%";
$l['lastvisit_never'] = "Never";
$l['lastvisit_hidden'] = "(Hidden)";

$l['search_button'] = 'Search';
$l['toplinks_memberlist'] = "Member List";
$l['toplinks_search'] = "Search";
$l['toplinks_calendar'] = "Calendar";
$l['toplinks_help'] = "Help";
$l['toplinks_portal'] = "Portal";
$l['bottomlinks_forumteam'] = "Forum Team";
$l['bottomlinks_contactus'] = "Contact Us";
$l['bottomlinks_returntop'] = "Return to Top";
$l['bottomlinks_syndication'] = "RSS Syndication";
$l['bottomlinks_litemode'] = "Lite (Archive) Mode";
$l['bottomlinks_markread'] = "Mark all forums read";

$l['welcome_usercp'] = "User CP";
$l['welcome_modcp'] = "Mod CP";
$l['welcome_admin'] = "Admin CP";
$l['welcome_logout'] = "Log Out";
$l['welcome_login'] = "Login";
$l['welcome_register'] = "Register";
$l['welcome_open_buddy_list'] = "Open Buddy List";
$l['welcome_newposts'] = "View New Posts";
$l['welcome_todaysposts'] = "View Today's Posts";
$l['welcome_pms'] = "Private Messages";
$l['welcome_pms_usage'] = "(Unread {1}, Total {2})";
$l['welcome_back'] = "<strong>Welcome back, {1}</strong>. You last visited: {2}";
$l['welcome_guest'] = "Hello There, Guest!";
$l['welcome_current_time'] = "<strong>Current time:</strong> {1}";

$l['moved_prefix'] = "Moved:";
$l['poll_prefix'] = "Poll:";

$l['forumbit_announcements'] = "Announcements";
$l['forumbit_stickies'] = "Important Threads";
$l['forumbit_forum'] = "Forum";
$l['forumbit_threads'] = "Threads";
$l['forumbit_posts'] = "Posts";
$l['forumbit_lastpost'] = "Last Post";
$l['forumbit_moderated_by'] = "Moderated By:";
$l['new_posts'] = "Forum Contains New Posts";
$l['no_new_posts'] = "Forum Contains No New Posts";
$l['click_mark_read'] = "Click to mark this forum as read";
$l['forum_closed'] = "Forum is Closed";
$l['forum_redirect'] = "Redirect Forum";
$l['lastpost_never'] = "Never";
$l['viewing_one'] = " (1 user browsing)";
$l['viewing_multiple'] = " ({1} users browsing)";
$l['by'] = "by";
$l['more_subforums'] = "and {1} more.";

$l['password_required'] = "Password Required";
$l['forum_password_note'] = "The administrator has required it so that a password is required for access to this forum.";
$l['enter_password_below'] = "Please enter the password below:";
$l['verify_forum_password'] = "Verify Forum Password";
$l['wrong_forum_password'] = "The password you entered is incorrect. Please try again.";

$l['reset_button'] = "Reset";
$l['username'] = "Username:";
$l['username1'] = "Email:";
$l['username2'] = "Username/Email:";
$l['password'] = "Password:";
$l['login_username'] = "Username:";
$l['login_username1'] = "Email:";
$l['login_username2'] = "Username/Email:";
$l['login_password'] = "Password:";
$l['lost_password'] = "Lost Password?";
$l['remember_me'] = "Remember me";
$l['remember_me_desc'] = "If ticked, your login details will be remembered on this computer, otherwise, you will be logged out as soon as you close your browser.";

$l['month_1'] = "January";
$l['month_2'] = "February";
$l['month_3'] = "March";
$l['month_4'] = "April";
$l['month_5'] = "May";
$l['month_6'] = "June";
$l['month_7'] = "July";
$l['month_8'] = "August";
$l['month_9'] = "September";
$l['month_10'] = "October";
$l['month_11'] = "November";
$l['month_12'] = "December";

$l['sunday'] = "Sunday";
$l['monday'] = "Monday";
$l['tuesday'] = "Tuesday";
$l['wednesday'] = "Wednesday";
$l['thursday'] = "Thursday";
$l['friday'] = "Friday";
$l['saturday'] = "Saturday";
$l['short_monday'] = "M";
$l['short_tuesday'] = "T";
$l['short_wednesday'] = "W";
$l['short_thursday'] = "T";
$l['short_friday'] = "F";
$l['short_saturday'] = "S";
$l['short_sunday'] = "S";

$l['yes'] = "Yes";
$l['no'] = "No";

$l['and'] = "and";
$l['date'] = "Date";

$l['nobody'] = "Nobody";

$l['attachments'] = "Attachments";
$l['attachments_desc'] = "Optionally you may attach one or more attachments to this post. Please select the file on the right and click 'Add Attachment' to upload it.";
$l['remove_attachment'] = "Remove";
$l['approve_attachment'] = "Approve";
$l['unapprove_attachment'] = "Unapprove";
$l['insert_attachment_post'] = "Insert Into Post";
$l['new_attachment'] = "New Attachment:";
$l['add_attachment'] = "Add Attachment";
$l['update_attachment'] = "Update Attachment";
$l['attachment_missing'] = "Please select one or more files before attempting to attach.";
$l['attachment_too_many_files'] = "You can upload a maximum of {1} files at once.";
$l['attachment_max_allowed_files'] = "You can attach {1} more file(s) to this post.";
$l['attachment_too_big_upload'] = "You can upload a maximum of {1} MB at once.";
$l['drop_files'] = "Click or drop some files here to upload...";
$l['upload_initiate'] = "Release to initiate upload...";
$l['post_preview'] = "Preview";
$l['change_user'] = "change user";
$l['post_icon'] = "Post Icon:";
$l['no_post_icon'] = "no icon";
$l['thread_subscription_method'] = "Thread Subscription:";
$l['thread_subscription_method_desc'] = "Specify the type of notification and thread subscription you'd like to have to this thread. (Registered users only)";
$l['no_subscribe'] = "Do not subscribe to this thread";
$l['no_subscribe_notification'] = "Subscribe without receiving any notification of new replies";
$l['instant_email_subscribe'] = "Subscribe and receive email notification of new replies";
$l['instant_pm_subscribe'] = "Subscribe and receive PM notification of new replies";

$l['today_rel'] = "<span title=\"{1}\">Today</span>";
$l['yesterday_rel'] = "<span title=\"{1}\">Yesterday</span>";
$l['today'] = "Today";
$l['yesterday'] = "Yesterday";
$l['error'] = "Board Message";

$l['multipage_pages'] = "Pages ({1}):";
$l['multipage_last'] = "Last";
$l['multipage_first'] = "First";
$l['multipage_next'] = "Next";
$l['multipage_previous'] = "Previous";
$l['multipage_link_start'] = " &hellip;";
$l['multipage_link_end'] = "&hellip; ";
$l['multipage_jump'] = "Jump to page";

$l['editor_bold'] = "Bold";
$l['editor_italic'] = "Italic";
$l['editor_underline'] = "Underline";
$l['editor_strikethrough'] = "Strikethrough";
$l['editor_subscript'] = "Subscript";
$l['editor_superscript'] = "Superscript";
$l['editor_alignleft'] = "Align left";
$l['editor_center'] = "Center";
$l['editor_alignright'] = "Align right";
$l['editor_justify'] = "Justify";
$l['editor_fontname'] = "Font Name";
$l['editor_fontsize'] = "Font Size";
$l['editor_fontcolor'] = "Font Color";
$l['editor_removeformatting'] = "Remove Formatting";
$l['editor_cut'] = "Cut";
$l['editor_copy'] = "Copy";
$l['editor_paste'] = "Paste";
$l['editor_cutnosupport'] = "Your browser does not allow the cut command. Please use the keyboard shortcut Ctrl/Cmd-X";
$l['editor_copynosupport'] = "Your browser does not allow the copy command. Please use the keyboard shortcut Ctrl/Cmd-C";
$l['editor_pastenosupport'] = "Your browser does not allow the paste command. Please use the keyboard shortcut Ctrl/Cmd-V";
$l['editor_pasteentertext'] = "Paste your text inside the following box:";
$l['editor_pastetext'] = "Paste Text";
$l['editor_numlist'] = "Numbered list";
$l['editor_bullist'] = "Bullet list";
$l['editor_undo'] = "Undo";
$l['editor_redo'] = "Redo";
$l['editor_rows'] = "Rows:";
$l['editor_cols'] = "Cols:";
$l['editor_inserttable'] = "Insert a table";
$l['editor_inserthr'] = "Insert a horizontal rule";
$l['editor_code'] = "Code";
$l['editor_php'] = "PHP";
$l['editor_width'] = "Width (optional):";
$l['editor_height'] = "Height (optional):";
$l['editor_insertimg'] = "Insert an image";
$l['editor_email'] = "E-mail:";
$l['editor_insertemail'] = "Insert an email";
$l['editor_url'] = "URL:";
$l['editor_insertlink'] = "Insert a link";
$l['editor_unlink'] = "Unlink";
$l['editor_more'] = "More";
$l['editor_insertemoticon'] = "Insert an emoticon";
$l['editor_videourl'] = "Video URL:";
$l['editor_videotype'] = "Video Type:";
$l['editor_insert'] = "Insert";
$l['editor_insertyoutubevideo'] = "Insert a YouTube video";
$l['editor_currentdate'] = "Insert current date";
$l['editor_currenttime'] = "Insert current time";
$l['editor_print'] = "Print";
$l['editor_viewsource'] = "View source";
$l['editor_description'] = "Description (optional):";
$l['editor_enterimgurl'] = "Enter the image URL:";
$l['editor_enteremail'] = "Enter the e-mail address:";
$l['editor_enterdisplayedtext'] = "Enter the displayed text:";
$l['editor_enterurl'] = "Enter URL:";
$l['editor_enteryoutubeurl'] = "Enter the YouTube video URL or ID:";
$l['editor_insertquote'] = "Insert a Quote";
$l['editor_invalidyoutube'] = "Invalid YouTube video";
$l['editor_dailymotion'] = "Dailymotion";
$l['editor_metacafe'] = "MetaCafe";
$l['editor_mixer'] = "Mixer";
$l['editor_vimeo'] = "Vimeo";
$l['editor_youtube'] = "Youtube";
$l['editor_twitch'] = "Twitch";
$l['editor_facebook'] = "Facebook";
$l['editor_liveleak'] = "LiveLeak";
$l['editor_insertvideo'] = "Insert a video";
$l['editor_maximize'] = "Maximize";

$l['quote'] = "Quote:";
$l['wrote'] = "Wrote:";
$l['code'] = "Code:";
$l['php_code'] = "PHP Code:";
$l['posted_image'] = "[Image: {1}]";
$l['posted_video'] = "[Video: {1}]";
$l['linkback'] = "Original Post";

$l['at'] = "at";
$l['na'] = "N/A";
$l['guest'] = "Guest";
$l['unknown'] = "Unknown";
$l['never'] = "Never";
$l['postbit_posts'] = "Posts:";
$l['postbit_threads'] = "Threads:";
$l['postbit_group'] = "Group:";
$l['postbit_joined'] = "Joined:";
$l['postbit_status'] = "Status:";
$l['postbit_attachments'] = "Attached Files";
$l['postbit_attachment_filename'] = "Filename:";
$l['postbit_attachment_size'] = "Size:";
$l['postbit_attachment_downloads'] = "Downloads:";
$l['postbit_attachments_images'] = "Image(s)";
$l['postbit_attachments_thumbnails'] = "Thumbnail(s)";
$l['postbit_unapproved_attachments'] = "{1} unapproved attachments.";
$l['postbit_unapproved_attachment'] = "1 unapproved attachment.";
$l['postbit_status_online'] = "Online";
$l['postbit_status_offline'] = "Offline";
$l['postbit_status_away'] = "Away";
$l['postbit_edited'] = "This post was last modified: {1} by";
$l['postbit_editreason'] = "Edit Reason";
$l['postbit_ipaddress'] = "IP Address:";
$l['postbit_ipaddress_logged'] = "Logged";
$l['postbit_post'] = "Post:";
$l['postbit_reputation'] = "Reputation:";
$l['postbit_reputation_add'] = "Give Reputation to this user";
$l['postbit_website'] = "Visit this user's website";
$l['postbit_email'] = "Send this user an email";
$l['postbit_find'] = "Find all posts by this user";
$l['postbit_report'] = "Report this post to a moderator";
$l['postbit_quote'] = "Quote this message in a reply";
$l['postbit_qdelete_post'] = "Delete this post";
$l['postbit_qdelete_thread'] = "Delete this thread";
$l['postbit_qrestore_post'] = "Restore this post";
$l['postbit_qrestore_thread'] = "Restore this thread";
$l['postbit_profile'] = "View this users profile";
$l['postbit_pm'] = "Send this user a private message";
$l['postbit_edit'] = "Edit this post";
$l['postbit_multiquote'] = "Quote this post";
$l['postbit_quick_edit'] = "Quick Edit";
$l['postbit_full_edit'] = "Full Edit";
$l['postbit_show_ignored_post'] = "Show this Post";
$l['postbit_currently_ignoring_user'] = "The contents of this message are hidden because {1} is on your <a href=\"usercp.php?action=editlists\">ignore list</a>.";
$l['postbit_post_under_moderation'] = "The post made by you is under moderation and currently not visible publicly. The post will be visible to everyone once a moderator approves it.";
$l['postbit_warning_level'] = "Warning Level:";
$l['postbit_warn'] = "Warn the author for this post";
$l['postbit_purgespammer'] = "Purge Spammer";
$l['postbit_post_deleted'] = "This post has been deleted.";
$l['postbit_post_unapproved'] = "This post is awaiting approval.";
$l['postbit_thread_deleted'] = "This thread has been deleted.";
$l['postbit_thread_unapproved'] = "This thread is awaiting approval.";
$l['postbit_deleted_post_user'] = "This post by {1} has been deleted.";

$l['postbit_button_reputation_add'] = 'Rate';
$l['postbit_button_website'] = 'Website';
$l['postbit_button_email'] = 'Email';
$l['postbit_button_find'] = 'Find';
$l['postbit_button_report'] = 'Report';
$l['postbit_button_quote'] = 'Reply';
$l['postbit_button_qdelete'] = 'Delete';
$l['postbit_button_qrestore'] = 'Restore';
$l['postbit_button_profile'] = 'Profile';
$l['postbit_button_pm'] = 'PM';
$l['postbit_button_warn'] = 'Warn';
$l['postbit_button_edit'] = 'Edit';
$l['postbit_button_multiquote'] = 'Quote';
$l['postbit_button_reply_pm'] = 'Reply';
$l['postbit_button_reply_all'] = 'Reply All';
$l['postbit_button_forward'] = 'Forward';
$l['postbit_button_delete_pm'] = 'Delete';
$l['postbit_button_purgespammer'] = "Purge Spammer";

$l['forumjump'] = "Forum Jump:";
$l['forumjump_pms'] = "Private Messages";
$l['forumjump_usercp'] = "User Control Panel";
$l['forumjump_wol'] = "Who's Online";
$l['forumjump_search'] = "Search";
$l['forumjump_home'] = "Forum Home";

$l['confirm_title'] = "Please Confirm";
$l['redirect'] = "You will now be redirected";
$l['unknown_error'] = "An unknown error has occurred.";
$l['post_fetch_error'] = 'There was an error fetching the posts.';
$l['ratings_update_error'] = 'There was an error updating the rating.';

$l['smilieinsert'] = "Smilies";
$l['smilieinsert_getmore'] = "get more";
$l['on'] = "On";
$l['off'] = "Off";
$l['remote_avatar_disabled_default_avatar'] = "You are currently using a remote avatar, which has been disabled. The default avatar will be used instead.";
$l['mod_notice'] = "Awaiting Moderation: {1}.";
$l['unapproved_thread'] = "1 unapproved thread";
$l['unapproved_threads'] = "{1} unapproved threads";
$l['unapproved_post'] = "1 unapproved post";
$l['unapproved_posts'] = "{1} unapproved posts";
$l['unapproved_attachment'] = "1 unapproved attachment";
$l['unapproved_attachments'] = "{1} unapproved attachments";
$l['unread_report'] = "1 unread report";
$l['unread_reports'] = "{1} unread reports";
$l['groupleader_notice'] = "Group Leader Notice:";
$l['pending_joinrequest'] = "1 pending group membership join request.";
$l['pending_joinrequests'] = "{1} pending group membership join requests.";

$l['search_user'] = "Search for a user";

$l['year'] = "Year";
$l['year_short'] = "y";
$l['years'] = "Years";
$l['years_short'] = "y";
$l['month'] = "Month";
$l['month_short'] = "m";
$l['months'] = "Months";
$l['months_short'] = "m";
$l['week'] = "Week";
$l['week_short'] = "w";
$l['weeks'] = "Weeks";
$l['weeks_short'] = "w";
$l['day'] = "Day";
$l['day_short'] = "d";
$l['days'] = "Days";
$l['days_short'] = "d";
$l['hour'] = "Hour";
$l['hour_short'] = "h";
$l['hours'] = "Hours";
$l['hours_short'] = "h";
$l['minute'] = "Minute";
$l['minute_short'] = "m";
$l['minutes'] = "Minutes";
$l['minutes_short'] = "m";
$l['second'] = "Second";
$l['second_short'] = "s";
$l['seconds'] = "Seconds";
$l['seconds_short'] = "s";

$l['rel_in'] = "In ";
$l['rel_ago'] = "ago";
$l['rel_less_than'] = "Less than ";
$l['rel_time'] = "<span title=\"{5}{6}\">{1}{2} {3} {4}</span>";
$l['rel_minutes_single'] = "minute";
$l['rel_minutes_plural'] = "minutes";
$l['rel_hours_single'] = "hour";
$l['rel_hours_plural'] = "hours";

$l['permanent'] = "Permanent";
$l['save_draft'] = "Save as Draft";
$l['go'] = "Go";
$l['bbclosed_warning'] = "Your board status is currently set to closed.";
$l['banned_warning'] = "Your forum account is currently banned.";
$l['banned_warning2'] = "Ban Reason";
$l['banned_warning3'] = "Ban will be lifted";
$l['banned_lifted_never'] = "Never";
$l['banned_email_warning'] = "You are currently using an email that is not allowed to be used on this forum. Please reset it before continuing.";
$l['powered_by'] = "Powered By";
$l['copyright'] = "Copyright";
$l['attach_quota'] = "Your allocated attachment usage quota is {1}.";
$l['attach_usage'] = "You are currently using <strong>{1}</strong>.";
$l['view_attachments'] = "[View My Attachments]";
$l['unlimited'] = "Unlimited";

$l['click_hold_edit'] = "(Click and hold to edit)";

$l['guest_count'] = "1 Guest";
$l['guest_count_multiple'] = "{1} Guests";

$l['size_yb'] = "YB";
$l['size_zb'] = "ZB";
$l['size_eb'] = "EB";
$l['size_pb'] = "PB";
$l['size_tb'] = "TB";
$l['size_gb'] = "GB";
$l['size_mb'] = "MB";
$l['size_kb'] = "KB";
$l['size_bytes'] = "bytes";

$l['slaps'] = "slaps";
$l['with_trout'] = "around a bit with a large trout.";

$l['mybb_engine'] = "MyBB Engine";
$l['quickdelete_confirm'] = "Are you sure you want to delete this post?";
$l['quickrestore_confirm'] = "Are you sure you want to restore this post?";
$l['newpm_notice_one'] = "<strong>You have one unread private message</strong> from {1} titled <a href=\"{2}/private.php?action=read&amp;pmid={3}\" style=\"font-weight: bold;\">{4}</a>";
$l['newpm_notice_multiple'] = "<strong>You have {1} unread private messages.</strong> The most recent is from {2} titled <a href=\"{3}/private.php?action=read&amp;pmid={4}\" style=\"font-weight: bold;\">{5}</a>";
$l['deleteevent_confirm'] = "Are you sure you want to delete this event?";
$l['removeattach_confirm'] = "Are you sure you want to remove the selected attachment from this post?";

$l['latest_threads'] = "Latest Threads";

$l['folder_inbox'] = "Inbox";
$l['folder_unread'] = "Unread";
$l['folder_sent_items'] = "Sent Items";
$l['folder_drafts'] = "Drafts";
$l['folder_trash'] = "Trash Can";
$l['folder_untitled'] = "Untitled Folder";

$l['standard_mod_tools'] = "Standard Tools";
$l['custom_mod_tools'] = "Custom Tools";

$l['error_loadlimit'] = "The maximum server load limit has been reached.  Please check back later once the server is less busy.";
$l['error_boardclosed'] = "This bulletin board is currently closed. The Administrator has specified the reason as to why below.";
$l['error_banned'] = "I'm sorry, but you are banned.  You may not post, read threads, or access the forum.  Please contact your forum administrator should you have any questions.";
$l['error_cannot_upload_php_post'] = "Can not upload file - Too large for php post_max_size directive. Please press the back button.";
$l['error_empty_post_input'] = "There has been an error due to your post data being empty. This could be due to a browser page refresh or direct access to this page. We recommend you press the browser back button and begin again.";
$l['error_database_repair'] = "MyBB is automatically repairing a crashed table.";

$l['unknown_user_trigger'] = "An unknown error has been triggered.";
$l['warnings'] = "The following warnings occurred:";

$l['ajax_loading'] = "Loading. <br />Please Wait&hellip;";
$l['saving_changes'] = "Saving changes&hellip;";
$l['refresh'] = "Refresh";
$l['select_language'] = "Quick Language Select";
$l['select_theme'] = "Quick Theme Select";

$l['invalid_post_code'] = "Authorization code mismatch. Are you accessing this function correctly? Please go back and try again.";
$l['invalid_nocaptcha'] = "Please solve the reCAPTCHA to verify that you're not a robot.";
$l['invalid_hcaptcha'] = "Please solve the hCaptcha to verify that you're not a robot.";
$l['invalid_captcha_verify'] = "The image verification code that you entered was incorrect. Please enter the code exactly how it appears in the image.";
$l['image_verification'] = "Image Verification";
$l['human_verification'] = "Human Verification";
$l['verification_note'] = "Please enter the text contained within the image into the text box below it. This process is used to prevent automated spam bots.";
$l['verification_note_nocaptcha'] = "Please tick the checkbox that you see below. This process is used to prevent automated spam bots.";
$l['verification_note_hcaptcha'] = "Please tick the checkbox that you see below. This process is used to prevent automated spam bots.";
$l['verification_subnote'] = "(case insensitive)";
$l['invalid_nocaptcha_transmit'] = "An error occurred with the human verification by reCAPTCHA. Please try again.";
$l['invalid_hcaptcha_transmit'] = "An error occurred with the human verification by hCaptcha. Please try again.";
$l['captcha_fetch_failure'] = 'There was an error fetching the new captcha.';
$l['question_fetch_failure'] = 'There was an error fetching the new question.';

$l['timezone_gmt_minus_1200'] = "(GMT -12:00) Howland and Baker Islands";
$l['timezone_gmt_minus_1100'] = "(GMT -11:00) Nome, Midway Island";
$l['timezone_gmt_minus_1000'] = "(GMT -10:00) Hawaii, Papeete";
$l['timezone_gmt_minus_950'] = "(GMT -9:30) Marquesas Islands";
$l['timezone_gmt_minus_900'] = "(GMT -9:00) Alaska";
$l['timezone_gmt_minus_800'] = "(GMT -8:00) Pacific Time";
$l['timezone_gmt_minus_700'] = "(GMT -7:00) Mountain Time";
$l['timezone_gmt_minus_600'] = "(GMT -6:00) Central Time, Mexico City";
$l['timezone_gmt_minus_500'] = "(GMT -5:00) Eastern Time, Bogota, Lima, Quito";
$l['timezone_gmt_minus_450'] = "(GMT -4:30) Caracas";
$l['timezone_gmt_minus_400'] = "(GMT -4:00) Atlantic Time, La Paz, Halifax";
$l['timezone_gmt_minus_350'] = "(GMT -3:30) Newfoundland";
$l['timezone_gmt_minus_300'] = "(GMT -3:00) Brazil, Buenos Aires, Georgetown, Falkland Is.";
$l['timezone_gmt_minus_200'] = "(GMT -2:00) Mid-Atlantic, South Georgia and the South Sandwich Islands";
$l['timezone_gmt_minus_100'] = "(GMT -1:00) Azores, Cape Verde Islands";
$l['timezone_gmt'] = "(GMT) Casablanca, Dublin, Edinburgh, London, Lisbon, Monrovia";
$l['timezone_gmt_100'] = "(GMT +1:00) Berlin, Bratislava, Brussels, Copenhagen, Madrid, Paris, Prague, Rome, Warsaw";
$l['timezone_gmt_200'] = "(GMT +2:00) Athens, Istanbul, Cairo, Jerusalem, South Africa";
$l['timezone_gmt_300'] = "(GMT +3:00) Kaliningrad, Minsk, Baghdad, Riyadh, Nairobi";
$l['timezone_gmt_350'] = "(GMT +3:30) Tehran";
$l['timezone_gmt_400'] = "(GMT +4:00) Moscow, Abu Dhabi, Baku, Muscat, Tbilisi";
$l['timezone_gmt_450'] = "(GMT +4:30) Kabul";
$l['timezone_gmt_500'] = "(GMT +5:00) Islamabad, Karachi, Tashkent";
$l['timezone_gmt_550'] = "(GMT +5:30) Mumbai, Kolkata, Chennai, New Delhi";
$l['timezone_gmt_575'] = "(GMT +5:45) Kathmandu";
$l['timezone_gmt_600'] = "(GMT +6:00) Almaty, Dhaka, Yekaterinburg";
$l['timezone_gmt_650'] = "(GMT +6:30) Yangon";
$l['timezone_gmt_700'] = "(GMT +7:00) Bangkok, Hanoi, Jakarta";
$l['timezone_gmt_800'] = "(GMT +8:00) Beijing, Hong Kong, Perth, Singapore, Taipei, Manila";
$l['timezone_gmt_850'] = "(GMT +8:30) Pyongyang";
$l['timezone_gmt_875'] = "(GMT +8:45) Eucla";
$l['timezone_gmt_900'] = "(GMT +9:00) Osaka, Sapporo, Seoul, Tokyo, Irkutsk";
$l['timezone_gmt_950'] = "(GMT +9:30) Adelaide, Darwin";
$l['timezone_gmt_1000'] = "(GMT +10:00) Melbourne, Papua New Guinea, Sydney, Yakutsk";
$l['timezone_gmt_1050'] = "(GMT +10:30) Lord Howe Island";
$l['timezone_gmt_1100'] = "(GMT +11:00) Magadan, New Caledonia, Solomon Islands, Vladivostok";
$l['timezone_gmt_1150'] = "(GMT +11:30) Norfolk Island";
$l['timezone_gmt_1200'] = "(GMT +12:00) Auckland, Wellington, Fiji, Marshall Islands";
$l['timezone_gmt_1275'] = "(GMT +12:45) Chatham Islands";
$l['timezone_gmt_1300'] = "(GMT +13:00) Samoa, Tonga, Tokelau";
$l['timezone_gmt_1400'] = "(GMT +14:00) Line Islands";
$l['timezone_gmt_short'] = "GMT {1}({2})";

$l['missing_task'] = "Error: Task file does not exist";
$l['task_backup_cannot_write_backup'] = "Error: The database backup task cannot write to backups directory.";
$l['task_backup_ran'] = "The database backup task successfully ran.";
$l['task_checktables_ran'] = "The check tables task successfully ran with no corrupted tables found.";
$l['task_checktables_ran_found'] = "Notice: The check tables task successfully ran and repaired the {1} table(s).";
$l['task_dailycleanup_ran'] = "The daily cleanup task successfully ran.";
$l['task_hourlycleanup_ran'] = "The hourly cleanup task successfully ran.";
$l['task_logcleanup_ran'] = "The log cleanup task successfully ran and pruned any old logs.";
$l['task_promotions_ran'] = "The promotions task successfully ran.";
$l['task_threadviews_ran'] = "The thread views task successfully ran.";
$l['task_usercleanup_ran'] = "The user cleanup task successfully ran.";
$l['task_massmail_ran'] = "The mass mail task successfully ran.";
$l['task_userpruning_ran'] = "The user pruning task successfully ran.";
$l['task_delayedmoderation_ran'] = "The delayed moderation task successfully ran.";
$l['task_massmail_ran_errors'] = "One or more problems occurred sending to \"{1}\":
{2}";
$l['task_versioncheck_ran'] = "The version check task successfully ran.";
$l['task_versioncheck_ran_errors'] = "Could not connect to MyBB for a version check.";
$l['task_recachestylesheets_ran'] = 'Re-cached {1} stylesheets.';
$l['task_sendmailqueue_ran'] = 'The send mail queue task sent up to {1} messages.';

$l['dismiss_notice'] = "Dismiss this notice";

$l['next'] = "Next";
$l['previous'] = "Previous";
$l['delete'] = "Delete";

$l['massmail_username'] = "Username";
$l['email_addr'] = "Email Address";
$l['board_name'] = "Board Name";
$l['board_url'] = "Board URL";

$l['comma'] = ", ";

$l['debug_generated_in'] = "Generated in {1}";
$l['debug_weight'] = "({1}% PHP / {2}% {3})";
$l['debug_sql_queries'] = "SQL Queries: {1}";
$l['debug_server_load'] = "Server Load: {1}";
$l['debug_memory_usage'] = "Memory Usage: {1}";
$l['debug_advanced_details'] = "Advanced Details";

$l['error_emailflooding_1_second'] = "Sorry, but you can only send one email every {1} minutes. Please wait another 1 second before attempting to email again.";
$l['error_emailflooding_seconds'] = "Sorry, but you can only send one email every {1} minutes. Please wait another {2} seconds before attempting to email again.";
$l['error_emailflooding_1_minute'] = "Sorry, but you can only send one email every {1} minutes. Please wait another 1 minute before attempting to email again.";
$l['error_emailflooding_minutes'] = "Sorry, but you can only send one email every {1} minutes. Please wait another {2} minutes before attempting to email again.";
$l['error_invalidfromemail'] = "You did not enter a valid from email address.";
$l['error_noname'] = "You did not enter a valid name.";
$l['your_email'] = "Your Email:";
$l['email_note'] = "Enter your email address here.";
$l['your_name'] = "Your Name:";
$l['name_note'] = "Enter your name here.";

$l['january'] = "January";
$l['february'] = "February";
$l['march'] = "March";
$l['april'] = "April";
$l['may'] = "May";
$l['june'] = "June";
$l['july'] = "July";
$l['august'] = "August";
$l['september'] = "September";
$l['october'] = "October";
$l['november'] = "November";
$l['december'] = "December";

$l['moderation_forum_attachments'] = "Please note that new attachments in this forum must be approved by a moderator before becoming visible.";
$l['moderation_forum_posts'] = "Please note that new posts in this forum must be approved by a moderator before becoming visible.";
$l['moderation_user_posts'] = "Please note that new posts you make must be approved by a moderator before becoming visible.";
$l['moderation_forum_thread'] = "Please note that new threads in this forum must be approved by a moderator before becoming visible.";
$l['moderation_forum_edits'] = "Please note that edited posts in this forum must be approved by a moderator before becoming visible.";
$l['moderation_forum_edits_quick'] = "Please note that edited posts in this forum must be approved by a moderator before becoming visible.";
$l['awaiting_message_link'] = " <a href=\"{1}/{2}/index.php?module=user-awaiting_activation\">Go to the ACP</a>.";
$l['awaiting_message_single'] = "There is 1 account awaiting activation. Please go to your ACP to activate the user.";
$l['awaiting_message_plural'] = "There are {1} accounts awaiting activation. Please go to your ACP to activate the users.";

$l['select2_match'] = "One result is available, press enter to select it.";
$l['select2_matches'] = "{1} results are available, use up and down arrow keys to navigate.";
$l['select2_nomatches'] = "No matches found";
$l['select2_inputtooshort_single'] = "Please enter one or more character";
$l['select2_inputtooshort_plural'] = "Please enter {1} or more characters";
$l['select2_inputtoolong_single'] = "Please delete one character";
$l['select2_inputtoolong_plural'] = "Please delete {1} characters";
$l['select2_selectiontoobig_single'] = "You can only select one item";
$l['select2_selectiontoobig_plural'] = "You can only select {1} items";
$l['select2_loadmore'] = "Loading more results&hellip;";
$l['select2_searching'] = "Searching&hellip;";

$l['stopforumspam_error_decoding'] = 'Error decoding data from StopForumSpam.com.';
$l['stopforumspam_error_retrieving'] = 'Error retrieving data from StopForumSpam.com.';
$l['stopforumspam_invalid_email'] = 'Invalid email address whilst checking against the StopForumSpam.com API.';
$l['stopforumspam_invalid_ip_address'] = 'Invalid IP address whilst checking against the StopForumSpam.com API.';

$l['sfs_error_username'] = 'username';
$l['sfs_error_ip'] = 'IP';
$l['sfs_error_email'] = 'email';
$l['sfs_error_or'] = 'or';

$l['expcol_collapse'] = '[-]';
$l['expcol_expand'] = '[+]';

$l['boardclosed_reason'] = 'These forums are currently closed for maintenance. Please check back later';

$l['use_default'] = "Use Default";
