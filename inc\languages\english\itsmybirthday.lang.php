<?php
/**
 * Its My Birthday! v1.0 English Language Pack
 * 
 * $Id: itsmybirthday.lang.php 1 2009-09-21 08:42:39Z - G33K - $
 */

$l['itsmybirthday_wishes_title'] = "<strong>The following users wish {1} a Happy Birthday:</strong>";
$l['itsmybirthday_wishes_title_nouser'] = "<strong>The following users also wish you all a Happy Birthday:</strong>";

$l['add_bday_wishes'] = "Add Birthday Wishes";
$l['del_bday_wishes'] = "Remove Birthday Wishes";

$l['imb_error_wish_disabled'] = "Adding Birthday Wishes Feature has been disabled";
$l['imb_error_invalid_action'] = "You are trying to perform an invalid action.";
$l['imb_error_not_bday_post'] = "Post specified is not a birthday post";
$l['imb_error_own_bday'] = "You can not add Birthday wishes to this post because this post is for YOUR Birthday.";
$l['imb_error_own_wish_delete'] = "This wish can not be deleted because you are not the user who added this Birthday wish. You can only delete Birthday wishes you added to posts.";
$l['imb_error_own_post'] = "You can not add Birthday wishes to this post because you are the owner of the post.";
$l['imb_error_already_wished'] = "You have already added your Birthday wishes to this post.";
$l['imb_error_unknown'] = "There was an unknown error and your Birthday wish was not added.";
$l['imb_error_removal_disabled'] = "You can not remove your Birthday Wishes because the removal of Birthday wishes has been disabled.";
$l['imb_error_wish_not_found'] = "The Birthday wish you specified can not be deleted because it can not be found. Its possible that it has already been deleted.";

$l['imb_redirect_wished'] = "Your Happy Birthday Wishes have been added to the post";
$l['imb_redirect_wish_deleted'] = "Your Happy Birthday Wishes have been removed from the post";
$l['imb_redirect_back'] = "<br />You will now be taken back to the post.";
?>