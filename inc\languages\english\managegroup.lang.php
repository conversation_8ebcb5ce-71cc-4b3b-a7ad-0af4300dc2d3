<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_group_management'] = "{1} Group Management";
$l['nav_join_requests'] = "Join Requests";
$l['nav_group_memberships'] = "Group Memberships";

$l['not_leader_of_this_group'] = "Sorry, but you are not a leader of this user group.";
$l['invalid_group'] = "This user group does not exist.";
$l['pending_requests'] = "Pending Join Requests";
$l['num_requests_pending'] = "There are currently <strong>{1}</strong> pending join requests for this user group.";
$l['group_management'] = "Group Management";
$l['members_of'] = "Members in \"{1}\"";
$l['user_name'] = "Username";
$l['contact'] = "Contact";
$l['reg_date'] = "Registered";
$l['post_count'] = "Posts";
$l['remove_selected'] = "Remove Selected Users from Group";
$l['add_member'] = "Add Member to \"{1}\"";
$l['add_member_submit'] = "Add Member to Group";
$l['invite_member'] = "Invite Member to \"{1}\"";
$l['invite_member_submit'] = "Invite Member to Group";
$l['join_requests'] = "Join Requests";
$l['join_requests_title'] = "Join Requests for \"{1}\"";
$l['leader'] = "(Leader)";
$l['reason'] = "Reason";
$l['accept'] = "Accept";
$l['ignore'] = "Ignore";
$l['decline'] = "Decline";
$l['action_requests'] = "Perform Actions";
$l['join_requests_moderated'] = "The join requests have been moderated.<br />You will now be taken to the request listing.";
$l['no_requests'] = "There are currently no pending join requests for this group.";
$l['no_users'] = "There are no users in this group.";
$l['user_added'] = "The user has been added to the user group.";
$l['users_removed'] = "The selected users have been removed from the user group.";
$l['group_no_members'] = "There are currently no members in this group.<br />To return to the group management page, click <a href=\"usercp.php?action=usergroups\">here</a>.";
$l['group_public_moderated'] = "This user group is a public user group that anyone can join.  All join requests must be moderated by a group leader.";
$l['group_public_not_moderated'] = "This user group is a public user group that anyone can join freely.";
$l['group_public_invite'] = "This user group is a public user group that requires an invitation from a group leader in order to join.";
$l['group_private'] = "This user group is a private user group.  Only users added by the group leader can be part of this group.";
$l['group_default'] = "This user group is a core user group.";
$l['group_leaders'] = "Group Leaders";
$l['no_users_selected'] = "Sorry, but no users seemed to be selected for removal.<br />Please go back and select the users you want to remove from this group.";

$l['error_alreadyingroup'] = "The user specified is already part of the user group.";
$l['error_alreadyinvited'] = "The user specified has already been invited.";

$l['user_invited'] = "The user has been invited to join the user group.";
$l['invite_pm_subject'] = "You have been invited to join \"{1}\"";
$l['invite_pm_message'] = "You have received an invitation to join the user group \"{1}\".

To join, please proceed to your [url={2}/usercp.php?action=usergroups]Group Memberships[/url] page and click 'Accept Invite'.

This invitation does not expire.";
$l['invite_pm_message_expires'] = "You have received an invitation to join the user group \"{1}\".

To join, please proceed to your [url={2}/usercp.php?action=usergroups]Group Memberships[/url] page and click 'Accept Invite'.

This invite will expire {3} days from now.";

