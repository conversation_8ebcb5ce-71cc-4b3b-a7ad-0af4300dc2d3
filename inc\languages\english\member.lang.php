<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_register'] = "Register";
$l['nav_activate'] = "Activate";
$l['nav_resendactivation'] = "Resend Activation Email";
$l['nav_lostpw'] = "Lost Password Recovery";
$l['nav_resetpassword'] = "Reset Password";
$l['nav_login'] = "Login";
$l['nav_emailuser'] = "Email User";
$l['nav_referrals'] = "Users Referred by {1}";
$l['nav_profile'] = "Profile of {1}";

$l['referrals'] = 'Referrals';
$l['referral_date'] = 'Referral Date:';
$l['referrals_no_user_specified'] = 'No user specified.';
$l['referrals_invalid_user'] = 'Invalid user specified.';
$l['member_no_referrals'] = 'No referrals for this user.';

$l['tpp_option'] = "Show {1} threads per page";
$l['ppp_option'] = "Show {1} posts per page";
$l['account_activation'] = "Account Activation";
$l['activate_account'] = "Activate Account";
$l['activation_code'] = "Activation Code:";

$l['email_user'] = "Send {1} an Email";
$l['email_subject'] = "Email Subject";
$l['email_message'] = "Email Message";
$l['send_email'] = "Send Email";
$l['error_hideemail'] = "The recipient has chosen to hide their email address and as a result you cannot email them.";
$l['error_no_email_subject'] = "You need to enter a subject for your email";
$l['error_no_email_message'] = "You need to enter a message for your email";

$l['login'] = "Login";
$l['pw_note'] = "Please note that passwords are case sensitive.";
$l['lostpw_note'] = "Lost your password?";
$l['lost_pw'] = "Lost Account Password";
$l['lost_pw_form'] = "Lost Password Recovery Form";
$l['email_address'] = "Email Address:";
$l['request_user_pass'] = "Request Login Credentials";
$l['profile'] = "Profile of {1}";
$l['registration_date'] = "Registration Date:";
$l['date_of_birth'] = "Date of Birth:";
$l['birthdayhidden'] = "Hidden";
$l['birthday'] = "Birthday:";
$l['local_time'] = "Local Time:";
$l['local_time_format'] = "{1} at {2}";
$l['users_forum_info'] = "{1}'s Forum Info";
$l['joined'] = "Joined:";
$l['lastvisit'] = "Last Visit:";
$l['total_posts'] = "Total Posts:";
$l['ppd_percent_total'] = "{1} posts per day | {2} percent of total posts";
$l['total_threads'] = "Total Threads:";
$l['tpd_percent_total'] = "{1} threads per day | {2} percent of total threads";
$l['find_posts'] = "Find All Posts";
$l['find_threads'] = "Find All Threads";
$l['members_referred'] = "Members Referred:";
$l['rating'] = "Rating:";
$l['users_contact_details'] = "{1}'s Contact Details";
$l['homepage'] = "Homepage:";
$l['pm'] = "Private Message:";
$l['send_pm'] = "Send {1} a private message.";
$l['skype_id'] = "Skype ID:";
$l['google_id'] = "Google Hangouts ID:";
$l['avatar']  = "Avatar:";
$l['warning_level'] = "Warning Level:";
$l['warn'] = "Warn";
$l['away_note'] = "{1} is currently away.";
$l['away_reason'] = "Reason:";
$l['away_since'] = "Away Since:";
$l['away_returns'] = "Returns on:";
$l['away_no_reason'] = "Not specified.";
$l['ban_note'] = "This forum account is currently banned.";
$l['ban_by'] = "Banned By";
$l['ban_length'] = "Ban Length";
$l['ban_remaining'] = "remaining";

$l['users_additional_info'] = "Additional Info About {1}";
$l['email'] = "Email:";
$l['send_user_email'] = "Send {1} an email.";
$l['users_signature'] = "{1}'s Signature";
$l['agreement'] = "Registration Agreement";
$l['agreement_1'] = "Whilst we attempt to edit or remove any messages containing inappropriate, sexually orientated, abusive, children abuse, children sexual abuse, hateful, slanderous, or threatening material that could be considered invasive of a person's privacy, or which otherwise violate any kind of law, it is impossible for us to review every message posted on this discussion system. For this reason you acknowledge that all messages posted on this discussion system express the views and opinions of the original message author and not necessarily the views of this bulletin board. Therefore we take no responsibility and cannot be held liable for any messages posted. We do not vouch for or warrant the accuracy and completeness of every message.";
$l['agreement_2'] = "By registering on this discussion system you agree that you will not post any material which is knowingly false, inaccurate, abusive, hateful, harassing, sexually orientated, threatening or invasive of a person's privacy, or any other material which may violate any applicable laws.";
$l['agreement_3'] = "Failure to comply with these rules may result in the termination of your account, account suspension, or permanent ban of access to these forums. Your IP Address is recorded with each post you make on this discussion system and is retrievable by the forum staff if need-be. You agree that we have the ability and right to remove, edit, or lock any account or message at any time should it be seen fit. You also agree that any information you enter on this discussion system is stored in a database, and that \"cookies\" are stored on your computer to save your login information.";
$l['agreement_4'] = "Any information you provide on these forums will not be disclosed to any third party without your complete consent, although the staff cannot be held liable for any hacking attempt in which your data is compromised.";
$l['agreement_5'] = "By continuing with the registration process you agree to the above rules and any others that the Administrator specifies.";
$l['registration'] = "Registration";
$l['required_fields'] = "Required Fields";
$l['complex_password'] = "<abbr title=\"A password that is at least {1} characters long and contains an upper case letter, a lower case letter and a number.\">Complex</abbr> Password:";
$l['confirm_email'] = "Confirm Email:";
$l['optional_fields'] = "Optional Fields";
$l['website_url'] = "Your Website URL:";
$l['birthdate'] = "Birthdate:";
$l['additional_info'] = "Additional Information";
$l['required_info'] = "Required Information";
$l['i_agree'] = "I Agree";
$l['account_details'] = "Account Details";
$l['account_prefs'] = "Account Preferences:";
$l['invisible_mode'] = "Hide me from the Who's Online list.";
$l['allow_notices'] = "Receive emails from the Administrators.";
$l['hide_email'] = "Hide your email from other members.";
$l['email_notify'] = "Automatically subscribe to threads you post in.";
$l['receive_pms'] = "Receive private messages from other users.";
$l['pm_notice'] = "Alert me with a notice when I receive a Private Message.";
$l['email_notify_newpm'] = "Notify me by email when I receive a new Private Message.";
$l['time_offset'] = "Time Zone (<abbr title=\"Daylight Saving Time\">DST</abbr> correction excluded):";
$l['time_offset_desc'] = "If you live in a time zone which differs to what this board is set at, you can select it from the list below.";
$l['dst_correction'] = "Daylight Saving Time correction:";
$l['dst_correction_auto'] = "Automatically detect DST settings";
$l['dst_correction_enabled'] = "Always use DST correction";
$l['dst_correction_disabled'] = "Never use DST correction";
$l['redirect_registered_coppa_activate'] = "Thank you for registering on {1}, {2}. Your account has successfully been created, however, as the owner of this account is under the age of 13, parental permission needs to be sought before this account can be used.<br /><br />A parent or legal guardian will need to download, fill in and submit to us a completed copy of our <a href=\"member.php?action=coppa_form\">COPPA Compliance &amp; Permission form</a>.<br /><br />Once we receive a completed copy of this form, the account will be activated.";
$l['coppa_compliance'] = "COPPA Compliance";
$l['coppa_desc'] = "In order to register on these forums, we require you to verify your age to comply with <a href=\"https://www.ftc.gov/enforcement/rules/rulemaking-regulatory-reform-proceedings/childrens-online-privacy-protection-rule\" title=\"Children's Online Privacy Protection Act\" target=\"_blank\" rel=\"noopener\">COPPA</a>. Please enter your date of birth below.<br /><br />If you are under the age of 13, parental permission must be obtained prior to registration. A parent or legal guardian will need to download, fill in and submit to us a completed copy of our <a href=\"member.php?action=coppa_form\" target=\"_blank\" rel=\"noopener\">COPPA Compliance &amp; Permission form</a>.";
$l['coppa_desc_for_deny'] = "In order to register on these forums, we require you to verify your age to comply with <a href=\"https://www.ftc.gov/enforcement/rules/rulemaking-regulatory-reform-proceedings/childrens-online-privacy-protection-rule\" title=\"Children's Online Privacy Protection Act\" target=\"_blank\" rel=\"noopener\">COPPA</a>. Please enter your date of birth below.";
$l['hide_dob'] = "You can choose to hide your date of birth and age by editing your profile after registering.";
$l['signature'] = "Signature:";
$l['continue_registration'] = "Continue with Registration";
$l['birthdayprivacy'] = "Date of Birth Privacy:";
$l['birthdayprivacyall'] = "Display Age and Date of Birth";
$l['birthdayprivacynone'] = "Hide Age and Date of Birth";
$l['birthdayprivacyage'] = "Display Only Age";
$l['leave_this_field_empty'] = "Leave this field empty:";
$l['error_need_to_be_thirteen'] = "You need to be of thirteen years or older to register on this forum.";
$l['coppa_registration'] = "COPPA Registration Form";
$l['coppa_form_instructions'] = "Please print this form, fill it in and either fax it to the number below or mail it to the provided mailing address.";
$l['fax_number'] = "Fax Number:";
$l['mailing_address'] = "Mailing Address:";
$l['account_information'] = "Account Information";
$l['parent_details'] = "Parent / Guardian Details";
$l['full_name'] = "Full Name:";
$l['relation'] = "Relation:";
$l['phone_no'] = "Phone #:";
$l['coppa_parent_agreement'] = "I understand that the information I have provided is truthful, that any information may be changed in the future by entering the supplied password and this user account can be removed by request.";

$l['coppa_agreement_1'] = "Users under the age of 13 must receive permission from their parent or legal guardian in order to register on {1}.";
$l['coppa_agreement_2'] = "A parent or legal guardian will need to download, fill in and submit to us a completed copy of our <a href=\"member.php?action=coppa_form\" target=\"_blank\" rel=\"noopener\">COPPA Compliance &amp; Permission form</a> before membership will be granted.";
$l['coppa_agreement_3'] = "If you'd like to, you can begin the registration process now, however the account will be inaccessible until the above compliance form is received.";

$l['error_invalid_birthday'] = 'The birthday you entered is invalid. Please enter a valid birthday.';
$l['error_awaitingcoppa'] = "You cannot login using this account as it is still awaiting COPPA validation from a parent or guardian.<br /><br />A parent or legal guardian will need to download, fill in and submit to us a completed copy of our <a href=\"member.php?action=coppa_form\">COPPA Compliance &amp; Permission form</a>.<br /><br />Once we receive a completed copy of this form, the account will be activated.";

$l['lang_select'] = "Language Settings:";
$l['lang_select_desc'] = "If you live in a country that speaks a language other than the forums default, you may be able to select an installed, read-able language pack below.";
$l['lang_select_default'] = "Use Default";

$l['submit_registration'] = "Submit Registration!";
$l['confirm_password'] = "Confirm Password:";
$l['referrer'] = "Referrer:";
$l['referrer_desc'] = "If you were referred by another member you can enter their username below. If not, simply leave this field blank.";
$l['resend_activation'] = "Resend Account Activation";
$l['request_activation'] = "Request Activation Code";
$l['ppp'] = "Posts Per Page:";
$l['ppp_desc'] = "Allows you to select the amount of posts to be shown per page in a thread.";
$l['tpp'] = "Threads Per Page:";
$l['tpp_desc'] = "Allows you to select the amount of threads to be shown per page in the thread listing.";
$l['reset_password'] = "Reset Password";
$l['send_password'] = "Send New Password!";
$l['registration_errors'] = "The following errors occurred with your registration:";
$l['timeonline'] = "Time Spent Online:";
$l['timeonline_hidden'] = "(Hidden)";
$l['registrations_disabled'] = "Sorry, but you cannot register at this time because the administrator has disabled new account registrations.";
$l['error_username_length'] = "Your username is invalid. Usernames have to be within {1} to {2} characters.";
$l['error_stop_forum_spam_spammer'] = 'Sorry, your {1} matches that of a known spammer. If you feel this is a mistake, please contact an administrator.';
$l['error_stop_forum_spam_fetching'] = 'Sorry, something went wrong verifying your account against a spammer database. Most likely the database couldn\'t be accessed. Please try again later.';

$l['none_registered'] = "None Registered";
$l['not_specified'] = "Not Specified";
$l['membdayage'] = "({1} years old)";
$l['mod_options'] = "Moderator Options";
$l['edit_in_mcp'] = "Edit this user in Mod CP";
$l['ban_in_mcp'] = "Ban this user in Mod CP";
$l['edit_ban_in_mcp'] = "Edit ban in Mod CP";
$l['lift_ban_in_mcp'] = "Lift ban in Mod CP";
$l['purgespammer'] = "Purge Spammer";
$l['edit_usernotes'] = "Edit user notes in Mod CP";
$l['no_usernotes'] = "There are currently no notes on this user";
$l['view_all_notes'] = "View all notes";
$l['view_notes_for'] = "View notes for {1}";
$l['registration_ip'] = "Registration IP:";
$l['last_known_ip'] = "Last Known IP:";
$l['reputation'] = "Reputation:";
$l['reputation_vote'] = "Rate";
$l['reputation_details'] = "Details";
$l['already_logged_in'] = "Notice: You are already currently logged in as {1}.";
$l['admin_edit_in_acp'] = "Edit this user in Admin CP";
$l['admin_ban_in_acp'] = "Ban this user in Admin CP";
$l['admin_lift_ban_in_acp'] = "Lift ban in Admin CP";
$l['admin_edit_ban_in_acp'] = "Edit ban in Admin CP";
$l['admin_options'] = "Administrator Options";

$l['redirect_registered_activation'] = "Thank you for registering on {1}, {2}.<p>To complete your registration, please check your email for the account activation instructions. You may not be able to post until your account has been activated.";
$l['redirect_emailupdated'] = "Your email has been successfully changed.<br />You will now be taken back to the forums index.";
$l['redirect_accountactivated'] = "Your account has successfully been activated.<br />You will now be taken back to the forums index.";
$l['redirect_accountactivated_admin'] = "Your email has successfully validated.<br />Your registration must be approved by an administrator. Until then, you may not be able to post on these forums.<br />You will now be taken back to the forum index.";
$l['redirect_registered'] = "Thank you for registering on {1}, {2}.<br />You will now be taken back to the forums index.";
$l['redirect_registered_admin_activate'] = "Thank you for registering on {1}, {2}.<br />Your registration must be approved by an administrator. Until then, you may not be able to post on these forums.";
$l['redirect_loggedout'] = "You have successfully been logged out.<br />You will now be taken back to the forum index.";
$l['redirect_alreadyloggedout'] = "You were already logged out or have not logged in yet.<br />You will now be taken back to the forum index.";
$l['redirect_lostpwsent'] = "Thank you, all accounts pertaining to that email address have now been sent an email with details on how to reset their passwords.<br /><br />You will now be taken to the forum index.";
$l['redirect_activationresent'] = "Your activation email has been resent to the email address provided.";
$l['redirect_passwordreset'] = "Thank you, the password for your account has been reset. The new randomly generated password has been emailed to the email address associated with your account.";
$l['redirect_memberrated'] = "The member has been rated successfully.";
$l['redirect_registered_passwordsent'] = "A random password has been generated and sent to your email address. Before you can login, you will need to check your email for this password.";
$l['redirect_validated'] = "Thank you, your account has been validated.<br />You will now be taken to the forum index.";

$l['error_activated_by_admin'] = "You are unable to resend your account activation email as all registrations must be approved by an Administrator.";
$l['error_alreadyregistered'] = "Sorry, but our system shows that you have already registered and the registration of multiple accounts has been disabled.";
$l['error_alreadyregisteredtime'] = "We cannot process your registration because there has already been {1} new registration(s) from your ip address in the past {2} hours. Please try again later.";
$l['error_badlostpwcode'] = "You have entered an invalid password reset code. Please re-read the email you were sent or contact the forum administrators for more help.";
$l['error_badactivationcode'] = "You have entered an invalid account activation code. To resend all activation emails to the email address on file, please click <a href=\"member.php?action=resendactivation\">here</a>.";
$l['error_alreadyactivated'] = "Your account has already been activated or does not require email validation.";
$l['error_alreadyvalidated'] = "Your email has already been validated.";
$l['error_nothreadurl'] = "Your message does not contain the URL of the thread. Please use the \"send to friend\" feature for it's intended purpose.";
$l['error_bannedusername'] = "You have entered a username that is banned from registration.  Please choose another username.";
$l['error_notloggedout'] = "Your user ID could not be verified to proceed with the log out process. This could be due to malicious Javascript that was attempting to log you out automatically.  If you intended to log out, please click the Log Out button at the top menu.";
$l['error_regimageinvalid'] = "The image verification code that you entered was incorrect. Please enter the code exactly how it appears in the image.";
$l['error_regimagerequired'] = "Please fill out the image verification code to continue the login process. Please enter the code exactly how it appears in the image.";
$l['error_spam_deny'] = "Our systems detect that you may be a spammer and therefore you have been denied registration. If this is a mistake, please contact the Administrator.";
$l['error_spam_deny_time'] = "Our systems detect that you may be a spammer and therefore you have been denied registration. Registration must take a minimum time of {1} seconds to prevent automated signups, you registered in {2} seconds. If this is a mistake, please contact the Administrator.";

$l['js_validator_no_username'] = "You need to enter a username.";
$l['js_validator_invalid_email'] = "You need to enter a valid email address.";
$l['js_validator_email_match'] = "You need to enter the same email address again.";
$l['js_validator_no_image_text'] = "You need to enter the text in the image above.";
$l['js_validator_no_security_question'] = "You need to enter the answer to the security question.";
$l['js_validator_password_matches'] = "The passwords you enter must match.";
$l['js_validator_password_length'] = "Your password must be {1} or more characters long.";
$l['js_validator_bad_password_security'] = "The password you entered is similar to either your username or email address. Please enter a stronger password.";
$l['js_validator_not_empty'] = "You must select or enter a value for this field.";
$l['js_validator_username_length'] = "Usernames must be between {1} and {2} characters long.";

$l['security_question'] = "Security Question";
$l['question_note'] = "Please answer the question provided. This process is used to prevent automated processes.";
$l['error_question_wrong'] = "The answer you provided for the security question is wrong. Please try again.";

$l['subscription_method'] = "Default Thread Subscription Mode:";
$l['no_auto_subscribe'] = "Do not subscribe";
$l['no_subscribe'] = "No notification";
$l['instant_email_subscribe'] = "Instant email notification";
$l['instant_pm_subscribe'] = "Instant PM notification";

$l['remove_from_buddy_list'] = "Remove from Buddy List";
$l['add_to_buddy_list'] = "Add to Buddy List";
$l['remove_from_ignore_list'] = "Remove from Ignore List";
$l['add_to_ignore_list'] = "Add to Ignore List";
$l['report_user'] = "Report User";

$l['newregistration_subject'] = "New registration at {1}";
$l['newregistration_message'] = "{1},

There is a new user at {2} who is pending admin activation.

Username: {3}

Thank you,
{2} Staff";
