<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_helpdocs'] = "Help Documents";
$l['nav_smilies'] = "Smilie Listing";
$l['nav_syndication'] = "Latest Thread Syndication (RSS)";

$l['buddy_list'] = "Buddy List";
$l['online'] = "Online";
$l['online_none'] = "<em>You have no online buddies</em>";
$l['offline'] = "Offline";
$l['offline_none'] = "<em>You have no offline buddies</em>";
$l['delete_buddy'] = "X";
$l['pm_buddy'] = "Send Private Message";
$l['last_active'] = "<strong>Last Active:</strong> {1}";
$l['close'] = "Close";
$l['no_buddies'] = "<em>Your buddy list is currently empty. Use your User CP or visit a user profile to add users to your buddy list.</em>";

$l['help_docs'] = "Help Documents";

$l['search_help_documents'] = "Search Help Documents";
$l['search_by_name'] = "Search by Name";
$l['search_by_document'] = "Search by Document";
$l['enter_keywords'] = "Enter Keywords";
$l['search'] = "Search";
$l['redirect_searchresults'] = "Thank you, your search has been submitted and you will now be taken to the results list.";
$l['search_results'] = "Search Results";
$l['help_doc_results'] = "Help Document Results";
$l['document'] = "Document";
$l['error_nosearchresults'] = "Sorry, but no results were returned using the query information you provided. Please redefine your search terms and try again.";
$l['no_help_results'] = "Sorry, but no results were returned using the query information you provided.";
$l['error_helpsearchdisabled'] = "The ability to search the help documents has been disabled by the Administrator.";

$l['smilies_listing'] = "Smilies Listing";
$l['name'] = "Name";
$l['abbreviation'] = "Abbreviation";
$l['click_to_add'] = "Click a smilie to insert it into your message";
$l['close_window'] = "close window";
$l['no_smilies'] = "There are currently no smilies available.";

$l['who_posted'] = "Who Posted?";
$l['total_posts'] = "Total Posts:";
$l['user'] = "User";
$l['num_posts'] = "# Posts";

$l['forum_rules'] = "{1} - Rules";

$l['error_invalid_limit'] = "The feed item limit you entered is invalid. Please specify a valid limit.";

$l['syndication'] = "Latest Thread Syndication";
$l['syndication_generated_url'] = "Your Generated Syndication URL:";
$l['syndication_note'] = "Below you can generate links to specific RSS syndication feeds. Links can be generated for all forums, per forum, or for a specific number of forums. You will then be presented with a link which you can copy in to an RSS reader. <i><a href=\"https://en.wikipedia.org/wiki/RSS\" target=\"_blank\" rel=\"noopener\">What is RSS?</a></i>";
$l['syndication_forum'] = "Forum to Syndicate:";
$l['syndication_forum_desc'] = "Please select a forum from the right. Use the CTRL key to select multiple forums.";
$l['syndication_version'] = "Feed Version:";
$l['syndication_version_desc'] = "Please select the version of the feeds you wish to generate.";
$l['syndication_version_json1'] = "JSON Feed 1";
$l['syndication_version_atom1'] = "Atom 1.0";
$l['syndication_version_rss2'] = "RSS 2.00 (Default)";
$l['syndication_generate'] = "Generate Syndication URL";
$l['syndication_limit'] = "Limit:";
$l['syndication_limit_desc'] = "The amount of threads to download at one time. 50 at once is maximum limit.";
$l['syndication_threads_time'] = "threads at a time";
$l['syndicate_all_forums'] = "Syndicate All Forums";

$l['redirect_markforumread'] = "The selected forum has been marked as read.";
$l['redirect_markforumsread'] = "All the forums have been marked as read.";
$l['redirect_forumpasscleared'] = "The stored password for this forum has been cleared.";
$l['redirect_cookiescleared'] = "All cookies have been cleared.";

$l['error_invalidforum'] = "Invalid forum";
$l['error_invalidhelpdoc'] = "The specified help document does not appear to exist.";
$l['error_invalidsearch'] = "An invalid search was specified.  Please go back and try again.";
$l['error_no_search_support'] = "This database engine does not support searching.";
$l['error_searchflooding'] = "Sorry, but you can only perform one search every {1} seconds. Please wait another {2} seconds before attempting to search again.";
$l['error_searchflooding_1'] = "Sorry, but you can only perform one search every {1} seconds. Please wait another 1 second before attempting to search again.";

$l['dst_settings_updated'] = "Your daylight saving time settings have automatically been adjusted.<br /><br />You will now be taken back to the forum index.";
