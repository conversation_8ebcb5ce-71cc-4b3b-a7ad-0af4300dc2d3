<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_width'] = "180";
$l['nav_modcp'] = "Mod CP";
$l['nav_menu'] = "Menu";

$l['mcp_nav_home'] = "Mod CP Home";
$l['mcp_nav_forums'] = "Forums &amp; Posts";
$l['mcp_nav_announcements'] = "Announcements";
$l['mcp_nav_modqueue'] = "Moderation Queue";
$l['mcp_nav_report_center'] = "Report Center";
$l['mcp_nav_all_reports'] = "All Reports";
$l['mcp_nav_modlogs'] = "Moderator Logs";
$l['mcp_nav_users'] = "Users";
$l['mcp_nav_editprofile'] = "Profile Editor";
$l['mcp_nav_banning'] = "Banning";
$l['mcp_nav_warninglogs'] = "Warning Logs";
$l['mcp_nav_ipsearch'] = "IP Search";
$l['mcp_nav_editing_ban'] = "Edit a Ban";
$l['mcp_nav_ban_user'] = "Ban a User";
$l['mcp_nav_modqueue_threads'] = "Thread Moderation Queue";
$l['mcp_nav_modqueue_posts'] = "Post Moderation Queue";
$l['mcp_nav_modqueue_attachments'] = "Attachment Moderation Queue";

$l['modcp'] = "Moderator Control Panel";

$l['view_all_reports'] = "View All Reports";
$l['all_reports'] = "All Reports";
$l['report_center'] = "Report Center";
$l['post_id'] = "Post ID";
$l['poster'] = "Poster";
$l['thread'] = "Thread";
$l['reporter'] = "Reporter";
$l['report_reason'] = "Reason";
$l['report_time'] = "Reported";
$l['mark_read'] = "Mark Selected as Read";
$l['no_reports'] = "There are currently no unread reports.";
$l['no_logs'] = "No moderator actions are currently logged.";
$l['error_noselected_reports'] = "Sorry, but you did not select any reported content to mark as read.";
$l['error_missing_ipaddress'] = "Sorry, but you did not enter in an IP Address to find.";
$l['error_no_results'] = "Sorry, there were no results found with the criteria you selected.";
$l['redirect_reportsmarked'] = "The selected reported content have been marked as read.";
$l['redirect_allreportsmarked'] = "All reported content has been marked as read.";
$l['redirect_modnotes'] = "The moderator notes have been updated.";

$l['for'] = "For";
$l['report_info'] = "Reported Content";
$l['report_type'] = "Report Reason";
$l['report_count'] = "# of Reports";
$l['report_lastpost'] = "Last Reported";

$l['report_reason_other'] = "Other Reason";

$l['report_info_post'] = "A <a href=\"{1}\">Post</a> by {2}";
$l['report_info_post_thread'] = "<br /><span class=\"smalltext\">In <a href=\"{1}\">{2}</a></span>";
$l['report_info_profile'] = "Profile of {1}";
$l['report_info_reputation'] = "<a href=\"{1}\">Reputation</a> from {2}";
$l['report_info_rep_profile'] = "<br /><span class=\"smalltext\">On {1}'s profile</span>";
$l['report_info_lastreporter'] = "{1}<br />by {2}";

$l['page_selected'] = "All <strong>{1}</strong> unread reports on this page are selected.";
$l['all_selected'] = "All <strong>{1}</strong> unread reports are selected.";
$l['select_all'] = "Select all <strong>{1}</strong> unread reports.";
$l['clear_selection'] = "Clear Selection.";

$l['moderator_notes'] = "Moderator Notes";
$l['notes_public_all'] = "These notes are public to all moderators.";
$l['save_notes'] = "Save Notes";
$l['bans_ending_soon'] = "Bans Ending Soon";
$l['latest_5_modactions'] = "5 Latest Moderator Actions";
$l['awaiting_moderation'] = "Awaiting Moderation";
$l['type'] = "Type";
$l['number_awaiting'] = "Number Awaiting";
$l['latest'] = "Latest";
$l['ipsearch'] = "IP Search";
$l['ipsearch_results'] = "IP Search Results for '{1}'";
$l['ipaddress_search'] = "IP Address Search";
$l['ipaddress_misc_info'] = "Misc. Information for '{1}'";
$l['ipaddress_host_name'] = "Host Name:";
$l['ipaddress_location'] = "GeoIP Location:";
$l['search_users'] = "Search Users";
$l['search_posts'] = "Search Posts";
$l['ip_address'] = "IP Address:";
$l['result'] = "Result";
$l['ipresult_regip'] = "Registration IP:";
$l['ipresult_lastip'] = "Last Known IP:";
$l['ipresult_post'] = "Post:";
$l['subject'] = "Subject";
$l['username'] = "Username";
$l['ipaddress'] = "IP Address";
$l['options'] = "Options:";
$l['find'] = "Find";
$l['modlogs'] = "Moderator Logs";
$l['action'] = "Action";
$l['all_moderators'] = "All Moderators";
$l['ip'] = "IP Address";
$l['info_on_ip'] = "Information on this IP Address";
$l['search_ip_sfs']  = "Search this IP on Stop Forum Spam";
$l['information']  = "Information";
$l['filter_modlogs'] = "Filter Moderator Logs";
$l['forum'] = "Forum";
$l['post'] = "Post";
$l['from_moderator'] = "From Moderator:";
$l['na_deleted'] = "N/A - Been Deleted";
$l['sort_by'] = "Sort by:";
$l['forum_name'] = "Forum Name";
$l['thread_subject'] = "Thread Subject";
$l['in'] = "in";
$l['order'] = "order";
$l['asc'] = "Ascending";
$l['desc'] = "Descending";
$l['per_page'] = "Results Per Page:";
$l['filter_logs'] = "Filter Logs";
$l['error_no_log_results'] = "Sorry, there were no results found with the criteria you selected.";
$l['find_users'] = "Search for Users";
$l['users'] = "Users";
$l['regdate'] = "Registration Date";
$l['lastvisit'] = "Last Visit";
$l['postnum'] = "Post Count";
$l['username_contains'] = "Username contains:";
$l['no_user_results'] = "No users were found with the specified search criteria.";
$l['edit_profile'] = "Edit Profile of {1}";
$l['birthday'] = "Date of Birth:";
$l['title'] = "User Title:";
$l['profile_required'] = "Required Fields";
$l['remove_avatar'] = "Remove user's avatar?";
$l['profile_optional'] = "Optional Fields";
$l['website_url'] = "Website URL:";
$l['birthdate'] = "Birthdate:";
$l['skype_id'] = "Skype ID:";
$l['google_id'] = "Google Hangouts ID:";
$l['away_notice_away'] = "You have been marked away since {1}";
$l['away_notice'] = "This option will allow you to select whether you are away or not.";
$l['additional_information'] = "Additional Information";
$l['update_profile'] = "Update Profile";
$l['custom_usertitle'] = "Custom User Title";
$l['new_custom_usertitle'] = "New Custom User Title: (leave blank to use existing)";
$l['custom_usertitle_note'] = "Here you can assign a custom user title which will overwrite the one based on users display group.";
$l['default_usertitle'] = "Default User Title:";
$l['current_custom_usertitle'] = "Current Custom User Title:";
$l['revert_usertitle'] = "Revert to group default";
$l['additional_contact_details'] = "Additional Contact Details";
$l['current_username'] = "Username:";
$l['away_information'] = "Away Information";
$l['away_status'] = "Away Status:";
$l['away_status_desc'] = "Allows you to leave an away message if you are going away for a while.";
$l['im_away'] = "I'm Away";
$l['im_here'] = "I'm Here";
$l['away_reason'] = "Away Reason:";
$l['away_reason_desc'] = "Allows you to enter a small description of why you are away  (max 200 characters).";
$l['return_date'] = "Return Date:";
$l['return_date_desc'] = "If you know when you will be back, you can enter your return date here.";
$l['error_modcp_return_date_past'] = "You cannot return in the past!";
$l['usergroup'] = "Primary Group";
$l['redirect_user_updated'] = "The users profile has successfully been updated.";
$l['posts_awaiting_moderation'] = "Posts Awaiting Moderation";
$l['threads_awaiting_moderation'] = "Threads Awaiting Moderation";
$l['attachments_awaiting_moderation'] = "Attachments Awaiting Moderation";
$l['mod_queue'] = "Moderation Queue";
$l['approve'] = "Approve";
$l['ignore'] = "Ignore";
$l['perform_actions'] = "Perform Actions";
$l['author'] = "Author";
$l['threads'] = "Threads";
$l['posts'] = "Posts";
$l['filename'] = "Filename";
$l['thread_post'] = "Thread / Post";
$l['permanent'] = "Permanent";
$l['ban_error'] = "Error";
$l['ban_banned'] = "Banned Users";
$l['ban_user'] = "Ban a User";
$l['reason'] = "Reason";
$l['ban_username'] = "Username:";
$l['ban_reason'] = "Reason:";
$l['ban_length'] = "Length";
$l['ban_remaining'] = "remaining";
$l['ban_ending_imminently'] = "Ban Ending Imminently";
$l['ban_bannedby'] = "Banned By";
$l['ban_movegroup'] = "Move to Banned Group:";
$l['ban_liftafter'] = "Lift Ban After:";
$l['no_banned'] = "There are currently no banned users.";
$l['no_banned_group'] = "There are currently no banned groups.";
$l['redirect_banuser'] = "The user has successfully been banned.";
$l['redirect_banuser_updated'] = "The user's ban has successfully been updated.";
$l['invalid_username'] = "The username you entered was invalid. Please ensure you enter a valid username.";
$l['error_useralreadybanned'] = "This user is already banned. You cannot ban a user more than once.";
$l['error_cannotbanuser'] = "You cannot ban this user because they have higher permissions than you. Please contact your administrator if you wish to ban this user.";
$l['error_cannotbanself'] = "You cannot ban yourself. Please enter another username.";
$l['error_no_perm_to_ban'] = "You do not have permission to ban this user.";
$l['error_nobanreason'] = "You did not enter a reason for this ban. Please enter a valid reason below.";
$l['error_nobangroup'] = "You did not select a valid group to move this user to.";
$l['edit_ban'] = "Edit Ban";
$l['lift_ban'] = "Lift Ban";
$l['ban'] = "Ban";
$l['error_invalidban'] = "You have selected an invalid ban.";
$l['redirect_banlifted'] = "The ban has successfully been lifted.";
$l['mark_all_ignored'] = "Mark all as ignored";
$l['mark_all_deletion'] = "Mark all for deletion";
$l['mark_all_approved'] = "Mark all as approved";
$l['meta_forum'] = "Forum:";
$l['meta_thread'] = "Thread:";
$l['mod_queue_empty'] = "All of the moderation queues are currently empty.";
$l['mod_queue_threads_empty'] = "The thread moderation queue is currently empty.";
$l['mod_queue_posts_empty'] = "The posts moderation queue is currently empty.";
$l['mod_queue_attachments_empty'] = "The attachment moderation queue is currently empty.";
$l['redirect_threadsmoderated'] = "The selected threads have been moderated.";
$l['redirect_postsmoderated'] = "The selected posts have been moderated.";
$l['redirect_attachmentsmoderated'] = "The selected attachments have been moderated.";
$l['multi_approve_posts'] = "Selected Posts Approved";
$l['multi_delete_posts'] = "Selected Posts Deleted Permanently";
$l['multi_soft_delete_posts'] = "Selected Posts Soft Deleted";
$l['multi_approve_threads'] = "Selected Threads Approved";
$l['multi_delete_threads'] = "Selected Threads Deleted Permanently";
$l['multi_soft_delete_threads'] = "Selected Threads Soft Deleted";
$l['edited_user'] = "Edited User's Profile";
$l['edited_user_info'] = "<strong>User:</strong> <a href=\"{2}\">{1}</a>";
$l['edited_user_ban'] = "Edited User Ban";
$l['banned_user'] = "Banned User";
$l['lifted_ban'] = "Lifted User Ban";
$l['no_bans_ending'] = "There are no bans ending soon.";

$l['warning_logs'] = "Warning Logs";
$l['warned_user'] = "Warned User";
$l['warning'] = "Warning";
$l['date_issued'] = "Date Issued";
$l['expires'] = "Expires";
$l['expiry_date'] = "Expiry Date";
$l['issued_date'] = "Issued Date";
$l['issued_by'] = "Issued By";
$l['details'] = "Details";
$l['filter_warning_logs'] = "Filter Warning Logs";
$l['filter_warned_user'] = "Warned user:";
$l['filter_issued_by'] = "Warning issued by:";
$l['filter_reason'] = "Reason contains:";
$l['view'] = "View";
$l['no_warning_logs'] = "There are no warning logs to view.";
$l['revoked'] = "Revoked ";
$l['signature'] = "Signature";
$l['suspend_signature'] = "<strong>Suspend this user's signature</strong>";
$l['suspend_length'] = "Suspension length:";
$l['mod_notes'] = "Moderator Notes";
$l['moderation'] = "Moderator Options";
$l['moderate_posts'] = "Moderate this user's posts";
$l['suspend_posts'] = "Suspend this user's posting privileges";
$l['modpost_length'] = "Moderate for:";
$l['suspost_length'] = "Suspend for:";

$l['moderateposts_for'] = "Moderated until {1}.<br />Untick this option to remove, or extend below.";
$l['suspendposting_for'] = "Suspended until {1}.<br />Untick this option to remove, or extend below.";
$l['suspendsignature_for'] = "Suspended until {1}.<br />Untick this option to remove, or extend below.";
$l['suspendposting_perm'] = "Suspended permanently.<br />Untick this option to remove, or change below.";
$l['moderateposts_perm'] = "Moderated permanently.<br />Untick this option to remove, or change below.";
$l['suspendsignature_perm'] = "Suspended permanently.<br />Untick this option to remove, or change below.";
$l['suspendsignature_error'] = "You selected to suspend this user's signature, but didn't enter a valid time period. Please enter a valid time to continue or untick the option to cancel.";
$l['moderateposting_error'] = "You selected to moderate this user's posts, but didn't enter a valid time period. Please enter a valid time to continue or untick the option to cancel.";
$l['suspendposting_error'] = "You selected to suspend this user's posts, but didn't enter a valid time period. Please enter a valid time to continue or untick the option to cancel.";
$l['suspendmoderate_error'] = "You've selected to suspend and moderate the user's posts. Please select only one type of moderation.";

$l['expire_hours'] = "hour(s)";
$l['expire_days'] = "day(s)";
$l['expire_weeks'] = "week(s)";
$l['expire_months'] = "month(s)";
$l['expire_permanent'] = "Permanent";

$l['manage_announcement'] = "Manage Announcements";
$l['forum_announcements'] = "Forum Announcements";
$l['announcement'] = "Announcement";
$l['controls'] = "Controls";
$l['expired_announcement'] = "Expired Announcement";
$l['active_announcement'] = "Active Announcement";
$l['active'] = "Active";
$l['expired'] = "Expired";
$l['edit'] = "Edit";
$l['add_announcement'] = "Add Announcement";
$l['edit_announcement'] = "Edit Announcement";
$l['no_forum_announcements'] = "There are currently no forum announcements on your board.";
$l['no_global_announcements'] = "There are currently no global announcements on your board.";
$l['add_global_announcement'] = "Add Global Announcement";
$l['global_announcements'] = "Global Announcements";
$l['title'] = "Title";
$l['start_date'] = "Start Date";
$l['time'] = "Time:";
$l['end_date'] = "End Date";
$l['never'] = "Never";
$l['allow_html'] = "Allow HTML";
$l['allow_mycode'] = "Allow MyCode";
$l['allow_smilies'] = "Allow Smilies";
$l['reset'] = "Reset";
$l['january'] = "January";
$l['february'] = "February";
$l['march'] = "March";
$l['april'] = "April";
$l['may'] = "May";
$l['june'] = "June";
$l['july'] = "July";
$l['august'] = "August";
$l['september'] = "September";
$l['october'] = "October";
$l['november'] = "November";
$l['december'] = "December";
$l['delete_announcement'] = "Delete Announcement";
$l['confirm_delete_announcement'] = "Are you sure you want to delete this announcement?";
$l['redirect_add_announcement'] = "The announcement has been created.";
$l['redirect_edit_announcement'] = "The announcement has been edited.";
$l['redirect_delete_announcement'] = "The announcement has been deleted.";
$l['error_missing_title'] = "You did not enter a title.";
$l['error_missing_message'] = "You did not enter a message.";
$l['error_missing_forum'] = "You did not select a forum.";
$l['error_invalid_start_date'] = "The starting date for the announcement is invalid.";
$l['error_invalid_end_date'] = "The ending date for the announcement is invalid.";
$l['error_end_before_start'] = "The ending date must be after the start date.";
$l['error_invalid_announcement'] = "The specified announcement is invalid.";

$l['announcement_added'] = "Announcement Added";
$l['announcement_edited'] = "Announcement Edited";
$l['announcement_deleted'] = "Announcement Deleted";

$l['preview'] = 'Preview';

$l['you_cannot_view_mod_logs'] = "You do not have sufficient permission to view the Moderator Logs.";
$l['you_cannot_view_reported_posts'] = "You do not have sufficient permission to view Reported Posts.";
$l['you_cannot_manage_announcements'] = "You do not have sufficient permission to manage Announcements.";
$l['you_cannot_moderate_threads'] = "You do not have sufficient permission to moderate threads.";
$l['you_cannot_moderate_posts'] = "You do not have sufficient permission to moderate posts.";
$l['you_cannot_moderate_attachments'] = "You do not have sufficient permission to moderate attachments.";
$l['you_cannot_use_mod_queue'] = "You do not have sufficient permission to use the Mod Queue.";

$l['post'] = 'Post';
