<?php
$l['myalerts'] = 'MyAlerts';

$l['myalerts_page_title'] = 'Recent Alerts';
$l['myalerts_page_delete_read'] = 'Delete Read Alerts';
$l['myalerts_page_delete_all'] = 'Delete All Alerts';
$l['myalerts_page_mark_all_read'] = 'Mark All Read';
$l['myalerts_page_getnew'] = 'Check for New Alerts';
$l['myalerts_page_row_read'] = '&#10003;';
$l['myalerts_page_row_unread'] = '&orarr;';
$l['myalerts_page_row_delete'] = '&#10005;';
$l['myalerts_page_row_read_title'] = 'Mark as Read';
$l['myalerts_page_row_unread_title'] = 'Mark as Unread';
$l['myalerts_page_row_delete_title'] = 'Delete Alert';
$l['myalerts_page_more'] = 'Alerts overflow onto the next page. Click here to go there with visible pagination.';

$l['myalerts_modal_title'] = 'Recent Alerts';
$l['myalerts_modal_display_alerts'] = 'View Alerts';
$l['myalerts_modal_unread_only'] = 'Show Unread Only';
$l['myalerts_modal_delete_read'] = 'Delete Read Alerts';
$l['myalerts_modal_delete_all'] = 'Delete All Alerts';
$l['myalerts_modal_delete_read_confirm'] = 'Really delete all read alerts? This cannot be undone!';
$l['myalerts_modal_delete_all_confirm'] = 'Really delete all alerts? This cannot be undone!';
$l['myalerts_modal_row_read'] = '&#10003;';
$l['myalerts_modal_row_unread'] = '&orarr;';
$l['myalerts_modal_row_delete'] = '&#10005;';
$l['myalerts_modal_mark_all_read'] = 'Mark All Read';
$l['myalerts_modal_mark_all_read_confirm'] = 'Really mark all alerts read? This cannot be undone!';
$l['myalerts_modal_row_read_title'] = 'Mark as Read';
$l['myalerts_modal_row_unread_title'] = 'Mark as Unread';
$l['myalerts_modal_row_delete_title'] = 'Delete Alert';

$l['myalerts_marked_all_read_title'] = 'Marked All Read';
$l['myalerts_marked_all_read_desc'] = 'All alerts were successfully marked as read.';

$l['myalerts_alerts'] = 'Alerts';

$l['myalerts_settings_page_title'] = 'Alert Settings';

$l['myalerts_online_location_listing'] = 'Viewing Alerts';

$l['myalerts_unread_title'] = 'Unread Alerts';

$l['myalerts_no_alerts'] = 'No alerts to display';
$l['myalerts_rep'] = '{1} modified your reputation.';
$l['myalerts_pm'] = '{1} sent you a new private message titled<br><strong>"{2}"</strong>';
$l['myalerts_buddylist'] = '{1} added you to their buddy list.';
$l['myalerts_quoted'] = '{1} quoted you in<br><strong>"{2}"</strong>';
$l['myalerts_post_threadauthor'] = '{1} replied to your thread<br><strong>"{2}"</strong> (<i>There may be more posts after this.</i>)';
$l['myalerts_subscribed_thread'] = '{1} replied to your subscribed thread<br><strong>"{2}"</strong>';
$l['myalerts_rated_threadauthor'] = '{1} rated your thread<br><strong>"{2}"</strong>';
$l['myalerts_voted_threadauthor'] = '{1} voted in your poll in<br><strong>"{2}"</strong>';

$l['myalerts_setting_rep'] = 'Receive alert for reputation?';
$l['myalerts_setting_pm'] = 'Receive alert for Private Message (PM)?';
$l['myalerts_setting_buddylist'] = 'Receive alert when added to buddylist?';
$l['myalerts_setting_quoted'] = 'Receive alert when quoted in a post?';
$l['myalerts_setting_post_threadauthor'] = 'Receive alert when somebody replies to your thread?';
$l['myalerts_setting_subscribed_thread'] = 'Receive alert when somebody replies to one of your subscribed threads?';
$l['myalerts_setting_rated_threadauthor'] = 'Receive alert when somebody rates your thread?';
$l['myalerts_setting_voted_threadauthor'] = 'Receive alert when somebody votes in your thread\'s poll?';
$l['myalerts_settings_save'] = 'Save Settings';
$l['myalerts_settings_updated'] = 'Alert settings updated successfully. Redirecting back to settings page.';
$l['myalerts_settings_updated_title'] = 'Alert Settings Updated.';

$l['myalerts_delete_deleted'] = 'Alert deleted successfully.';
$l['myalerts_delete_error'] = 'Alert cannot be deleted at this time.';
$l['myalerts_delete_read_confirm'] = 'Really delete all read alerts? This cannot be undone!';
$l['myalerts_delete_all_confirm'] = 'Really delete all alerts? This cannot be undone!';
$l['myalerts_delete_mass_deleted'] = 'Alerts Deleted';
$l['myalerts_delete_all'] = 'All alerts deleted successfully.';
$l['myalerts_mark_all_read_confirm'] = 'Really mark all alerts read? This cannot be undone!';

$l['myalerts_usercp_nav'] = 'Alerts';
$l['myalerts_usercp_nav_alerts'] = 'View Alerts';
$l['myalerts_usercp_nav_settings'] = 'Alert Settings';
$l['myalerts_usercp_nav_delete_read'] = 'Delete Read Alerts';
$l['myalerts_usercp_nav_delete_all'] = 'Delete All Alerts';
$l['myalerts_delete_read_deleted'] = 'Read Alerts Deleted';
$l['myalerts_delete_read'] = 'Read alerts deleted successfully.';

$l['myalerts_error_alert_not_found'] = 'Alert not found.';

$l['myalerts_delete'] = 'Delete';

// For the task when run from a user context via the task image bottom of page.
// Duplicated in the admin language file for when the task runs from the ACP.
$l['myalerts_task_cleanup_ran'] = 'Read alerts over {1} days old and unread alerts over {2} days old were deleted successfully!';
$l['myalerts_task_cleanup_error'] = 'Something went wrong while cleaning up the alerts...';
