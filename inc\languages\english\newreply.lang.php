<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_newreply'] = "Post Reply";

$l['post_reply_to'] = "Post Reply to {1}";
$l['post_new_reply'] = "Post a New Reply";
$l['reply_to'] = "Reply to thread: {1}";
$l['post_subject'] = "Post Subject:";
$l['your_message'] = "Your Message:";
$l['post_options'] = "Post Options:";
$l['options_sig'] = "<strong>Signature:</strong> include your signature. (registered users only)";
$l['options_emailnotify'] = "<strong>Email Notification:</strong> receive an email whenever there is a new reply. (registered users only)";
$l['options_disablesmilies'] = "<strong>Disable Smilies:</strong> disable smilies from showing in this post.";
$l['post_reply'] = "Post Reply";
$l['preview_post'] = "Preview Post";
$l['mod_options'] = "Moderator Options:";
$l['close_thread'] = "<strong>Close Thread</strong>: prevent further posting in this thread.";
$l['stick_thread'] = "<strong>Stick Thread:</strong> stick this thread to the top of the forum.";
$l['forum_rules'] = "{1} - Rules";
$l['thread_review'] = "Thread Review (Newest First)";
$l['thread_review_more'] = "This thread has more than {1} replies. <a href=\"{2}\" target=\"_blank\">Read the whole thread.</a>";
$l['posted_by'] = "Posted by";
$l['draft_saved'] = "The new post has successfully been saved as a draft.<br />You will now be taken to your draft listing.";
$l['error_post_already_submitted'] = "You have already posted this reply to the specified thread. Please visit the thread to see your reply.";
$l['multiquote_external_one'] = "You have selected one post outside of this thread.";
$l['multiquote_external'] = "You have selected {1} posts outside of this thread.";
$l['multiquote_external_one_deselect'] = "deselect this post";
$l['multiquote_external_deselect'] = "deselect these posts";
$l['multiquote_external_one_quote'] = "Quote this post too";
$l['multiquote_external_quote'] = "Quote these posts too";

$l['redirect_newreply'] = "Thank you, your reply has been posted.";
$l['redirect_newreply_moderation'] = "The administrator has specified that all new posts require moderation. You will now be returned to the thread.";
$l['redirect_newreply_post'] = "<br />You will now be taken to your post.";
$l['redirect_newreplyerror'] = "Sorry, but your reply has been rejected for lack of content. <br />You will now be returned to the thread.";
$l['redirect_threadclosed'] = "You cannot post replies in this thread because it has been closed by a moderator.";
$l['error_post_noperms'] = "You don't have permission to edit this draft.";


$l['error_stop_forum_spam_spammer'] = 'Sorry, your {1} matches that of a known spammer. If you feel this is a mistake, please contact an administrator';
$l['error_stop_forum_spam_fetching'] = 'Sorry, something went wrong verifying your reply against a spammer database. Most likely the database couldn\'t be accessed. Please try again later.';

$l['error_suspendedposting'] = "Your posting privileges are currently suspended {1}.<br /><br />

Suspension Date: {2}";
$l['error_suspendedposting_temporal'] = "until {1}";
$l['error_suspendedposting_permanent'] = "permanently";

