<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_newthread'] = "New Thread";
$l['newthread_in'] = "New Thread in {1}";
$l['post_new_thread'] = "Post a new Thread";
$l['thread_subject'] = "Thread Subject:";
$l['your_message'] = "Your Message:";
$l['post_options'] = "Post Options:";
$l['options_sig'] = "<b>Signature:</b> include your signature. (registered users only)";
$l['options_emailnotify'] = "<b>Email Notification:</b> receive an email whenever there is a new reply. (registered users only)";
$l['options_disablesmilies'] = "<b>Disable Smilies:</b> disable smilies from showing in this post.";
$l['post_thread'] = "Post Thread";
$l['preview_post'] = "Preview Post";
$l['poll'] = "Poll:";
$l['poll_desc'] = "Optionally you may attach a poll to this thread.";
$l['poll_check'] = "I want to post a poll";
$l['num_options'] = "Number of options:";
$l['max_options'] = "(Maximum: {1})";
$l['mod_options'] = "Moderator Options:";
$l['close_thread'] = "<b>Close Thread</b>: prevent further posting in this thread.";
$l['stick_thread'] = "<b>Stick Thread:</b> stick this thread to the top of the forum.";
$l['draft_saved'] = "The new thread has successfully been saved as a draft.<br />You will now be taken to your draft listing.";
$l['error_post_already_submitted'] = "You have already posted this thread in this forum. Please visit the forum to see your thread.";
$l['no_prefix'] = "No Prefix";
$l['forum_rules'] = "{1} - Rules";

$l['multiquote_external_one'] = "You have selected one post from another thread.";
$l['multiquote_external'] = "You have selected {1} posts from other threads.";
$l['multiquote_external_one_deselect'] = "deselect this post";
$l['multiquote_external_deselect'] = "deselect these posts";
$l['multiquote_external_one_quote'] = "Quote this post too";
$l['multiquote_external_quote'] = "Quote these posts too";

$l['redirect_newthread'] = "Thank you, your thread has been posted.";
$l['redirect_newthread_poll'] = "<br />You will now be taken to the poll options and configuration page.";
$l['redirect_newthread_moderation'] = "<br />The administrator has specified that all new threads require moderation. You will now be returned to the thread listing.";
$l['redirect_newthread_unviewable'] = "<br />You do not have permission to view threads in this forum. You will now be returned to the forum.";
$l['redirect_newthread_thread'] = "<br />You will now be taken to the new thread.";
$l['invalidthread'] = "The specified draft does not exist or you don't have permission to view it.";

$l['error_stop_forum_spam_spammer'] = 'Sorry, your {1} matches that of a known spammer. If you feel this is a mistake, please contact an administrator';
$l['error_stop_forum_spam_fetching'] = 'Sorry, something went wrong verifying your thread against a spammer database. Most likely the database couldn\'t be accessed. Please try again later.';

$l['error_suspendedposting'] = "Your posting privileges are currently suspended {1}.<br /><br />

Suspension Date: {2}";
$l['error_suspendedposting_temporal'] = "until {1}";
$l['error_suspendedposting_permanent'] = "permanently";

