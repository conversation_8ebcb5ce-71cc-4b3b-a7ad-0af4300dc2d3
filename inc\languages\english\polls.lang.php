<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_postpoll'] = "Post Poll";
$l['nav_editpoll'] = "Edit Poll";
$l['nav_pollresults'] = "Poll Results";

$l['edit_poll'] = "Edit Poll";
$l['delete_poll'] = "Delete Poll";
$l['delete_q'] = "Delete?";
$l['delete_note'] = "To delete this poll, check the box to the left and click the button to the right.";
$l['delete_note2'] = "<b>Note:</b> Once this poll has been deleted there is no way of restoring it.";
$l['question'] = "Question:";
$l['num_options'] = "Number of Options:";
$l['max_options'] = "Maximum:";
$l['poll_options'] = "Poll Options:";
$l['update_options'] = "Update Options";
$l['poll_options_note'] = "Poll options should be short and to the point.";
$l['options'] = "Options:";
$l['option_multiple'] = "<b>Allow multiple choice:</b> Users have the ability to vote on more than one option.";
$l['option_multiple_maxoptions'] = "Maximum number of options per user (0 for no limit):";
$l['option_public'] = "<b>Public Poll:</b> Give users the ability to see which user voted on which option(s).";
$l['option_closed'] = "<b>Poll is Closed:</b> If checked the poll will be closed from voting.";
$l['poll_timeout'] = "Poll Timeout:";
$l['timeout_note'] = "The number of day(s) which people can vote on this poll.<br />(Set to 0 to if this poll should not timeout.)";
$l['days_after'] = "days after:";
$l['update_poll'] = "Update Poll";
$l['option'] = "Option";
$l['votes'] = "Votes:";
$l['post_new_poll'] = "Post New Poll";
$l['days'] = "days";
$l['poll_results'] = "Poll Results";
$l['poll_total'] = "Total:";
$l['poll_votes'] = "vote(s)";

$l['redirect_pollposted'] = "Your poll has been posted.<br />You will now be returned to the thread.";
$l['redirect_pollpostedmoderated'] = "Your poll has been posted, but your thread is still pending moderation.<br />You will be returned to the forum.";
$l['redirect_pollupdated'] = "The poll has been updated.<br />You will now be returned to the thread.";
$l['redirect_votethanks'] = "Thank you for voting.<br />You will now be returned to the thread.";
$l['redirect_unvoted'] = "Your vote(s) in this thread have been removed.<br />You will now be returned to the thread.";
$l['redirect_polldeleted'] = "Thank you, the poll has successfully been removed from the thread.<br />You will now be taken back to the thread.";

$l['error_polloptiontoolong'] = "One or more poll options you entered are longer than the acceptable limit. Please go back and shorten them.";
$l['error_polloptionsequence'] = "One or more poll options you entered contain a sequence which should not be used in them: <strong>||~|~||</strong>. Please go back and remove it.";
$l['error_noquestionoptions'] = "You either did not enter a question for your poll or do not have enough options. The minimum number of options a poll can have is 2.<br />Please go back and correct this error.";
$l['error_pollalready'] = "Thread already has poll!";
$l['error_nopolloptions'] = "The specified poll option is invalid or does not exist.";
$l['error_maxpolloptions'] = "You have voted for too many options. You are only allowed to vote for {1} options.<br />Please go back and try again.";
$l['error_alreadyvoted'] = "You have already voted in this poll.";
$l['error_notvoted'] = "You haven't voted in this poll.";
$l['error_invalidpoll'] = "The specified poll is invalid or does not exist.";
$l['error_pollclosed'] = "You cannot vote in a poll that has been closed.";
$l['poll_time_limit'] = "Sorry but you cannot add a poll to your thread. The Administrator has set it so that polls can only be added within {1} hours of posting.";

$l['poll_deleted'] = "Deleted Poll";
$l['poll_edited'] = "Edited Poll";
