<?php
/* Copyright (c) 2012 by <PERSON>.

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERC<PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>. */

$l['privatewarning'] = "Warning for private messages (optional):";
$l['warning_tooshort'] = "Warning for private messages is too short. It must be at least 8 characters.";
$l['warning_toolong'] = "Warning for private messages is too long. Maximum size is 200 characters.";
$l['privatewarning_warning'] = "Warning from the recipients:";
$l['privatewarning_warning_send'] = "Warning from the recipients (resubmit the form to send your message):";
