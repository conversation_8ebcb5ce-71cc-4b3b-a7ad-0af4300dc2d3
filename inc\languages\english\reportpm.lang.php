<?php
/**
 * Report PMs
 * Copyright 2011 Starpaul20
 */

$l['reportpm_info_name'] = "Report Private Messages";
$l['reportpm_info_desc'] = "Allows users to report Private Messages if they are spam/abuse etc.";

$l['report_content_privatemessage'] = "Private Message";

$l['report_info_pm'] = "<a href=\"javascript:;\" onclick=\"{1}\">Private Message</a> from {2}";
$l['postbit_report_pm'] = "Report this private message to a moderator";

$l['view_reported_pm'] = "View Reported Private Message";
$l['subject'] = "Subject:";
$l['from_user'] = "From User:";
$l['to_user'] = "To User:";
$l['date_sent'] = "Date Sent:";
$l['ip_address'] = "IP Address:";
$l['error_badreport'] = "Invalid report specified.";

$l['error_pmreport_unread'] = "This private message has been reported, this message cannot be deleted until the report is read. Please try again later.";
$l['error_pmreport_unread_multi'] = "One or more private messages selected have unread reports, those messages cannot be deleted until those reports are marked as read. Please try again later.";
