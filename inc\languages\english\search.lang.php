<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_search'] = "Search";
$l['nav_results'] = "Results";

$l['pages_last'] = "last";
$l['search'] = "Search";
$l['search_keyword'] = "Search by Keyword";
$l['search_username'] = "Search by Username";
$l['search_entire_post'] = "search entire post";
$l['search_titles_only'] = "search titles only";
$l['match_username'] = "match exact username";
$l['search_forums'] = "Search in Forum(s)";
$l['search_all_forums'] = "Search All Open Forums";
$l['search_options'] = "Search Options";
$l['threads_at_least'] = "Find threads with at least";
$l['threads_at_most'] = "Find threads with at most";
$l['find_anydate'] = "Find posts from any post date";
$l['find_yesterday'] = "Find posts from yesterday";
$l['find_lastweek'] = "Find posts from a week ago";
$l['find_2weeks'] = "Find posts from 2 weeks ago";
$l['find_month'] = "Find posts from a month ago";
$l['find_3months'] = "Find posts from 3 months ago";
$l['find_6months'] = "Find posts from 6 months ago";
$l['find_year'] = "Find posts from a year ago";
$l['and_newer'] = "and newer";
$l['and_older'] = "and older";
$l['sorting_options'] = "Sorting Options";
$l['sort_lastpost'] = "Sort Results by last post date";
$l['sort_author'] = "Sort Results by author";
$l['sort_forum'] = "Sort Results by forum";
$l['sort_views'] = "Sort Results by view count";
$l['sort_replies'] = "Sort Results by reply count";
$l['sort_in'] = "in";
$l['sort_asc'] = "ascending";
$l['sort_desc'] = "descending";
$l['sort_order'] = "order";
$l['asc'] = "asc";
$l['desc'] = "desc";
$l['display_options'] = "Display Options";
$l['show_results_as'] = "Show Results as";
$l['show_results_threads'] = "threads";
$l['show_results_posts'] = "posts";
$l['search_results'] = "Search Results";
$l['post'] = "Post";
$l['author'] = "Author";
$l['forum'] = "Forum";
$l['replies'] = "Replies";
$l['views'] = "Views";
$l['posted'] = "Posted";
$l['thread'] = "Thread";
$l['lastpost'] = "Last Post";
$l['post_thread'] = "Thread:";
$l['post_subject'] = "Post:";
$l['replies2'] = "replies";
$l['selectall'] = "Select all the additional results?";
$l['any_prefix'] = "Find threads with any prefix";
$l['no_prefix'] = "No prefix";
$l['inline_thread_moderation'] = "Inline Thread Moderation:";
$l['close_threads'] = "Close Threads";
$l['open_threads'] = "Open Threads";
$l['stick_threads'] = "Stick Threads";
$l['unstick_threads'] = "Unstick Threads";
$l['soft_delete_threads'] = "Soft Delete Threads";
$l['restore_threads'] = "Restore Threads";
$l['delete_threads'] = "Delete Threads Permanently";
$l['move_threads'] = "Move / Copy Threads";
$l['approve_threads'] = "Approve Threads";
$l['unapprove_threads'] = "Unapprove Threads";
$l['inline_soft_delete_posts'] = "Soft Delete Posts";
$l['inline_restore_posts'] = "Restore Posts";
$l['inline_delete_posts'] = "Delete Posts Permanently";
$l['inline_merge_posts'] = "Merge Posts";
$l['inline_split_posts'] = "Split Posts";
$l['inline_move_posts'] = "Move Posts";
$l['inline_approve_posts'] = "Approve Posts";
$l['inline_unapprove_posts'] = "Unapprove Posts";
$l['inline_post_moderation'] = "Inline Post Moderation:";
$l['inline_go'] = "Go";
$l['clear'] = "Clear";
$l['icon_dot'] = "Contains posts by you. "; // The spaces for the icon labels are strategically placed so that there should be no extra space at the beginning or end of the resulting label and that spaces separate each 'status' ;)
$l['icon_no_new'] = "No new posts.";
$l['icon_new'] = "New posts.";
$l['icon_hot'] = " Hot thread.";
$l['icon_close'] = " Closed thread.";
$l['attachment_count'] = "This thread contains 1 attachment.";
$l['attachment_count_multiple'] = "This thread contains {1} attachments.";
$l['goto_first_unread'] = "Go to first unread post";
$l['page_selected'] = "All <strong>{1}</strong> results on this page are selected.";
$l['all_selected'] = "All <strong>{1}</strong> results in this search are selected.";
$l['select_all'] = "Select all <strong>{1}</strong> results in this search.";
$l['clear_selection'] = "Clear Selection.";

$l['results'] = "results";
$l['mod_options'] = "Moderator Options";
$l['display_all'] = "Display all";
$l['display_only_approved'] = "Display only approved";
$l['display_only_unapproved'] = "Display only unapproved";
$l['display_only_softdeleted'] = "Display only soft deleted";

$l['redirect_searchresults'] = "Thank you, your search has been submitted and you will now be taken to the results list.";

$l['error_minsearchlength'] = "One or more of your search terms were shorter than the minimum length. The minimum search term length is {1} characters.<br /><br />If you're trying to search for an entire phrase, enclose it within double quotes. For example \"The quick brown fox jumps over the lazy dog\".";
$l['error_nosearchresults'] = "Sorry, but no results were returned using the query information you provided. Please redefine your search terms and try again.";
$l['error_no_search_support'] = "This database engine does not support searching.";
$l['error_nosearchterms'] = "You did not enter any search terms. At a minimum, you must enter either some search terms or a username to search by.";
$l['error_searchflooding_1'] = "Sorry, but you can only perform one search every {1} seconds. Please wait another 1 second before attempting to search again.";
$l['error_searchflooding'] = "Sorry, but you can only perform one search every {1} seconds. Please wait another {2} seconds before attempting to search again.";
$l['error_invalidsearch'] = "An invalid search was specified.  Please go back and try again.";
