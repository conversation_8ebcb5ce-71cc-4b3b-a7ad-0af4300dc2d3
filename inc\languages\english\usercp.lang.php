<?php
/**
 * MyBB 1.8 English Language Pack
 * Copyright 2014 MyBB Group, All Rights Reserved
 *
 */

$l['nav_usercp'] = "User Control Panel";
$l['nav_profile'] = "Edit Profile";
$l['nav_options'] = "Edit Options";
$l['nav_email'] = "Change Email Address";
$l['nav_password'] = "Change Password";
$l['nav_changename'] = "Change Username";
$l['nav_subthreads'] = "Subscribed Threads";
$l['nav_forumsubscriptions'] = "Subscribed Forums";
$l['nav_editsig'] = "Edit Signature";
$l['nav_avatar'] = "Change Avatar";
$l['nav_notepad'] = "Personal Notepad";
$l['nav_editlists'] = "Manage Buddy and Ignore Lists";
$l['nav_drafts'] = "Saved Drafts";
$l['nav_usergroups'] = "Group Memberships";
$l['nav_attachments'] = "Attachment Manager";
$l['nav_addsubscription'] = "Add Subscription";
$l['nav_removesubscription'] = "Remove Subscription";

$l['resend_activation'] = "Resend Activation Email";
$l['away_notice_away'] = "You have been marked away since {1}";
$l['away_notice'] = "This option will allow you to select whether you are away or not.";
$l['already_uploaded_avatar'] = "You are currently using an uploaded image. If you choose to use another one, your old avatar image will be deleted from the server.";
$l['using_remote_avatar'] = "You are currently using an image from a remote site. If you choose to use another one, your old avatar URL will be emptied.";
$l['na_deleted'] = "N/A - Been Deleted";
$l['user_cp'] = "User Control Panel";
$l['user_cp_welcome'] = "This is your user control panel. You can access your profile, subscriptions, and private messaging all from here.<br />The main page has information on your account.";
$l['account_summary'] = "Your Account Summary";
$l['username'] = "Username:";
$l['user_id'] = "User ID:";
$l['title'] = "User Title:";
$l['postnum'] = "Posts:";
$l['posts_day'] = "({1} per day | {2} percent of total posts)";
$l['additional_contact_details'] = "Additional Contact Information";
$l['email'] = "Email:";
$l['reputation'] = "Reputation:";
$l['website'] = "Website:";
$l['usergroup'] = "User Group:";
$l['birthday'] = "Date of Birth:";
$l['birthdayprivacy'] = "Date of Birth Privacy:";
$l['birthdayprivacyall'] = "Display Age and Date of Birth";
$l['birthdayprivacynone'] = "Hide Age and Date of Birth";
$l['birthdayprivacyage'] = "Display Only Age";
$l['avatar'] = "Avatar:";
$l['avatar_mine'] = "This is your Avatar";
$l['change_avatar'] = "Change Avatar";
$l['avatar_url'] = "Avatar URL:";
$l['avatar_url_note'] = "Enter the URL of an avatar on the internet.";
$l['avatar_url_gravatar'] = "To use a <a href=\"https://gravatar.com\" target=\"_blank\" rel=\"noopener\">Gravatar</a> enter your Gravatar email.";
$l['avatar_upload'] = "Upload Avatar:";
$l['avatar_upload_note'] = "Choose an avatar on your local computer to upload.";
$l['no_avatar'] = "No Avatar";
$l['no_avatar_note'] = "Select this option if you don't want an avatar.";
$l['change_username'] = "Change Username";
$l['new_username'] = "New Username:";
$l['update_username'] = "Update Username";
$l['edit_lists'] = "Edit Buddy and Ignore Lists";
$l['edit_buddy_list'] = "Edit Buddy List";
$l['edit_ignore_list'] = "Edit Ignore List";
$l['users_added_to_ignore_list'] = "The selected user(s) have been added to your ignore list";
$l['users_added_to_buddy_list'] = "The selected user(s) have been added to your buddy list";
$l['removed_from_ignore_list'] = "{1} has been removed from your ignore list";
$l['removed_from_buddy_list'] = "{1} has been removed from your buddy list";
$l['cant_add_self_to_ignore_list'] = "You cannot add yourself to your ignore list.";
$l['cant_add_self_to_buddy_list'] = "You cannot add yourself to your buddy list.";
$l['users_already_on_ignore_list'] = "One or more users you added are already on your ignore list.";
$l['users_already_on_ignore_list_alt'] = "One or more users you added is on your buddy list. Please remove them as a buddy before ignoring them.";
$l['users_already_on_buddy_list'] = "One or more users you added are already on your buddy list.";
$l['users_already_on_buddy_list_alt'] = "One or more users you added is on your ignore list. Please remove them from the list before adding them as a buddy.";
$l['invalid_user_selected'] = "One or more of the selected users were not found.";
$l['ignore_list_empty'] = "Your ignore list is currently empty. To add one or more users to your ignore list, please use the field above.";
$l['buddy_list_empty'] = "Your buddy list is currently empty. To add one or more users to your buddy list, please use the field above.";
$l['confirm_remove_buddy'] = "Remove this user from your buddy list?";
$l['confirm_remove_ignored'] = "Remove this user from your ignore list?";
$l['adding_buddy'] = "Adding Buddy&hellip;";
$l['adding_ignored'] = "Adding to Ignored&hellip;";
$l['add_buddies'] = "Add Users to your Buddy List";
$l['add_buddies_desc'] = "To add one or more users to your buddy list, enter their usernames below. Separate multiple usernames with a comma.";
$l['username_or_usernames'] = "Username(s):";
$l['add_to_buddies'] = "Add to Buddies";
$l['current_buddies'] = "You currently have <span id=\"buddy_count\">{1}</span> user(s) on your buddy list";
$l['add_ignored_users'] = "Add Users to your Ignore List";
$l['add_ignored_users_desc'] = "To ignore posts and private messages from specific users, enter their username below. You can separate multiple names with a comma.";
$l['ignore_users'] = "Ignore User(s)";
$l['current_ignored_users'] = "You currently have <span id=\"ignored_count\">{1}</span> user(s) on ignore";
$l['online'] = "Online";
$l['offline'] = "Offline";
$l['remove_from_list'] = "Remove from list";
$l['edit_sig'] = "Edit Signature";
$l['edit_sig_note'] = "Here you can enter a short message which will be automatically appended to the bottom of your posts.";
$l['edit_sig_note2'] = "Smilies are {1}.<br />MyCode is {2}.<br />[img] tags are {3}.<br />HTML is {4}.<br />Max. length is {5} characters.";
$l['edit_sig_error_title'] = "An error has occurred:";
$l['edit_sig_no_permission'] = "You do not have permission to edit your signature.";
$l['characters_remaining'] = "characters remaining";
$l['enable_sig_posts'] = "Enable my signature in all of my existing posts.";
$l['disable_sig_posts'] = "Disable my signature in all of my existing posts.";
$l['leave_sig_settings'] = "No action.";
$l['update_sig'] = "Update Signature";
$l['preview'] = "Preview Signature";
$l['current_sig'] = "Your Current Signature";
$l['sig_preview'] = "Signature Preview";

$l['sig_suspended'] = "Your ability to add a signature has been suspended.";
$l['sig_suspended_posts'] = "You must have at least {1} posts before adding a signature.";

$l['change_email'] = "Change Email Address";
$l['please_enter_confirm_new_email'] = "Please Enter and Confirm Your New Email Address";
$l['new_email'] = "New Email Address:";
$l['confirm_email'] = "Confirm Email Address:";
$l['update_email'] = "Update Email Address";
$l['thread'] = "Thread";
$l['author'] = "Author";
$l['replies'] = "Replies";
$l['views'] = "Views";
$l['lastpost'] = "Last Post";
$l['post_reply'] = "Post Reply";
$l['forum_subscriptions'] = "Forum Subscriptions";
$l['posts'] = "Posts";
$l['forum'] = "Forum";
$l['threads'] = "Threads";
$l['unsubscribe'] = "Unsubscribe";
$l['new_thread'] = "New Thread";
$l['personal_notepad'] = "Personal Notepad";
$l['update_notepad'] = "Update Notepad";
$l['edit_options'] = "Edit Options";
$l['login_cookies_privacy'] = "Privacy";
$l['invisible_mode'] = "Hide me from the Who's Online list.";
$l['invisible_mode_desc'] = "Selecting yes will hide you from the online users list. This setting has no effect for buddies on your current buddies list.";
$l['messaging_notification'] = "Messaging and Notification";
$l['allow_notices'] = "Receive emails from the Administrators.";
$l['allow_notices_desc'] = "Selecting yes will allow Administrators to send you notices and board newsletters.";
$l['allow_emails'] = "Hide your email from other members.";
$l['allow_emails_desc'] = "Selecting yes will allow other members to send you emails through this board.";
$l['email_notify'] = "Automatically subscribe to threads you post in.";
$l['email_notify_desc'] = "Selecting yes will automatically subscribe you to threads when you make a post in them.";
$l['receive_pms'] = "Receive private messages from other users.";
$l['receive_pms_desc'] = "Enables you to send and receive Private Messages.";
$l['receive_from_buddy'] = "Only receive private messages from users on my <a href=\"#\" onclick=\"MyBB.popupWindow('/misc.php?action=buddypopup'); return false;\" target=\"_blank\">Buddy List</a>. This setting has no effect unless there is at least one buddy on the list.";
$l['pm_notice'] = "Alert me with a notice when I receive a Private Message.";
$l['pm_notify'] = "Notify me by email when I receive a new Private Message.";
$l['show_codebuttons'] = "Show the MyCode formatting options on the posting pages.";
$l['source_editor'] = "Put the editor in source mode by default.";
$l['show_redirect'] = "Show friendly redirect pages.";
$l['thread_view_options'] = "Thread View Options";
$l['thread_mode'] = "Thread View Mode:";
$l['thread_mode_desc'] = "The style of threads shown to you. Selecting 'Use Default' will use the boards default mode.";
$l['threaded'] = "Threaded";
$l['linear'] = "Linear";
$l['show_classic_postbit'] = "Display posts in classic mode.";
$l['show_images'] = "Display images in posts.";
$l['show_videos'] = "Display videos in posts.";
$l['show_sigs'] = "Display users' signatures in their posts.";
$l['show_sigs_desc'] = "Do you want to view user's signatures in their posts?";
$l['show_avatars'] = "Display users' avatars in their posts.";
$l['show_avatars_desc'] = "Do you want to view user's avatars in their posts?";
$l['show_quick_reply'] = "Show the quick reply box on the view thread page.";
$l['show_quick_reply_desc'] = "The quick reply box enables you to make a 'quick reply' to threads using the reply box provided at the end of a thread";
$l['forum_display_options'] = "Forum Display Options";
$l['thread_view'] = "Default Thread View:";
$l['thread_view_lastday'] = "Show threads from last day";
$l['thread_view_5days'] = "Show threads from last 5 days";
$l['thread_view_10days'] = "Show threads from last 10 days";
$l['thread_view_20days'] = "Show threads from last 20 days";
$l['thread_view_50days'] = "Show threads from last 50 days";
$l['thread_view_75days'] = "Show threads from last 75 days";
$l['thread_view_100days'] = "Show threads from last 100 days";
$l['thread_view_year'] = "Show threads from the last year";
$l['thread_view_all'] = "Show all threads";
$l['date_time_options'] = "Date and Time Options";
$l['date_format'] = "Date Format:";
$l['date_format_desc'] = "The format that dates will be shown in.";
$l['time_format'] = "Time Format:";
$l['time_format_desc'] = "The format that times will be shown in.";
$l['time_offset'] = "Time Zone (<abbr title=\"Daylight Saving Time\">DST</abbr> correction excluded):";
$l['time_offset_desc'] = "If you live in a time zone which differs to what this board is set at, you can select it from the list below.";
$l['gmt'] = "GMT";
$l['dst_correction'] = "Daylight Saving Time correction:";
$l['dst_correction_auto'] = "Automatically detect DST settings";
$l['dst_correction_enabled'] = "Always use DST correction";
$l['dst_correction_disabled'] = "Never use DST correction";
$l['board_language'] = "Board Language";
$l['other_options'] = "Other Options";
$l['style'] = "Board Style:";
$l['style_desc'] = "If you don't like the default style of this board you can change how you view it here.";
$l['update_options'] = "Update Options";
$l['tpp_option'] = "Show {1} threads per page";
$l['ppp_option'] = "Show {1} posts per page";
$l['ppp'] = "Posts Per Page:";
$l['ppp_desc'] = "Allows you to select the amount of posts to be shown per page in a thread.";
$l['tpp'] = "Threads Per Page:";
$l['tpp_desc'] = "Allows you to select the amount of threads to be shown per page in the thread listing.";
$l['change_password'] = "Change Password";
$l['current_password'] = "Current Password:";
$l['password_confirmation'] = "Password Confirmation";
$l['please_enter_confirm_new_password'] = "Please Enter and Confirm Your New Password";
$l['new_password'] = "New Password:";
$l['confirm_password'] = "Confirm Password:";
$l['update_password'] = "Update Password";
$l['edit_profile'] = "Edit Profile";
$l['profile_required'] = "Required Fields";
$l['change_email_notice'] = "To change your email address please click <a href=\"usercp.php?action=email\">here</a>";
$l['profile_optional'] = "Optional Fields";
$l['website_url'] = "Your Website URL:";
$l['birthdate'] = "Birthdate:";
$l['contact_field_skype'] = "Skype ID:";
$l['contact_field_google'] = "Google Hangouts ID:";
$l['contact_field_error'] = "Sorry, but we cannot change your contact information as the ID specified is too long. Google Hangouts IDs and Skype IDs can be up to 75 characters long.";
$l['additional_information'] = "Additional Information";
$l['update_profile'] = "Update Profile";
$l['away_information'] = "Away Information";
$l['away_status'] = "Away Status:";
$l['away_status_desc'] = "Allows you to leave an away message if you are going away for a while.";
$l['im_away'] = "I'm Away";
$l['im_here'] = "I'm Here";
$l['away_reason'] = "Away Reason:";
$l['away_reason_desc'] = "Allows you to enter a small description of why you are away  (max 200 characters).";
$l['return_date'] = "Return Date:";
$l['return_date_desc'] = "If you know when you will be back, you can enter your return date here.";
$l['subscriptions'] = "Thread Subscriptions";
$l['remove_all_subscriptions'] = "Remove All Subscriptions";
$l['no_thread_subscriptions'] = "You're currently not subscribed to any threads.<p>To subscribe to a thread:</p><ol><li>Navigate to the thread you wish to subscribe to.</li><li>Click the 'Subscribe to this thread' link towards the bottom of the page.</li></ol>";
$l['no_forum_subscriptions'] = "You're currently not subscribed to any forums.<p>To subscribe to a forum:</p><ol><li>Navigate to the forum you wish to subscribe to.</li><li>Click the 'Subscribe to this forum' link just above, towards the right side of, the list of threads in that forum.</li></ol>";
$l['no_drafts'] = "You do not currently have any draft threads or posts.<p>To save a draft thread or post:</p><ol><li>Proceed to the normal posting pages for the action you wish to perform (post a new thread or reply to an existing one).</li><li>Fill in as much or as little as you want on the posting page.</li><li>Click the 'Save as Draft' button below the posting area.</li></ol>";
$l['drafts'] = "Saved Drafts";
$l['drafts_count'] = "Saved Drafts ({1})";
$l['draft_saved'] = "Saved";
$l['edit_draft'] = "Edit Draft";
$l['draft_title'] = "Draft Title";
$l['delete_drafts'] = "Delete Selected Drafts";
$l['draft_options'] = "Draft Options";
$l['selected_drafts_deleted'] = "The selected drafts have been deleted.<br />You will be now taken back to the drafts listing.";
$l['no_drafts_selected'] = "You did not select any drafts to delete";
$l['group_memberships'] = "Group Memberships";
$l['not_member_of_group'] = "Sorry, you cannot perform that action because you are currently not a member of that group.";
$l['cannot_set_displaygroup'] = "This group cannot be set as a display group.";
$l['display_group_changed'] = "Your display group has been updated.<br />You will be now taken back to the group memberships page.";
$l['usergroups_memberof'] = "Groups You're a Member Of";
$l['usertitle'] = "User Title";
$l['usergroup_leave'] = "Leave Group";
$l['usergroup_leave_primary'] = "(You cannot leave your primary group)";
$l['usergroup_leave_leader'] = "(You are a leader of this group)";
$l['usergroup_joins_moderated'] = "A group leader is required to moderate joins to this group.";
$l['usergroup_joins_invite'] = "A group leader must invite you to join this group.";
$l['usergroup_cannot_leave'] = "(You cannot leave this user group)";
$l['usergroup_joins_anyone'] = "Anyone is free to join this group.";
$l['usergroup_leaders'] = "Group led by:";
$l['usergroups_joinable'] = "Groups You Can Join";
$l['join_conditions'] = "Join Conditions";
$l['join_group'] = "Join Group";
$l['join_group_applied'] = "You applied to join this group: {1}";
$l['pending_invitation'] = "You have been invited to this group: <a href=\"usercp.php?action=usergroups&amp;acceptinvite={1}&amp;my_post_key={2}\">Accept Invite</a>";
$l['usergroups_leader'] = "Groups You Lead";
$l['usergroup_members'] = "Group Members";
$l['join_requests'] = "Pending Join Requests";
$l['request_join_usergroup'] = "Request Group Membership";
$l['join_group_moderate_note'] = "All joins to this group must first be approved by a moderator.";
$l['user_group'] = "Group:";
$l['usergroups_usergroup'] = "User Group";
$l['join_reason'] = "Reason:";
$l['send_join_request'] = "Send Join Request";
$l['cannot_join_group'] = "Sorry, but you cannot join this group as it is not a publicly join-able group.";
$l['cannot_join_invite_group'] = "In order to join this group, you must be invited by a group leader.";
$l['no_pending_invitation'] = "You do not have any pending invitation to this group.";
$l['already_accepted_invite'] = "You have already accepted an invite to join this group.";
$l['already_member_of_group'] = "You cannot join a group of which you are already a member of.";
$l['already_sent_join_request'] = "You have already sent a request to join this group and it is yet to be moderated.";
$l['joinreason_too_long'] = "Specified Join Reason is too long. A maximum of 250 characters is allowed. Please remove {1} character(s) from this field.";
$l['group_join_requestsent'] = "Your join request has been successfully sent. Once your membership is approved you will be automatically joined to this user group.<br />You will be now taken to the group memberships page.";
$l['joined_group'] = "You have successfully joined the specified group.<br />You will be now taken back to the group management page.";
$l['cannot_leave_primary_group'] = "Sorry, but you cannot leave your primary group.";
$l['left_group'] = "You have successfully left the specified group.<br />You will be now taken back to the group management page.";
$l['avatar_note'] = "An avatar is a small identifying image shown under the author's name whenever they make a post.";
$l['avatar_note_dimensions'] = "The maximum dimensions for avatars are: {1}x{2} pixels.";
$l['avatar_note_size'] = "The maximum file size for avatars is {1}.";
$l['custom_avatar'] = "Custom Avatar";
$l['remove_avatar'] = "Remove Avatar";
$l['attachments_manager'] = "Attachments Manager";
$l['attachments_attachment'] = "Attachment";
$l['attachments_post'] = "Post";
$l['delete_attachments'] = "Delete Selected Attachments";
$l['attachment_size_downloads'] = "({1}, {2} Downloads)";
$l['attachment_thread'] = "Thread:";
$l['no_attachments'] = "You currently do not have any files attached to your posts.";
$l['date_uploaded'] = "Posted";
$l['no_attachments_selected'] = "You did not select any attachments to delete.";
$l['attachments_deleted'] = "The selected attachments have been successfully deleted.<br />You will be now redirected to the attachments manager.";
$l['attachments_usage_quota'] = "- Using {1} of {2} in {3} Attachments";
$l['attachments_usage_percent'] = " ({1}%)";
$l['attachments_usage'] = "- {1} in {2} Attachments";
$l['attachments_stats'] = "Your Attachment Statistics";
$l['attachstats_attachs'] = "Number of Attachments";
$l['attachstats_spaceused'] = "Space Used";
$l['attachstats_quota'] = "Attachment Quota";
$l['attachstats_totaldl'] = "Total Downloads";
$l['attachstats_bandwidth'] = "Approximate Bandwidth Usage";
$l['error_avatarurltoolong'] = "Sorry, but we cannot change your avatar as the new avatar URL specified is too long. You can try to use a shorter URL which is less than 200 characters long.";
$l['error_avatartoobig'] = "Sorry, but we cannot change your avatar as the new avatar you specified is too big. The maximum dimensions are {1}x{2} (width x height).";
$l['error_invalidavatarurl'] = "The URL you entered for your avatar does not appear to be valid. Please ensure you enter a valid URL.";
$l['error_remote_avatar_not_allowed'] = "Remote avatar URLs have been disabled by the forum administrator.";
$l['error_avatarimagemissing'] = "Avatar image file is missing, please select one before attempting to upload.";
$l['custom_usertitle'] = "Custom User Title";
$l['new_custom_usertitle'] = "New Custom User Title: (leave blank to use existing)";
$l['custom_usertitle_note'] = "Here you can assign yourself a custom user title which will overwrite the one based on your display group.";
$l['default_usertitle'] = "Default User Title:";
$l['current_custom_usertitle'] = "Current Custom User Title:";
$l['revert_usertitle'] = "Revert to group default";
$l['primary_usergroup'] = "Primary User Group:";
$l['display_group'] = "Display Group";
$l['set_as_display_group'] = "Set as Display Group";
$l['registration_date'] = "Registration Date:";
$l['view_members'] = "View Members";
$l['view_requests'] = "Join Requests";
$l['cannot_leave_group'] = "You cannot leave this group because it is not a publicly join-able group.";
$l['details'] = "Details";
$l['members_referred'] = "Members Referred:";
$l['referral_link'] = "<br />To refer a member to this board, direct them to {1}/member.php?action=register&amp;referrer={2}";
$l['redirect_subscriptionsremoved'] = "Your thread subscriptions list has been cleared.<br />You will be now returned to the thread subscriptions list.";
$l['redirect_forumsubscriptionsremoved'] = "Your forum subscriptions list has now been cleared.<br />You will be now returned to where you came from.";
$l['redirect_subscriptionadded'] = "The selected thread has been added to your subscriptions list.<br />You will be now returned to the location you came from.";
$l['redirect_optionsupdated'] = "You have successfully updated your options.<br />You will be now returned to the edit options page.";
$l['redirect_subscriptionremoved'] = "The selected thread has been removed from your subscriptions list.<br /> You will be now returned to the thread subscriptions list.";
$l['redirect_sigupdated'] = "Your signature has been successfully updated.<br />You will be now returned to the signature settings.";
$l['redirect_notepadupdated'] = "Your personal notepad has been updated.<br />You will be now returned to the User CP.";
$l['redirect_profileupdated'] = "Your profile has been successfully updated.<br />You will be now returned to the profile settings.";
$l['redirect_forumsubscriptionadded'] = "The selected forum has been added to your forum subscriptions list.<br />You will be now returned to where you came from.";
$l['redirect_forumsubscriptionremoved'] = "The selected forum has been removed from your forum subscriptions list.<br />You will be now returned to where you came from.";
$l['redirect_namechanged'] = "Your name has been successfully changed.<br />You will be now returned to the username settings.";
$l['redirect_emailupdated'] = "Your email address has been successfully updated.<br />You will be now returned to the email settings.";
$l['redirect_passwordupdated'] = "Your password has been successfully updated.<br />You will be now returned to the password settings.";
$l['redirect_changeemail_activation'] = "Your email address has been successfully updated. For your new email address to become active, we require that you complete a validation process.<p>Please check the new email address you specified for further instructions on how to complete the account activation process.</p>";
$l['redirect_avatarupdated'] = "Your avatar has been successfully changed.<br />You will be now returned to the avatar settings.";
$l['error_noavatar'] = "You did not choose an avatar. Please go back and do so now. If you don't want an avatar, select the \"No avatar\" option.";
$l['error_avatartype'] = "Invalid file type. An uploaded avatar must be in GIF, JPEG, BMP or PNG format.";
$l['error_alreadyingroup'] = "The user specified is already a part of the user group.";
$l['error_usercp_return_date_past'] = "You cannot return in the past!";
$l['error_avatarresizefailed'] = "Your avatar was unable to be resized so that it is within the required dimensions.";
$l['error_avataruserresize'] = "You can also try checking the 'attempt to resize my avatar' check box and uploading the same image again.";
$l['avatar_auto_resize_note'] = "If your avatar is too large, it will automatically be resized.";
$l['avatar_auto_resize_option'] = "Try to resize my avatar if it is too large.";
$l['subscribe_to_thread'] = "Subscribe to Thread: {1}";
$l['unsubscribe_from_forum'] = "Unsubscribe from Forum: {1}";
$l['unsubscribe_from_forum_desc'] = "Please confirm that you wish to unsubscribe from this forum. You will not receive any automatic notifications of new threads in this forum and it will not appear in your list of subscribed forums.";
$l['unsubscribe_from_thread'] = "Unsubscribe from Thread: {1}";
$l['unsubscribe_from_thread_desc'] = "Please confirm that you wish to unsubscribe from this thread. You will not receive any automatic notifications of replies to this thread and it will not appear in your list of subscribed threads.";
$l['unsubscribe_change_notificaton'] = "Change the notification method instead";
$l['notification_method'] = "Notification Method:";
$l['no_notification'] = "No Notification";
$l['no_notification_desc'] = "You will not receive any automatic notifications of replies to this thread.";
$l['email_notification'] = "Instant Email Notification";
$l['email_notification_desc'] = "You will receive instant notification via email of any replies made to this thread.";
$l['pm_notification'] = "Instant PM Notification";
$l['pm_notification_desc'] = "You will receive instant notification via PM of any replies made to this thread.";
$l['do_subscribe'] = "Subscribe to Thread";
$l['do_unsubscribe_forum'] = "Unsubscribe from Forum";
$l['do_unsubscribe_thread'] = "Unsubscribe from Thread";
$l['subscription_method'] = "Default Thread Subscription Mode:";
$l['no_auto_subscribe'] = "Do not subscribe";
$l['no_subscribe'] = "No notification";
$l['instant_email_subscribe'] = "Instant email notification";
$l['instant_pm_subscribe'] = "Instant PM notification";
$l['with_selected'] = "With Selected:";
$l['delete_subscriptions'] = "Delete subscriptions";
$l['update_no_notification'] = "Change to no notification";
$l['update_email_notification'] = "Change to email notification";
$l['update_pm_notification'] = "Change to PM notification";
$l['no_subscriptions_selected'] = "You did not select any subscriptions to perform the selected action on.";
$l['redirect_subscriptions_updated'] = "The selected subscriptions have been updated.<br />You will be now returned to the thread subscriptions list.";
$l['latest_threads'] = "Your Latest Threads";
$l['find_all_threads'] = "Find All of Your Threads";
$l['new_thread_subscriptions'] = "Thread Subscriptions With New Posts";
$l['view_all_subscriptions'] = "View All Subscriptions";
$l['latest_warnings'] = "Latest Warnings Received";
$l['current_warning_level'] = "Current warning level: <strong>{1}%</strong> ({2}/{3})";
$l['warning'] = "Warning";
$l['date_issued'] = "Date Issued";
$l['expiry_date'] = "Expiry Date";
$l['issued_by'] = "Issued By";
$l['warning_for_post'] = "for post: ";
$l['warning_revoked'] = "Revoked";
$l['already_expired'] = "Expired";
$l['warning_points'] = "({1} points)";
$l['new_posts_thread'] = "New Posts";
$l['new_hot_thread'] = "Hot Thread (New)";
$l['posts_by_you'] = "Contains Posts by You";
$l['no_new_thread'] = "No New Posts";
$l['hot_thread'] = "Hot Thread (No New)";
$l['closed_thread'] = "Closed Thread";
$l['icon_dot'] = "Contains posts by you. "; // The spaces for the icon labels are strategically placed so that there should be no extra space at the beginning or end of the resulting label and that spaces separate each 'status' ;)
$l['icon_no_new'] = "No new posts.";
$l['icon_new'] = "New posts.";
$l['icon_hot'] = " Hot thread.";
$l['icon_close'] = " Closed thread.";
$l['goto_first_unread'] = "Go to first unread post";

$l['buddylist_error'] = 'There was an error fetching the buddy list. ';

$l['buddyrequests_sent'] = 'Buddy Requests Sent';
$l['buddyrequests_received'] = 'Buddy Requests Received';
$l['from'] = 'From';
$l['to'] = 'To';
$l['date'] = 'Date';
$l['options'] = 'Options';
$l['accept'] = 'Accept';
$l['decline'] = 'Decline';
$l['cancel'] = 'Cancel';
$l['no_requests'] = 'No requests found.';
$l['buddyrequests_pm'] = 'Receive PM notifications for new buddy requests.';
$l['buddyrequests_auto'] = 'Automatically accept buddy requests (if the above checkbox is ticked, a PM is sent informing of the new buddy connection).';
$l['buddyrequest_received'] = 'Buddy request received';
$l['buddyrequest_new_buddy'] = 'You have a new buddy';
$l['buddyrequest_new_buddy_message'] = "Hi,\n\nI have been automatically added to your buddy list.";
$l['buddyrequest_accepted_request'] = 'I have accepted your buddy request';
$l['buddyrequest_accepted_request_message'] = "Hi,\n\nI have accepted your buddy request.";
$l['buddyrequest_received_message'] = "I have just sent you a buddy request.\nYou can view your pending requests from User Control Panel -> Buddy/Ignore List.";
$l['users_already_sent_request'] = "You have already sent a buddy request to one of the users you added.";
$l['users_already_rec_request'] = "You have a pending buddy request from one of the users you added.";
$l['users_already_sent_request_alt'] = "You have sent a buddy request to one of the users you added. Please cancel the buddy request before adding the user to the ignore list.";
$l['users_already_rec_request_alt'] = "You have a pending buddy request from one of the users you added. Please decline the buddy request before adding the user to the ignore list.";
$l['invalid_request'] = 'You have selected an invalid buddy request.';
$l['buddyrequest_cancelled'] = 'The selected buddy request has been cancelled.';
$l['buddyrequest_accepted'] = 'The selected buddy request has been accepted.';
$l['buddyrequest_declined'] = 'The selected buddy request has been declined.';
$l['user_doesnt_exist'] = 'The end user no longer exists.';
$l['buddyrequests_sent_success'] = 'Buddy requests sent successfully.';
