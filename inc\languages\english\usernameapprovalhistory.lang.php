<?php
/**
 * Username Change Approval and History
 * Copyright 2012 Starpaul20
 */

$l['nav_profile'] = "Profile of {1}";
$l['nav_usernamehistory'] = "Username History";
$l['viewing_username_history'] = "Viewing Username History";
$l['viewing_username_history2'] = "Viewing Username History of <a href=\"misc.php?action=usernamehistory&amp;uid={1}\">{2}</a>";

$l['username_history_for'] = "Username History for {1}";
$l['old_username'] = "Old Username";
$l['date_changed'] = "Date Changed";
$l['ip_address'] = "IP Address";
$l['options'] = "Options";
$l['delete'] = "Delete";
$l['no_history'] = "There is no username history associated with this user.";
$l['username_changes'] = "Username Changes";
$l['admin_change'] = "Username was changed by {1} from the Admin CP";
$l['approval_notice'] = "Please note your username change must be approved by an Administrator before you can use the new username.";
$l['max_changes_message_days'] = "You are allowed to change your username {1} times in a {2} day period.";
$l['max_changes_message_day'] = "You are allowed to change your username {1} times per day.";
$l['max_changes_message'] = "You are allowed to change your username {1} times.";
$l['num_changes_left'] = "You have <strong>{1}</strong> changes left.";
$l['num_change_left'] = "You have <strong>1</strong> change left.";

$l['invalid_user'] = "The specified user is invalid or does not exist.";
$l['error_max_changes_day'] = "You cannot change your username because you've already used up your allocated quota of changing your name {1} times in the past {2} days.";
$l['error_max_changes'] = "You cannot change your username because you've already used up your allocated quota of changing your name {1} times.";
$l['redirect_namechangedapproval'] = "Your name has successfully been changed. Please note that the Administrator requires new usernames to be approved before you can use it.<br />You will now be returned to the User CP.";
$l['error_alreadyawaiting'] = "You already have a username change request waiting.";
$l['error_minimum_wait_time'] = "You must wait a minimum of {1} hours before you can change your username again.";

$l['unread_approval_count'] = "Moderator Notice: There is 1 username change awaiting approval.";
$l['unread_approval_counts'] = "Moderator Notice: There are {1} username changes awaiting approval.";

$l['mcp_nav_usernameapproval'] = "Username Approval";
$l['username_approval'] = "Username Change Approval";
$l['current_name'] = "Current Username";
$l['date'] = "Date";
$l['ipaddress'] = "IP Address";
$l['new_username'] = "New Username";
$l['no_usernames_awaiting_approval'] = "There are no username changes awaiting approval.";
$l['approve_changes'] = "Approve Changes";
$l['delete_changes'] = "Delete Changes";
$l['redirect_changes_approved'] = "The username changes have been approved successfully.<br />You will now be redirected to the username changes page.";
$l['redirect_changes_deleted'] = "The username changes have been deleted successfully.<br />You will now be redirected to the username changes page.";

$l['delete_history_confirm'] = "Are you sure you wish to delete this username history entry?";
$l['error_invalidhistory'] = "You have selected an invalid username history entry.";
$l['redirect_historydeleted'] = "The username history entry has been deleted successfully.";
