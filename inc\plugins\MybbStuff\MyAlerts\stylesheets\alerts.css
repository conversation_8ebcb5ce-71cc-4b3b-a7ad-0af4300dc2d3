.usercp_nav_myalerts {
	background: url(images/usercp/transmit_blue.png) no-repeat left center !important;
}

.usercp_nav_myalerts_delete_all {
	background: url(images/usercp/delete.png) no-repeat left center !important;
}

.usercp_nav_myalerts_delete_read {
	background: url(images/usercp/bin.png) no-repeat left center !important;
}

.newAlerts > a {
	color: red !important;
}

ul.panel_links a.myalerts {
	background-position: 0 -180px;
}

.alert-row__no-alerts td {
	text-align: center;
}

.alert--read.alert {
	opacity: .5;
}

.alert.alert--read td.trow1 {
	background-color: #F5F5F5;
}

.alerts--new a {
	color: red !important;
	font-weight: bold;
}

#myalerts_alerts_modal {
	width: 460px;
}

#myalerts_modal_right_btns {
	margin-top: 4px;
}
