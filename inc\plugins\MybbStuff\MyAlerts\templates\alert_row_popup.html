<tr class="alert {$alert['alert_status']} alert--{$alert['alert_code']}"
    id="alert_row_popup_{$alert['id']}">
    <td class="{$altbg} align-center alert__avatar" align="center">
        <a class="avatar" href="{$alert['from_user_raw_profilelink']}"><img
                src="{$alert['avatar']['image']}"
                alt="{$alert['username']}'s avatar"
                title="{$alert['username']}"
                {$alert['avatar']['width_height']}/></a>
    </td>
    <td class="{$altbg} alert__content">
        <a href="{$mybb->settings['bburl']}/alerts.php?action=view&amp;id={$alert['id']}">
            {$alert['message']}
        </a>
    </td>
    <td class="{$altbg} alert__time" align="center">
        {$alert['received_at']}<br />
        <a href="{$mybb->settings['bburl']}/alerts.php?action=markread&amp;id={$alert['id']}&amp;my_post_key={$mybb->post_code}" class="markReadAlertButton{$markReadHiddenClass}" id="popup_markread_alert_{$alert['id']}" title="{$lang->myalerts_modal_row_read_title}">{$lang->myalerts_modal_row_read}</a><a href="{$mybb->settings['bburl']}/alerts.php?action=markunread&amp;id={$alert['id']}&amp;my_post_key={$mybb->post_code}" class="markUnreadAlertButton{$markUnreadHiddenClass}" id="popup_markunread_alert_{$alert['id']}" title="{$lang->myalerts_modal_row_unread_title}">{$lang->myalerts_modal_row_unread}</a> | 
        <a href="{$mybb->settings['bburl']}/alerts.php?action=delete&amp;id={$alert['id']}&amp;my_post_key={$mybb->post_code}" class="deleteAlertButton" id="delete_alert_{$alert['id']}" title="{$lang->myalerts_modal_row_delete_title}">{$lang->myalerts_modal_row_delete}</a>
    </td>
</tr>
