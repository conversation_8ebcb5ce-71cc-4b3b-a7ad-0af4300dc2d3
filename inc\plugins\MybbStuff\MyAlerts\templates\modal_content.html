<div id="myalerts_alerts_modal" style="display: none;" class="modal">
    <table class="tborder" cellspacing="{$theme['borderwidth']}" cellpadding="{$theme['tablespace']}" border="0">
        <thead>
        <tr>
            <th class="thead" colspan="3">
                <strong>{$lang->myalerts_modal_title}</strong>
            </th>
        </tr>
        </thead>
        <tbody id="alerts_content">
        {$alerts}
        </tbody>
        <tfoot>
        <tr>
            <td class="tfoot smalltext" colspan="3">
                <a href="{$mybb->settings['bburl']}/alerts.php">{$lang->myalerts_modal_display_alerts}</a>
                <input type="checkbox" id="unreadOnlyCheckbox" name="unreadOnlyCheckbox" value="1"{$unreadOnlyChecked} /> <label for="unreadOnlyCheckbox" style="cursor:pointer;">{$lang->myalerts_modal_unread_only}</label>
                <span class="float_right" id="myalerts_modal_right_btns">
                    <a class="markAllReadButton" href="{$mybb->settings['bburl']}/alerts.php?action=mark_all_read&amp;my_post_key={$mybb->post_code}&amp;ret_link={$myalerts_return_link}">{$lang->myalerts_modal_mark_all_read}</a> | 
                    <a href="{$mybb->settings['bburl']}/alerts.php?action=delete_read&amp;my_post_key={$mybb->post_code}&amp;ret_link={$myalerts_return_link}"
                       onclick="return confirm('{$lang->myalerts_modal_delete_read_confirm}'); return false;">{$lang->myalerts_modal_delete_read}</a> | 
                    <a href="{$mybb->settings['bburl']}/alerts.php?action=delete_all&amp;my_post_key={$mybb->post_code}&amp;ret_link={$myalerts_return_link}"
                       onclick="return confirm('{$lang->myalerts_modal_delete_all_confirm}'); return false;">{$lang->myalerts_modal_delete_all}</a>
                </span>
                <br class="clear"/>
                <!-- Clear alerts link goes here... -->
            </td>
        </tr>
        </tfoot>
    </table>
</div>
