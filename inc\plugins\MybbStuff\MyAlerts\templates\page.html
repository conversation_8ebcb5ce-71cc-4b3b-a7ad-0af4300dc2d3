<html>
<head>
    <title>{$lang->myalerts_page_title} - {$mybb->settings['bbname']}</title>
    <script type="text/javascript">
        <!--
        var myalerts_autorefresh = {$mybb->settings['myalerts_autorefresh']};
        var page = {$page};
        var pages = {$pages};
        // -->
    </script>
    {$headerinclude}
</head>
<body>
{$header}
<table width="100%" border="0" align="center">
    <tr>
        {$usercpnav}
        <td valign="top">
            <div class="float_right">
                {$multipage}
            </div>
            <div class="clear"></div>
            <table border="0" cellspacing="{$theme['borderwidth']}"
                   cellpadding="{$theme['tablespace']}"
                   class="tborder">
                <thead>
                <tr>
                    <th class="thead" colspan="3">
                        <strong>{$lang->myalerts_page_title}</strong>

                        <div class="float_right">
                            <a href="{$mybb->settings['bburl']}/alerts.php?action=mark_all_read&amp;my_post_key={$mybb->post_code}" onclick="return confirm('{$lang->myalerts_mark_all_read_confirm}')">{$lang->myalerts_page_mark_all_read}</a> |
                            <a href="{$mybb->settings['bburl']}/alerts.php?action=delete_read&amp;my_post_key={$mybb->post_code}" onclick="return confirm('{$lang->myalerts_delete_read_confirm}')">{$lang->myalerts_page_delete_read}</a> | 
                            <a href="{$mybb->settings['bburl']}/alerts.php?action=delete_all&amp;my_post_key={$mybb->post_code}" onclick="return confirm('{$lang->myalerts_delete_all_confirm}')">{$lang->myalerts_page_delete_all}</a> | 
                            <a id="getLatestAlerts"
                               href="{$mybb->settings['bburl']}/alerts.php">{$lang->myalerts_page_getnew}</a>
                        </div>
                    </th>
                </tr>
                </thead>
                <tbody id="latestAlertsListing">
                {$alertsListing}
                </tbody>
            </table>
            <div class="float_right">
                {$multipage}
            </div>
            <br class="clear"/>
        </td>
    </tr>
</table>
{$footer}
</body>
</html>
