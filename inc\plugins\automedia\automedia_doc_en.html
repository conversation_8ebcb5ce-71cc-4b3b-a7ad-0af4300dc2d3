<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta name="generator" content="PSPad editor, www.pspad.com">
  <title>AutoMedia 4 Plugin for MyBB 1.8</title>
<style type="text/css">
<!--
 /* Font Definitions */
@font-face
	{font-family:Wingdings;
	panose-1:5 0 0 0 0 0 0 0 0 0;
	mso-font-charset:2;
	mso-generic-font-family:auto;
	mso-font-pitch:variable;
	mso-font-signature:0 268435456 0 0 -2147483648 0;}
 /* Style Definitions */
p.<PERSON>, li.<PERSON>, div.<PERSON>o<PERSON>orm<PERSON>
	{mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:Arial;
	mso-fareast-font-family:"Times New Roman";
	mso-bidi-font-family:"Times New Roman";}
h1
	{mso-style-next:Normal;
	margin-top:12.0pt;
	margin-right:0cm;
	margin-bottom:3.0pt;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:1;
	font-size:18.0pt;
	mso-bidi-font-size:16.0pt;
	font-family:Arial;
	mso-bidi-font-family:"Times New Roman";
	mso-font-kerning:16.0pt;}
h2
	{mso-style-next:Normal;
	margin-top:12.0pt;
	margin-right:0cm;
	margin-bottom:3.0pt;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:2;
	font-size:12.0pt;
	mso-bidi-font-size:14.0pt;
	font-family:Arial;
	mso-bidi-font-family:"Times New Roman";
	font-style:italic;}
h3
	{mso-style-next:Normal;
	margin-top:12.0pt;
	margin-right:0cm;
	margin-bottom:3.0pt;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:3;
	font-size:12.0pt;
	mso-bidi-font-size:13.0pt;
	font-family:Arial;
	mso-bidi-font-family:"Times New Roman";}
h4
	{mso-style-next:Normal;
	margin-top:12.0pt;
	margin-right:0cm;
	margin-bottom:3.0pt;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:4;
	font-size:14.0pt;
	font-family:Arial;
	mso-bidi-font-family:"Times New Roman";}
a:link, span.MsoHyperlink
	{color:blue;
	text-decoration:underline;
	text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
	{color:purple;
	text-decoration:underline;
	text-underline:single;}
p.Normal, li.Normal, div.Normal
	{mso-style-name:Normal;
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:Arial;
	mso-fareast-font-family:"Times New Roman";
	mso-bidi-font-family:"Times New Roman";}
@page Section1
	{size:595.45pt 841.7pt;
	margin:72.0pt 90.0pt 72.0pt 90.0pt;
	mso-header-margin:36.0pt;
	mso-footer-margin:36.0pt;
	mso-paper-source:0;}
div.Section1
	{page:Section1;}
 /* List Definitions */
@list l0
	{mso-list-id:708802764;
	mso-list-type:hybrid;
	mso-list-template-ids:524603758 67567617 67567619 67567621 67567617 67567619 67567621 67567617 67567619 67567621;}
@list l0:level1
	{mso-level-number-format:bullet;
	mso-level-text:\F0B7;
	mso-level-tab-stop:36.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l1
	{mso-list-id:1068265625;
	mso-list-type:hybrid;
	mso-list-template-ids:-1703537850 67567631 67567641 67567643 67567631 67567641 67567643 67567631 67567641 67567643;}
@list l1:level1
	{mso-level-tab-stop:36.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level2
	{mso-level-tab-stop:72.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level3
	{mso-level-tab-stop:108.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level4
	{mso-level-tab-stop:144.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level5
	{mso-level-tab-stop:180.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level6
	{mso-level-tab-stop:216.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level7
	{mso-level-tab-stop:252.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level8
	{mso-level-tab-stop:288.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l1:level9
	{mso-level-tab-stop:324.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2
	{mso-list-id:1907449403;
	mso-list-type:hybrid;
	mso-list-template-ids:-2073785086 67698689 67698691 67698693 67698689 67698691 67698693 67698689 67698691 67698693;}
@list l2:level1
	{mso-level-number-format:bullet;
	mso-level-text:\F0B7;
	mso-level-tab-stop:36.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l2:level2
	{mso-level-tab-stop:72.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level3
	{mso-level-tab-stop:108.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level4
	{mso-level-tab-stop:144.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level5
	{mso-level-tab-stop:180.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level6
	{mso-level-tab-stop:216.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level7
	{mso-level-tab-stop:252.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level8
	{mso-level-tab-stop:288.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
@list l2:level9
	{mso-level-tab-stop:324.0pt;
	mso-level-number-position:left;
	text-indent:-18.0pt;}
ol
	{margin-bottom:0cm;}
ul
	{margin-bottom:0cm;}
-->
</style>
</head>

<body link=blue vlink=purple style='tab-interval:36.0pt'>
<div style="margin-left: 50px;">
<div class=Section1>
<a name="_top"></a>
<h1>Automedia 4 Plugin for MyBB 1.8</h1>

<h2>Documentation</h2>

<p class=Normal></p>

<p class=Normal><b>Author:</b> doylecc ( <a href="http://community.mybb.com/user-14694.html" target="_blank" rel="noopener">http://community.mybb.com/user-14694.html</a>)</p>

<p class=Normal><b>Version:</b> 4.1.1 (02-28-2019)</p>

<p class=Normal><b>Homepage:</b> <a href="http://doylecc.altervista.org" target="_blank" rel="noopener">http://doylecc.altervista.org</a></p>

<p class=Normal><b>Contact:</b> <a href="http://community.mybb.com/user-14694.html" target="_blank" rel="noopener">http://community.mybb.com</a></p>

<p class=Normal></p>

<p class=Normal></p>

<h3>Content</h3>

<ul style='margin-top:0cm' type=disc>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_How_do_I">Plugin Description</a></li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_Where_can_I">Installation</a> </li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_Why_doesnt_?">Update</a></li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_Globale_Einstellungen">Global Settings</a></li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_When_is_?">User-CP</a></li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_MyCode-Buttons">MyCode-Buttons</a></li>
 <li class=MsoNormal style='mso-list:l2 level1 lfo3;tab-stops:list 36.0pt'><a
     href="#_Lizenz">License</a></li>
</ul>

<p class=MsoNormal><br></p>

<h3><a name="_How_do_I"></a></h3>

<h3>Plugin Description</h3>

<p class=MsoNormal>The AutoMedia Plugin embeds automatically video/audio/flash without need for MyCode tags.
The plugin recognizes the posted URL and shows the videos/audios. You don't need to insert any MyCode. The plugin utilizes the Embed.ly or Urlembed.com service.</p>

<p class=MsoNormal><b>Feature Overview:</b></p>

<p class=MsoNormal>-&gt;Can be enabled/disabled in ACP</p>

<p class=MsoNormal>-&gt;Template edits are applied automatically if one day a new theme is imported or added.<br>
Additionally, template edits can be reapplied from Plugins overview in ACP (e.g. after reverting your templates) without deactivating the plugin.</p>

<p class=MsoNormal>-&gt;Can be disabled for guests only</p>

<p class=MsoNormal>-&gt;Can be disabled for certain usergroups</p>

<p class=MsoNormal>-&gt;Can be enabled for certain forums only</p>

<p class=MsoNormal>-&gt;Embedding of individual links can be disabled by using MyCode tags [amoff]URL[/amoff].</p>

<p class=MsoNormal>-&gt;By using the [amplist][source]URL1.mp3[/source][source]URL2.mp3[/source][source]URL3.mp3[/source][/amplist] MyCode tags
MP3 playlists can be created.</p>

<p class=MsoNormal>-&gt;Embedding can be disabled for signatures (default)</p>

<p class=MsoNormal>-&gt;Embedding can be disabled in quotes</p>

<p class=MsoNormal>-&gt;Codebuttons for MyCode can be displayed in editor below textbox.</p>

<p class=MsoNormal>-&gt;Every user can enable/disable embedding in User CP.</p>

<p class=MsoNormal>-&gt;Embeds videos and music from dozens of different websites.</p>

<p class=MsoNormal>-&gt;Documentation available from plugin overview in ACP.</p>

<p class=MsoNormal>-&gt;Embedding of adult video sites can be enabled/disabled.</p>

<p class=MsoNormal>-&gt;Embedding of adult video sites can be enabled/disabled for guests only.</p>

<p class=MsoNormal>-&gt;Permissions for adult videos can be set by usergroups and forums.</p>

<p class=MsoNormal><b>List of supported file types and websites:</b></p>

<p class=MsoNormal>The oEmbed API supports many websites (embed.ly about 400 ans urlembed.com over 5000). See <a href="https://urlembed.com/provider" title="urlembed" target="_blank" rel="noopener">urlembed.com Providers list</a> and <a href="http://embed.ly/providers" title="embed.ly" target="_blank" rel="noopener">embed.ly Providers List</a> for more details.</p>

<p class=MsoNormal>Additionally the included Mediaelement HTML5 player, Flowplayer and MP3 player let you embed different file types. e.g.:</p>

<p class=MsoNormal><br></p>

<p class=MsoNormal># MP3/M4A/WAV/OGG audio and MP3 Playlist</p>

<p class=MsoNormal># MP4/M4V/MP4V/OGV/WEBM video</p>

<p class=MsoNormal># FLV Flash video</p>

<p class=MsoNormal># SWF Flash</p>

<p class=MsoNormal># DivX video</p>

<p class=MsoNormal># AVI video</p>

<p class=MsoNormal># MKV video</p>

<p class=MsoNormal># MOV Quicktime video</p>

<p class=MsoNormal># RM/RAM/SMIL/RV/RPM Real media</p>

<p class=MsoNormal># WMV/WMA Windows media</p>

<p class=MsoNormal># MPG/MPEG video</p>

<p class=MsoNormal><br></p>

<p class=MsoNormal>Please note that you need to have all necessary plugins/addons installed in your webbrowser to play all media file types!<br></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal># 14 different adult video websites are currently supported (deactivated by default)</p>

<p class=MsoNormal><br><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_Where_can_I"></a><a name="_Installation"></a></h3>

<h3>Installation</h3>

<ol>
<li class=MsoNormal>Upload the complete content of the Upload folder into your forum home directory.<br>
(For additional languages you have to upload the language files too.)</li>

<li class=MsoNormal>Go to your &quot;AdminCP Configuration - Plugins&quot; and click &quot;Install &amp; Activate&quot; behind AutoMedia</li>

<li class=MsoNormal>After the activation you'll find a new settinggroup &quot;AutoMedia Global&quot; in &quot;ACP - Configuration - Settings&quot;.<br>
Here you can generally enable/disable embedding without losing your settings.<br>

You can disable embedding for guests only.<br>

You can disable embedding for different usergroup(s).<br>

Embedding can be enabled for certain forums only.<br>

Embedding of adult videos can be enabled (disabled by default).<br>

Embedding of adult videos can be disabled for guests only.<br>

Permissions for adult videos can be set by usergroups and forums.<br>

Embedding can be disabled in signatures (default).<br>

Width and height of embedded media can be set.<br>

Embed.ly or urlembed.com service can be used.<br>

Embedding of attachments can be activated.<br>

Codebuttons can be enabled/disabled for MyCode.<br>

Embedding can be disabled in quoted posts.</li>

<li class=MsoNormal>Adds an own admin permission.</li>

<li class=MsoNormal>In &quot;ACP - Tools &amp; Maintenance&quot; a new menu item &quot;AutoMedia&quot; is added, where you can view and manage the installed modules for all supported sites and file types.<br>

You can enable/disable embedding for every single mediasite and file type. (all are enabled by default)</li>

<li class=MsoNormal>The plugin adds a new menu item &quot;AutoMedia&quot; in &quot;User CP&quot;. In &quot;AutoMedia&quot; a select box is shown. Every user can set whether he wants embedding enabled (Yes (default)) or disabled (No).
If he chooses "No" he will only see the links in posts instead the embedded videos/audios and in his own posts only links will be shown to all users.</li>

<li class=MsoNormal>All done.</li>
</ol>
<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_Why_doesnt_?"></a></h3>

<h3>Update</h3>

<p class=MsoNormal>Update from version 3.*:</p>
<ol>
<li class=MsoNormal>Upload the complete content of the Upload folder into your forum home directory and overwrite the existing files.</li>

<li class=MsoNormal>In ACP -> Configuration -> Plugins deactivate AutoMedia and activate it again.</li>

<li class=MsoNormal>In ACP -> Templates & Style -> Templates -> run Find Updated Templates. If there are any automedia templates listed, revert them (and possibly reinsert your own code.)</li>

<li class=MsoNormal>All done.</li>
</ol>
<p class=MsoNormal><br></p>

<p class=MsoNormal>Update from all older versions:</p>
<ol>
<li class=MsoNormal>Deinstall the old AutoMedia plugin.</li>

<li class=MsoNormal>Upload the complete content of the Upload folder into your forum home directory and overwrite the existing files.<br>
(You can delete the old &quot;mediaplayer&quot; folder in the forum home directory.)<br>
For additional languages you have to upload new language files too.</li>

<li class=MsoNormal>see &quot;Installation&quot; beginning with #2</li>
</ol>
<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_Who_is_?"></a><a name="_Globale_Einstellungen"></a></h3>

<h3>Global Settings</h3>

<p class=MsoNormal>In &quot;ACP - Configuration - AutoMedia Global&quot; you can see the following 17 settings:</p>

<p class=MsoNormal><br></p>

<ol style='margin-top:0cm' start=1 type=1>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Enable AutoMedia for AutoMedia Sites? - Here you can completely activate/deactivate the automatic embedding.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Enable AutoMedia
     for Guests? - Here you can disable the automatic embedding for Guests.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Disallow AutoMedia
     for the following Usergroups: - Here you can disable the automatic embedding for certain usergroups (id's separated by comma).</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Show AutoMedia
     in following Forums only: - Here you can insert the id&#39;s of the forums (id&#39;s separated by comma, 0 = all forums) where automatic embedding shall be activated.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Enable Embedding of Adult Sites? - Here you can enable/disable the automatic embedding of adult site videos (special modules).
     (deactivated by default) - Notice: Adult Sites (special) modules in Module Management are only visible if embedding of adult sites  is enabled.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Enable Adult Site Videos for Guests? - Here you can disable the automatic embedding of adult site videos for guests.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Allow Adult Site Videos for the following Usergroups: - Here you can enable the automatic embedding of adult site videos for certain usergroups (id's separated by comma).</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Allow Adult Site Videos for the following Forums only: - Here you can insert the id&#39;s of the forums (id&#39;s separated by comma, 0 = all forums) where automatic embedding of adult site videos shall be activated.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Enable AutoMedia for &quot;AutoMedia Sites&quot; in Signatures? - Here you can enable/disable the automatic embedding in signatures (disabled by default.)</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Max. width of
     shown media files. - Here you can set the width (in pixels) for the embedded files.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Max. height of
     shown files. - Here you can set the height (in pixels) for the embedded files.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Use Embed.ly or urlembed.com service.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Embed attachments, if they are inserted into a post</li>
<li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Show Codebuttons
     for MP3 Playlist and Deactivation MyCodes - if set to &quot;Yes&quot; the codebuttons for inserting the [amoff]-MyCode (disabling the embedding for individual links) and the [amplist]-MyCode (for creating a MP3 playlist) are displayed for the users below the editors message text box.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>AutoMedia in quoted posts? - Here you can enable/disable the automatic embedding when quoting posts.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Display attachment links. - Here you can enable/disable the display of the download links of embedded attachments.</li>
 <li class=MsoNormal style='mso-list:l1 level1 lfo6;tab-stops:list 36.0pt'>Embed local links? - Here you can enable/disable the automatic embedding of local forum links.</li>
</ol>

<p class=MsoNormal><br></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_When_is_?"></a></h3>

<h3>User-CP</h3>

<p class=MsoNormal>In User-CP you'll find the menu item &quot;AutoMedia.&quot; Here the user can enable/disable the automatic embedding.
On the right part of the table a status icon is shown. </p>

<p class=MsoNormal>Thumbs up = automatic embedding is enabled in the users personal settings.<br>
Thumbs down = automatic embedding is disabled in the users personal settings.
</p>

<p class=MsoNormal>The center of the table shows a select box with following options: &quot;YES&quot; (enable embedding) and &quot;NO&quot; (disable embedding).</p>

<p class=MsoNormal>The setting is saved after clicking the &quot;Ok&quot; button. </p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_MyCode-Buttons"></a></h3>

<h3>MyCode-Buttons</h3>

<p class=MsoNormal>If the codebuttons for inserting the MyCodes are enabled in the global plugin settings, the buttons are displayed below the editors message text box.</p>

<p class=MsoNormal>With a click on the respective button the MyCode tags are inserted (opening and closing tag simultaneously.)</p>

<p class=MsoNormal>The tags are inserted at the cursor point or - if text is selected - before and after the selected text.</p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<h3><a name="_Lizenz"></a></h3>

<h3>License</h3>

<p class=MsoNormal>This program is free software: </p>

<p class=MsoNormal>you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version. </p>

<p class=MsoNormal>This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details..</p>

<p class=MsoNormal><br></p>

<p class=MsoNormal> You should have received a copy of the GNU General Public License
    along with this program.  If not, see &lt;<a href="http://www.gnu.org/licenses/"
target="_blank" rel="noopener">http://www.gnu.org/licenses/</a>&gt;.<br><br></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><a href="#_top">Back to top</a></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal><br></p>

<p class=MsoNormal>Last edited: 02-28-2019</p>

</div>
</div>
</body>

</html>
