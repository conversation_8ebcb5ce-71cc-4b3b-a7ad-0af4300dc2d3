<?php

require_once MYBB_ROOT . "inc/functions_user.php";

function array_keyby($key, $array)
{
    $newArr = [];

    foreach ($array as $item) {
        $newArr[$item[$key]] = $item;
    }

    return $newArr;
}

function array_groupby($key, $array)
{
    $newArr = [];

    foreach ($array as $item) {
        if (is_callable($key)) {
            $_key = $key($item);
        } else {
            $_key = $item[$key];
        }

        $newArr[$_key][] = $item;
    }

    return $newArr;
}

function toSteamID64($steamid2)
{
    if (preg_match('/^STEAM_/', $steamid2)) {
        $parts = explode(':', $steamid2);
        return bcadd(bcadd(bcmul($parts[2], '2'), '76561197960265728'), $parts[1]);
    } elseif (is_numeric($steamid2) && strlen($steamid2) < 16) {
        return bcadd($steamid2, '76561197960265728');
    } else {
        return $steamid2; // We have no idea what this is, so just return it.
    }
}

function toSteamID2($steamid64)
{
    $steamid2 = bcsub($steamid64, '76561197960265728');

    return 'STEAM_0:' . bcmod($steamid2, '2') . ':' . bcdiv($steamid2, 2);
}

function timeDiffHuman($timeInterval, $attributes = [], $newLine = false)
{
    $str = '';

    if ($timeInterval->y > 0 && ($attributes['years'] ?? true)) {
        $str .= '%y years ';
    }

    if ($timeInterval->m > 0 && ($attributes['months'] ?? true)) {
        $str .= '%m months ';
    }

    if ($timeInterval->d > 0 && ($attributes['days'] ?? true)) {
        $str .= '%d days ';
    }

    if ($timeInterval->h > 0 && ($attributes['hours'] ?? true)) {
        $str .= '%h hours ';
    }

    if ($timeInterval->i > 0 && ($attributes['minutes'] ?? true)) {
        $str .= '%i minutes ';
    }

    if ($timeInterval->s > 0 && ($attributes['seconds'] ?? true)) {
        $str .= '%s seconds';
    }

    return trim($timeInterval->format($str));
}

function http_query($array1, $array2)
{
    return http_build_query(array_merge($array1, $array2));
}

function encrypt($ClearTextData)
{
    $EncryptionKey = base64_decode($env['APP_KEY']);
    $InitializationVector  = openssl_random_pseudo_bytes(openssl_cipher_iv_length('AES-256-CBC'));
    $EncryptedText = openssl_encrypt($ClearTextData, 'AES-256-CBC', $EncryptionKey, 0, $InitializationVector);
    return base64_encode($EncryptedText . '::' . $InitializationVector);
}

function decrypt($CipherData)
{
    $EncryptionKey = base64_decode($env['APP_KEY']);
    list($Encrypted_Data, $InitializationVector) = array_pad(explode('::', base64_decode($CipherData), 2), 2, null);
    return openssl_decrypt($Encrypted_Data, 'AES-256-CBC', $EncryptionKey, 0, $InitializationVector);
}

function mybb_login($user)
{
    global $session, $db;

    $has_session = $db->query('SELECT * FROM mybb_sessions WHERE uid = ' . $user['uid']);
    if ($has_session->num_rows > 0) {
        $sid = $has_session->fetch_field('sid');
    } else {
        $key = generate_loginkey(50);

        $db->update_query('sessions', ['uid' => $user['uid']], 'sid = \'' . $session->sid . '\'');
        $db->update_query('users', ['loginkey' => $key], 'uid = \'' . $user['uid'] . '\'');
    }

    my_setcookie('sid', $sid ?? $session->sid, -1, true);
    my_setcookie('mybbuser', $user['uid'] . '_' . ($key ?? $user['loginkey']), $remember, true, 'lax');
}

function render_mybb_template($slug, $full = false)
{
    foreach (array_keys($GLOBALS) as $global) {
        eval('global $' . $global . ';');
    }

    ob_start();
    if ($full) {
        eval("\$page= \"" . $templates->get('headerinclude') . "\";");
        echo $page;
        eval("\$page= \"" . $templates->get('header') . "\";");
        echo $page;
    }

    eval("\$page= \"" . $templates->get($slug) . "\";");
    echo $page;
    return ob_get_clean();
}

function ngrok()
{
    global $mybb, $env;

    if ($env['APP_ENV'] == 'local') {
        exec('curl --silent --show-error http://fearlessrp.local/ngrok/api/tunnels | sed -nE \'s/.*public_url":"https:..([^"]*).*/\1/p\'', $output);

        if (!$output) {
            return $mybb->settings['bburl'];
        }

        return $output[0];
    }

    return $mybb->settings['bburl'];
}
