<?php

//Ammo
$items['ammo_sniper'] = array ('Ammunition', 'Sniper Ammo' , 1, 350, 'ammo_sniper');
$items['ammo_rifle'] = array ('Ammunition', 'Rifle Ammo' , 1, 250, 'ammo_rifle');
$items['ammo_darts'] = array ('Ammunition', 'Darts' , 1, 600, 'ammo_darts');
$items['ammo_pistol'] = array ('Ammunition', 'Pistol Ammo' , 1, 200, 'ammo_pistol');
$items['ammo_shotgun'] = array ('Ammunition', 'Shotgun Ammo' , 1, 300, 'ammo_shotgun');

//Goverment
$items['dodgechargerpolicecar'] = array ('Government Vehicles', 'Dodge Charger Pursuit Vehicle' , 1, 100000, 'dodgechargerpolicecar');
$items['chevroletunmarked'] = array ('Government Vehicles', 'Chevrolet Suburban' , 1, 200000, 'chevroletunmarked');
$items['chevroletpolicecar'] = array ('Government Vehicles', 'Chevrolet Suburban' , 1, 100000, 'chevroletpolicecar');
$items['Firetruck'] = array ('Government Vehicles', 'Fire Engine' , 0, 1000, 'Firetruck');
$items['Ambulance'] = array ('Government Vehicles', 'Ambulance' , 2, 1000, 'Ambulance');
$items['crownvicpolicecar'] = array ('Government Vehicles', 'Crown Victoria Police Cruiser' , 0, 1000, 'crownvicpolicecar');
$items['swatvan'] = array ('Government Vehicles', 'S.R.U. Van' , 0, 100000, 'swatvan');

//Vehicle Maintenance
$items['toolbox'] = array ('Vehicle Maintenance', 'Toolbox' , 2, 1000, 'toolbox');
$items['petrol'] = array ('Vehicle Maintenance', 'Petrol Canister' , 2, 1000, 'petrol');
$items['engine'] = array ('Vehicle Maintenance', 'Engine' , 10, 500, 'engine');
$items['wheel'] = array ('Vehicle Maintenance', 'Wheel' , 4, 100, 'wheel');

//Vehicles
$items['focus'] = array ('Vehicles', 'Ford Focus SVT' , 0, 47500, 'focus');
$items['nissan'] = array ('Vehicles', 'Nissan Skyline GTR R32' , 0, 190000, 'nissan');
$items['rv'] = array ('Vehicles', 'RV 1980s' , 0, 190000, 'rv');
$items['porsche'] = array ('Vehicles', 'Porsche 918 Spyder' , 0, 380000, 'porsche');
$items['chrysler300c'] = array ('Vehicles', 'Chrysler 300C' , 0, 155000, 'chrysler300c');
$items['rroyce'] = array ('Vehicles', 'Rolls-Royce Ghost' , 0, 600000, 'rroyce');
$items['rollsroycesilvercloud'] = array ('Vehicles', 'Rolls-Royce Silver Cloud', 0, 480000, 'rollsroycesilvercloud');
$items['mustang'] = array ('Vehicles', 'Ford Mustang Boss 429' , 0, 160000, 'mustang');
$items['mercedesc32'] = array ('Vehicles', 'Mercedes C32 AMG' , 0, 70000, 'mercedesc32');
$items['spider'] = array ('Vehicles', 'Ferrari 458 Spider' , 0, 350000, 'spider');
$items['volvos60'] = array ('Vehicles', 'Volvo S60R' , 0, 180000, 'volvos60');
$items['bentley'] = array ('Vehicles', 'Bentley PM Continental GT' , 0, 400000, 'bentley');
$items['supra'] = array ('Vehicles', 'Toyota Supra' , 0, 190000, 'supra');
$items['transit'] = array ('Vehicles', 'Ford Transit' , 0, 90000, 'transit');
$items['astonv12'] = array ('Vehicles', 'Aston V12 Vantage' , 0, 200000, 'astonv12');
$items['landrover'] = array ('Vehicles', 'Range Rover 08' , 0, 220000, 'landrover');
$items['mclaren'] = array ('Vehicles', 'McLaren P1' , 0, 1000000, 'mclaren');
$items['bmwgtr'] = array ('Vehicles', 'BMW E46 M3 GTR' , 0, 1000000, 'bmwgtr');
$items['bmwgtr_legacy'] = array ('Vehicles', 'BMW E46 M3 GTR - Legacy' , 0, 1000000, 'bmwgtr_legacy');
$items['Minivan'] = array ('Vehicles', 'VW Golf MKII' , 0, 25000, 'Minivan');
$items['dodgecharge'] = array ('Vehicles', 'Dodge Charger' , 1, 250000, 'dodgecharge');
$items['murcielago'] = array ('Vehicles', 'Murcielago' , 0, 400000, 'murcielago');
$items['ford1940'] = array ('Vehicles', 'Ford Deluxe Coupe 1940' , 0, 100000, 'ford1940');
$items['chevroletsuburban'] = array ('Vehicles', 'Chevrolet Suburban' , 1, 200000, 'chevroletsuburban');
$items['Audi'] = array ('Vehicles', 'Audi RS4 Avant' , 0, 198000, 'Audi');
$items['Trabbi'] = array ('Vehicles', 'Trabant' , 0, 15000, 'Trabbi');
$items['Smart'] = array ('Vehicles', 'Smart ForTwo' , 0, 45000, 'Smart');
$items['ferrari250gt'] = array ('Vehicles', 'Ferrari 250GT' , 0, 380000, 'ferrari250gt');
$items['raptor'] = array ('Vehicles', 'Ford Raptor' , 0, 160000, 'raptor');
$items['beetle'] = array ('Vehicles', 'Volkswagen New Beetle Convertible' , 0, 32000, 'beetle');
$items['minicoopers11'] = array ('Vehicles', 'Mini Cooper S 11' , 0, 50000, 'minicoopers11');
$items['Mercedes'] = array ('Vehicles', 'Mercedes-Benz G65' , 0, 300000, 'Mercedes');
$items['volvo850'] = array ('Vehicles', 'Volvo 850 R' , 0, 35000, 'volvo850');
$items['mitsubishi'] = array ('Vehicles', 'Mitsubishi Lancer Evolution X GSR' , 0, 195000, 'mitsubishi');
$items['mercedesblack'] = array ('Vehicles', 'Mercedes-Benz C63 AMG Black' , 0, 420000, 'mercedesblack');

// Electric Vehicles
$items['nissanleaf'] = array ('Vehicles', 'Nissan Leaf' , 0, 95000, 'nissanleaf');
$items['teslamodelx'] = array ('Vehicles', 'Tesla Model X' , 0, 285000, 'teslamodelx');
$items['teslamodels'] = array ('Vehicles', 'Tesla Model S' , 0, 650000, 'teslamodels');

// Public Transport
$items['Cabbie'] = array ("Vehicles", "Cabbie", 0, 1000, 'Cabbie');
$items['Bus'] = array ("Vehicles", "Bus", 0, 1000, 'Bus');

//Food
$items['Goldfish'] = array ('Food', 'Goldfish' , 1, 300, 'Goldfish');
$items['Shoe'] = array ('Food', 'Mr. Breen\'s lost shoe' , 1, 210, 'Shoe');
$items['candycane'] = array ('Food', 'Candy cane' , 1, 100, 'candycane');
$items['waterbottle'] = array ('Food', 'Water Bottle' , 1, 90);
$items['beans'] = array ('Food', 'Beans' , 1, 90, 'beans');
$items['Bruger'] = array ('Food', 'Burger' , 1, 90, 'Bruger');
$items['chinese'] = array ('Food', 'Chinese Food' , 1, 250, 'chinese');
$items['christmasdinner'] = array ('Food', 'Christmas Dinner' , 1, 600, 'christmasdinner');
$items['live_worm'] = array ('Food', 'Live worm' , 0.2, 10, 'live_worm');
$items['scotch'] = array ('Food', 'Scotch Whisky' , 1, 150, 'scotch');
$items['cityrp_rod'] = array ('Food', 'Fishing Rod' , 2, 2000, 'cityrp_rod');
$items['Coffee'] = array ('Food', 'Coffee' , 1, 220, 'Coffee');
$items['cola'] = array ('Food', 'Orange-Lemonade' , 1, 200, 'cola');
$items['watermelon'] = array ('Food', 'Watermelon' , 1, 210, 'watermelon');
$items['Carrot'] = array ('Food', 'Carrot' , 1, 90, 'Carrot');
$items['AppleCider'] = array ('Food', 'Apple Cider' , 1, 225, 'AppleCider');
$items['milk'] = array ('Food', 'Milk' , 1, 210, 'milk');
$items['Fish'] = array ('Food', 'Fish' , 1, 250, 'Fish');
$items['eggnog'] = array ('Food', 'Eggnog' , 1, 110, 'eggnog');
$items['Tea'] = array ('Food', 'Tea' , 1, 220, 'Tea');
$items['lemonade'] = array ('Food', 'Pear-Lemonade' , 1, 200, 'lemonade');
$items['alcohol'] = array ('Food', 'Alcoholic Beverage' , 1, 90, 'alcohol');
$items['heartchoco'] = array ('Food', 'Box of chocolates' , 1, 500, 'heartchoco');

//Packaging
$items['big_package'] = array ('Packaging', 'Big Locker' , 1, 1500, 'big_package');
$items['package'] = array ('Packaging', 'Big Package' , 1, 1000, 'package');
$items['small_package'] = array ('Packaging', 'Small Package' , 1, 500, 'small_package');

//Misc
$items['doorremotecontrol'] = array ('Misc.', 'Door Remote Control' , 0, 100000, 'doorremotecontrol');
$items['boxed_pocket_medium'] = array ('Misc.', 'Medium Boxed Pocket' , 0, 5000, 'boxed_pocket_medium');
$items['Cruise'] = array ('Misc.', 'Cruise Control' , 0, 50000, 'Cruise');
$items['portableradio'] = array ('Misc.', 'Portable Radio' , 0, 25000, 'portableradio');
$items['boxed_pocket_small'] = array ('Misc.', 'Small Boxed Pocket' , 0, 2500, 'boxed_pocket_small');
$items['boxed_pocket_big'] = array ('Misc.', 'Big Boxed Pocket' , 0, 10000, 'boxed_pocket_big');
$items['carkeys'] = array ('Misc.', 'Remote Key' , 0, 25000, 'carkeys');
$items['Battery'] = array ('Misc.', 'Battery' , 2, 300, 'Battery');
$items['big_pocket'] = array ('Misc.', 'Big pocket' , -20, 10000, 'big_pocket');
$items['medium_pocket'] = array ('Misc.', 'Medium pocket' , -10, 5000, 'medium_pocket');
$items['small_pocket'] = array ('Misc.', 'Small Pocket' , -5, 2500, 'small_pocket');
$items['picture_bilboard'] = array ('Misc.', 'Billboard' , 0, 25000, 'picture_bilboard');
$items['picture_frame'] = array ('Misc.', 'Picture frame' , 0, 10000, 'picture_frame');
$items['picture_frameless'] = array ('Misc.', 'Frameless picture frame' , 0, 20000, 'picture_frameless');
$items['picture_frame_square'] = array ('Misc.', 'Square picture frame' , 0, 10000, 'picture_frame_square');
$items['picture_frame_table'] = array ('Misc.', 'Table picture frame' , 0, 5000, 'picture_frame_table');
$items['picture_frame_wide'] = array ('Misc.', 'Wide picture frame' , 0, 10000, 'picture_frame_wide');
$items['messageboard'] = array ('Misc.', 'Message Board' , 2, 8500, 'messageboard');
$items['cityrp_metal_detector'] = array ('Misc.', 'Metal Detector' , 0, 20000, 'cityrp_metal_detector');
$items['playeridentification'] = array ('Misc.', 'Identification' , 5, 10000, 'playeridentification');
$items['life_alert'] = array ('Misc.', 'Life Alert' , 5, 10000, 'life_alert');
$items['cityrp_ziptie'] = array ('Misc.', 'Ziptie' , 1, 2200, 'cityrp_ziptie');
$items['vehicle_tracker'] = array ('Misc.', 'Vehicle Tracker' , 5, 25000, 'vehicle_tracker');
$items['coffee_machine'] = array ('Misc.', 'Coffee Machine' , 5, 1000, 'coffee_machine');
$items['coffee_beans'] = array ('Misc.', 'Coffee Beans' , 1, 100, 'coffee_beans');
$items['coffee_can_empty'] = array ('Misc.', 'Coffee Can (Empty)' , 1, 200, 'coffee_can_empty');
$items['coffee_mug_empty'] = array ('Misc.', 'Coffee Mug (Empty)' , 1, 50, 'coffee_mug_empty');
$items['coffee_can'] = array ('Misc.', 'Coffee Can (Full)' , 1, 1000, 'coffee_can');

//Contraband
$items['money_printer'] = array ('Contraband', 'Money Printer' , 0, 1000, 'money_printer');
$items['drug_lab'] = array ('Contraband', 'Drug Lab' , 0, 750, 'drug_lab');

//Clothing
$items['odessa'] = array ('Clothing', 'Worn-out Security Outfit' , 1, 25000, 'odessa');
$items['suit5'] = array ('Clothing', 'Suit  \'Giovanni Cipriani \'' , 1, 75000, 'suit5');
$items['suit13'] = array ('Clothing', 'Dress  \'Georgio Armani \'' , 1, 100000, 'suit13');
$items['suit25'] = array ('Clothing', 'Dress  \'Bonfilia Calvina \'' , 1, 100000, 'suit25');
$items['scientist6'] = array ('Clothing', 'Laboratory Coat  \'Dr. Newton \'' , 1, 50000, 'scientist6');
$items['suit26'] = array ('Clothing', 'Outfit  \'Chell Catalano \'' , 1, 80000, 'suit26');
$items['mesa_scientist_female'] = array ('Clothing', 'Theoretical Physicist Female Coat' , 1, 150000, 'mesa_scientist_female');
$items['suit2'] = array ('Clothing', 'Suit  \'Vittorio Cipriani \'' , 1, 100000, 'suit2');
$items['suit6'] = array ('Clothing', 'Suit  \'Giancarlo Moretti \'' , 1, 100000, 'suit6');
$items['uniform_police'] = array ('Clothing', 'Stolen police uniform' , 1, 100000, 'uniform_police');
$items['suit24'] = array ('Clothing', 'Dress  \'Adriani Valentini \'' , 1, 100000, 'suit24');
$items['suit7'] = array ('Clothing', 'Suit  \'Carmine Cipriani \'' , 1, 100000, 'suit7');
$items['suit20'] = array ('Clothing', 'Dress  \'Eloisa Donatella \'' , 1, 100000, 'suit20');
$items['suit21'] = array ('Clothing', 'Dress  \'Giacinta Fiorenza \'' , 1, 100000, 'suit21');
$items['suit4'] = array ('Clothing', 'Suit \'Mario Piccolo \'' , 1, 100000, 'suit4');
$items['fireman_mask'] = array ('Clothing', 'Fireman Gasmask' , 1, 5000, 'fireman_mask');
$items['marlowe_hat'] = array ('Clothing', 'Marlowe\'s hat' , 1, 5000, 'marlowe_hat');
$items['mask'] = array ('Clothing', 'Gas mask' , 1, 0, 'mask');
$items['scientist3'] = array ('Clothing', 'Laboratory Coat \'Dr. Landry\'' , 1, 50000, 'scientist3');
$items['fireman_oxygen'] = array ('Clothing', 'Fireman Oxygen Cylinder' , 1, 3500, 'fireman_oxygen');
$items['scientist1'] = array ('Clothing', 'Laboratory Coat \'Dr. Marinko\'' , 1, 50000, 'scientist1');
$items['scientist10'] = array ('Clothing', 'Laboratory Coat \'Dr. Poitras\'' , 1, 50000, 'scientist10');
$items['suit14'] = array ('Clothing', 'Dress \'Germandotta Alessa\'' , 1, 100000, 'suit14');
$items['suit16'] = array ('Clothing', 'Dress \'Adelina Crocetta\'' , 1, 100000, 'suit16');
$items['suit15'] = array ('Clothing', 'Dress \'Florentina Cipriana\'' , 1, 100000, 'suit15');
$items['suit10'] = array ('Clothing', 'Suit \'Lucio Basso\'' , 1, 100000, 'suit10');
$items['scientist5'] = array ('Clothing', 'Laboratory Coat \'Dr. Coulson\'' , 1, 50000, 'scientist5');
$items['marlowe'] = array ('Clothing', '\'Philip Marlowe\'' , 1, 45000, 'marlowe');
$items['scientist7'] = array ('Clothing', 'Laboratory Coat \'Dr. Lemay\'' , 1, 50000, 'scientist7');
$items['uniform_prisoner'] = array ('Clothing', 'Stolen prisoner\'s uniform' , 1, 100000, 'uniform_prisoner');
$items['scientist8'] = array ('Clothing', 'Laboratory Coat \'Dr. Horah\'' , 1, 50000, 'scientist8');
$items['uniform_medic'] = array ('Clothing', 'Stolen paramedic\'s uniform' , 1, 100000, 'uniform_medic');
$items['luccio2'] = array ('Clothing', 'Luccio 2' , 1, 75000, 'luccio2');
$items['uniform_fireman'] = array ('Clothing', 'Stolen fireman\'s uniform' , 1, 100000, 'uniform_fireman');
$items['fedora'] = array ('Clothing', 'Fedora' , 1, 50000, 'fedora');
$items['uniform_security'] = array ('Clothing', 'Security Uniform' , 1, 50000, 'uniform_security');
$items['suit18'] = array ('Clothing', 'Dress \'Giosetta Diamante\'' , 1, 100000, 'suit18');
$items['suit11'] = array ('Clothing', 'Suit \'Adelmo Marchesi\'' , 1, 100000, 'suit11');
$items['suit19'] = array ('Clothing', 'Dress \'Beatrice Calvina\'' , 1, 100000, 'suit19');
$items['mesa_scientist_male'] = array ('Clothing', 'Theoretical Physicist Male Coat' , 1, 150000, 'mesa_scientist_male');
$items['boris_tracksuit'] = array ('Clothing', 'Gopnik tracksuit' , 1, 200000, 'boris_tracksuit');
$items['suit3'] = array ('Clothing', 'Suit \'Sal Milano\'' , 1, 100000, 'suit3');
$items['cityrp_briefcase'] = array ('Clothing', 'Briefcase' , 1, 25000, 'cityrp_briefcase');
$items['scientist2'] = array ('Clothing', 'Laboratory Coat \'Dr. Schuler\'' , 1, 50000, 'scientist2');
$items['gov_cap'] = array ('Clothing', 'Government cap' , 1, 5000, 'gov_cap');
$items['ninjasuit'] = array ('Clothing', 'Ninja Suit' , 1, 0, 'ninjasuit');
$items['suit1'] = array ('Clothing', 'Suit \'Giorgio Rossi\'' , 1, 100000, 'suit1');
$items['fireman_cap'] = array ('Clothing', 'Fireman Helmet' , 1, 2500, 'fireman_cap');
$items['heavyarmor'] = array ('Clothing', 'S.R.U. Heavy Armour Suit' , 1, 0, 'heavyarmor');
$items['suit8'] = array ('Clothing', 'Suit \'Enrico Bianco\'' , 1, 100000, 'suit8');
$items['suit9'] = array ('Clothing', 'Suit \'Dino Rossetto\'' , 1, 100000, 'suit9');
$items['suit12'] = array ('Clothing', 'Suit \'Fiorello Romani\'' , 1, 100000, 'suit12');
$items['suit17'] = array ('Clothing', 'Dress \'Enrica Concetta\'' , 1, 100000, 'suit17');
$items['scientist9'] = array ('Clothing', 'Laboratory Coat \'Dr. Einstein\'' , 1, 50000, 'scientist9');
$items['adminsuit'] = array ('Clothing', 'Private Suit with glasses' , 1, 0, 'adminsuit');
$items['suit23'] = array ('Clothing', 'Dress \'Enrica Dona\'' , 1, 100000, 'suit23');
$items['scientist4'] = array ('Clothing', 'Laboratory Coat \'Dr. Sicilia\'' , 1, 50000, 'scientist4');
$items['devsuit'] = array ('Clothing', 'Devsuit' , 1, 0, 'devsuit');
$items['conehat'] = array ('Clothing', 'Cone hat' , 1, 0, 'conehat');
$items['combatman1'] = array ('Clothing', 'Male Rebel Outfit' , 1, 25000, 'combatman1');
$items['combatwoman'] = array ('Clothing', 'Female Rebel Outfit' , 1, 25000, 'combatwoman');


//Pharmaceuticals
$items['paracetamol'] = array ('Pharmaceuticals', 'Paracetamol' , 1, 300, 'paracetamol');
$items['steroids'] = array ('Pharmaceuticals', 'Steroids' , 1, 100, 'steroids');
$items['nitrazepam'] = array ('Pharmaceuticals', 'Nitrazepam' , 1, 100, 'nitrazepam');
$items['health_vial'] = array ('Pharmaceuticals', 'Health Vial' , 1, 200, 'health_vial');
$items['health_kit'] = array ('Pharmaceuticals', 'Health Kit' , 2, 250, 'health_kit');
$items['cpr_kit'] = array ('Pharmaceuticals', 'CPR Kit' , 5, 25000, 'cpr_kit');

//Black Market
$items['Shroom'] = array ('Black Market', 'Shroom' , 1, 240, 'Shroom');
$items['Injection'] = array ('Black Market', 'Injection' , 2, 260, 'Injection');
$items['weed'] = array ('Black Market', 'Weed' , 1, 250, 'weed');
$items['cityrp_rope'] = array ('Black Market', 'Hostage Rope' , 1, 2500, 'cityrp_rope');
$items['cityrp_lockpick'] = array ('Black Market', 'Lockpick' , 1, 2500, 'cityrp_lockpick');
$items['cityrp_makeshiftjammer'] = array ('Black Market', 'Makeshift ECM jammer' , 1, 2500, 'cityrp_makeshiftjammer');
$items['seed'] = array ('Black Market', 'Weed Seed' , 1, 25, 'seed');
$items['Heroin'] = array ('Black Market', 'Heroin' , 1, 270, 'Heroin');
$items['breach'] = array ('Black Market', 'Breach' , 2, 500, 'breach');
$items['cityrp_blindfold'] = array ('Black Market', 'Blindfold' , 2, 4500, 'cityrp_blindfold');
$items['cocainee'] = array ('Black Market', 'Cocaine' , 1, 280, 'cocainee');
$items['cityrp_cracker'] = array ('Black Market', 'Keypad Cracker' , 2, 2000, 'cityrp_cracker');
$items['kevlar'] = array ('Black Market', 'Kevlar' , 2, 1500, 'kevlar');
$items['pot'] = array ('Black Market', 'Drugs Pot' , 1, 100, 'pot');

//Mixtures
$items['ammo_dartsschematics'] = array ('Mixtures', 'Darts Schematics' , 0, 2300, 'ammo_dartsschematics');
$items['ammo_sniperschematics'] = array ('Mixtures', 'Sniper ammo Schematics' , 0, 1030, 'ammo_sniperschematics');
$items['ammo_shotgunschematics'] = array ('Mixtures', 'Shotgun ammo Schematics' , 0, 800, 'ammo_shotgunschematics');
$items['sch_mjammer'] = array ('Mixtures', 'Makeshift ECM Jammer Schematics' , 0, 50000, 'sch_mjammer');
$items['ammo_rifleschematics'] = array ('Mixtures', 'Rifle ammo Schematics' , 0, 570, 'ammo_rifleschematics');
$items['ammo_pistolschematics'] = array ('Mixtures', 'Pistol ammo Schematics' , 0, 350, 'ammo_pistolschematics');
$items['weapon_molotovschematics'] = array ('Mixtures', 'Molotov Schematics' , 0, 5825, 'weapon_molotovschematics');
$items['sch_toolbox'] = array ('Mixtures', 'Toolbox Schematics' , 0, 20000, 'sch_toolbox');
$items['coffee_schematics'] = array ('Mixtures', 'Coffee Recipe' , 0, 600, 'coffee_schematics');

//Weapons
$items['weapon_mad_ak47'] = array ('Weapons', 'AK 47' , 2, 3500, 'weapon_mad_ak47');
$items['weapon_mad_g3'] = array ('Weapons', 'G3SG1' , 3, 12500, 'weapon_mad_g3');
$items['weapon_mad_tranq'] = array ('Weapons', 'Tranquillizer Sniper' , 3, 11000, 'weapon_mad_tranq');
$items['weapon_mad_galil'] = array ('Weapons', 'Galil ARM' , 2, 7000, 'weapon_mad_galil');
$items['weapon_mad_ump'] = array ('Weapons', 'H&K UMP45' , 2, 3000, 'weapon_mad_ump');
$items['cityrp_crowbar'] = array ('Weapons', 'Crowbar' , 1, 125, 'cityrp_crowbar');
$items['cityrp_weapon_molotov'] = array ('Weapons', 'Molotov' , 1, 2250, 'cityrp_weapon_molotov');
$items['cityrp_blackjack'] = array ('Weapons', 'Blackjack' , 1, 300, 'cityrp_blackjack');
$items['cityrp_asp'] = array ('Weapons', 'ASP baton' , 1, 500, 'cityrp_asp');
$items['cityrp_baseballbat'] = array ('Weapons', 'Baseball Bat' , 1, 200, 'cityrp_baseballbat');
$items['weapon_mad_xm1014'] = array ('Weapons', 'Xm1014' , 3, 8500, 'weapon_mad_xm1014');
$items['weapon_mad_usp'] = array ('Weapons', 'USP .45' , 1, 650, 'weapon_mad_usp');
$items['weapon_mad_tmp'] = array ('Weapons', 'Steyr TMP' , 2, 2000, 'weapon_mad_tmp');
$items['weapon_mad_p228'] = array ('Weapons', 'P228' , 1, 400, 'weapon_mad_p228');
$items['weapon_mad_mp5'] = array ('Weapons', 'MP5' , 2, 2500, 'weapon_mad_mp5');
$items['weapon_mad_mac10'] = array ('Weapons', 'Ingram MAC-10' , 2, 1500, 'weapon_mad_mac10');
$items['weapon_mad_knife'] = array ('Weapons', 'Knife' , 1, 200, 'weapon_mad_knife');
$items['weapon_mad_tommy'] = array ('Weapons', 'Tommy Gun' , 2, 5500, 'weapon_mad_tommy');
$items['weapon_mad_famas'] = array ('Weapons', 'FAMAS' , 2, 5500, 'weapon_mad_famas');
$items['weapon_mad_deagle'] = array ('Weapons', 'Desert Eagle' , 1, 800, 'weapon_mad_deagle');
$items['weapon_mad_aug'] = array ('Weapons', 'Steyr AUG' , 2, 9500, 'weapon_mad_aug');
$items['weapon_mad_m4'] = array ('Weapons', 'M4A1' , 2, 3500, 'weapon_mad_m4');
$items['weapon_mad_57'] = array ('Weapons', 'FN Five-seven' , 1, 300, 'weapon_mad_57');
$items['weapon_mad_glock'] = array ('Weapons', 'Glock 18' , 1, 250, 'weapon_mad_glock');
$items['weapon_mad_flare'] = array ('Weapons', 'Flare' , 2, 100, 'weapon_mad_flare');
$items['weapon_mad_awp'] = array ('Weapons', 'AWP' , 2, 14000, 'weapon_mad_awp');
$items['weapon_mad_m3'] = array ('Weapons', 'M3 Super 90' , 3, 8000, 'weapon_mad_m3');
$items['cityrp_chainsaw'] = array ('Weapons', 'Chainsaw' , 1, 3100, 'cityrp_chainsaw');

//Ingredients
$items['taser_damaged'] = array ('Ingredients', 'Damaged stun gun' , 1, 9999, 'taser_damaged');
$items['med_tranq'] = array ('Ingredients', 'Medical tranquillizer' , 1, 9999, 'med_tranq');
$items['sniperbulletcasing'] = array ('Ingredients', 'Sniper Bullet Casing' , 2, 120, 'sniperbulletcasing');
$items['dartscasing'] = array ('Ingredients', 'Darts Casing' , 2, 100, 'dartscasing');
$items['shotgunbulletcasing'] = array ('Ingredients', 'Shotgun Bullet Casing' , 2, 100, 'shotgunbulletcasing');
$items['blackpowder'] = array ('Ingredients', 'Sachet of Black Powder' , 1, 100, 'blackpowder');
$items['scrap_metal'] = array ('Ingredients', 'Scrap metal' , 1, 9999, 'scrap_metal');
$items['taser_cell_dep'] = array ('Ingredients', 'Depleted taser cell' , 1, 9999, 'taser_cell_dep');
$items['fueled_rag'] = array ('Ingredients', 'Fueled Rag' , 1, 1000, 'fueled_rag');
$items['spare_parts'] = array ('Ingredients', 'Spare mechanical parts' , 1, 9999, 'spare_parts');
$items['scrap_elec'] = array ('Ingredients', 'Scrap electronics' , 1, 9999, 'scrap_elec');
$items['riflebulletcasing'] = array ('Ingredients', 'Rifle Bullet Casing' , 2, 80, 'riflebulletcasing');
$items['unfueled_rag'] = array ('Ingredients', 'Piece of Rag' , 2, 50, 'unfueled_rag');
$items['bulletcasing'] = array ('Ingredients', 'Pistol Bullet Casing' , 2, 50, 'bulletcasing');

 // Boats
$items['jetski'] = array ('Boats', 'Kawasaki Jetski' , 0, 300000, 'jetski');
$items['yacht'] = array ('Boats', 'Yacht' , 0, 2750000, 'yacht');

 // Trucks and trailers
$items['kenworth'] = array ('Trucks and trailers', 'Kenworth T800' , 0, 300000, 'kenworth');
$items['flatbedtdm'] = array ('Trucks and trailers', 'Flatbed' , 0, 50000, 'flatbedtdm');
$items['logtrailer'] = array ('Trucks and trailers', 'Log Trailer' , 0, 50000, 'logtrailer');
$items['tides'] = array ('Trucks and trailers', 'Tides Truck' , 0, 125000, 'tides');
$items['dumptrailer'] = array ('Trucks and trailers', 'Dump Trailer' , 0, 50000, 'dumptrailer');
$items['reefer'] = array ('Trucks and trailers', 'Reefer 3000R Long' , 0, 50000, 'reefer');
$items['volvofh16'] = array ('Trucks and trailers', 'Volvo FH16 2012 6x2' , 0, 425000, 'volvofh16');
$items['scania'] = array ('Trucks and trailers', 'Scania 2009 4x2' , 0, 375000, 'scania');
$items['boxtrailer'] = array ('Trucks and trailers', 'Single Axle Box Trailer' , 0, 50000,'boxtrailer');
$items['towtruck'] = array ('Trucks and trailers', 'Tow Truck' , 0, 1000, 'towtruck');

// Delivery Trucks -- Added by Awestruck
$items['deliverytruckbox'] = array ('Delivery Trucks', 'Merc Sprinter Box Truck', 0, 275000, 'deliverytruckbox');
$items['deliverytrucklwb'] = array ('Delivery Trucks', 'Merc Sprinter Large Van', 0, 175000, 'deliverytrucklwb');
$items['deliverytruckswb'] = array ('Delivery Trucks', 'Merc Sprinter Small Van', 0, 105000, 'deliverytruckswb');

// Milestones -- Added by Tomo
$items['badass'] = array ('Milestones', 'Badass Suit', 1, 100000, 'badass');
$items['policeinterceptor'] = array ('Milestones', 'Ford Police Interceptor 2020', 0, 400000, 'policeinterceptor');
$items['hazmatsuit'] = array ('Milestones', 'HAZMAT Suit', 1, 100000, 'hazmatsuit');
$items['hummer'] = array ('Milestones', 'Hummer H1 Alpha', 0, 500000, 'hummer');
$items['jagftype'] = array ('Milestones', 'Jaguar F-Type', 0, 650000, 'jagftype');
$items['pmcuniform'] = array ('Milestones', 'PMC Uniform', 1, 100000, 'pmcuniform');
$items['polaris6x6quad'] = array ('Milestones', 'Polaris 6x6', 0, 450000, 'polaris6x6quad');
$items['sruglass'] = array ('Milestones', 'S.R.U. Glass Helmet', 1, 10000, 'sruglass');

// Mining -- Added by Tomo
$items['cityrp_pickaxe'] = array ('Mining', 'Pickaxe', 1, 2000, 'cityrp_pickaxe');
$items['furnace'] = array ('Mining', 'Furnace', 10, 20000, 'furnace');
$items['stone'] = array ('Mining', 'Stone', 1, 11, 'stone');
$items['unrefined_ore'] = array ('Mining', 'Unrefined Ore', 5, 75, 'unrefined_ore');
$items['gold'] = array ('Mining', 'Gold', 1, 900, 'gold');
$items['iron'] = array ('Mining', 'Iron', 1, 180, 'iron');
$items['silver'] = array ('Mining', 'Silver', 1, 450, 'silver');
$items['tin'] = array ('Mining', 'Tin', 1, 135, 'tin');
$items['copper'] = array ('Mining', 'Copper', 1, 135, 'copper');
?>
