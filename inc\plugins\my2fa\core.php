<?php

declare(strict_types=1);

namespace My2FA;

use function inline_error;
use function my_setcookie;
use function output_page;
use function validate_password_from_uid;
use function verify_post_check;

function isUserVerificationRequired(int $userId): bool
{
    return
        doesUserHave2faEnabled($userId) &&
        !isSessionTrusted() && !isUserDeviceTrusted($userId);
}

function isAdminVerificationRequired(int $userId): bool
{
    return
        doesUserHave2faEnabled($userId) &&
        !isAdminSessionTrusted() && !isUserDeviceTrusted($userId);
}

function isSessionTrusted(): bool
{
    global $session;

    //return strpos($session->sid, 'my2fa=') === 0;

    // temp due to how mybb currently handles sessions
    $sessionStorage = selectSessionStorage((string)$session->sid);

    return
        isset($sessionStorage['verified_by']) &&
        $sessionStorage['verified_by'] === $session->uid;
}

function isAdminSessionTrusted(): bool
{
    global $admin_session;

    return strpos($admin_session['sid'], 'my2fa=') === 0;
}

function isUserDeviceTrusted(int $userId): bool
{
    global $mybb;

    if (
        !isset($mybb->cookies['my2fa_token']) ||
        !isDeviceTrustingAllowed()
    ) {
        return false;
    }

    $userToken = selectUserTokens($userId, (array)$mybb->cookies['my2fa_token']);

    return !empty($userToken);
}

function isDeviceTrustingAllowed(): bool
{
    return
        setting('enable_device_trusting') &&
        (!defined('IN_ADMINCP') || !setting('disable_device_trusting_in_acp'));
}

function hasUserBeenRedirected(): bool
{
    global $session;

    return !empty(selectSessionStorage((string)$session->sid)['redirected']);
}

function doesUserHave2faEnabled(int $userId): bool
{
    return selectUserHasMy2faField($userId);
}

function isRedirectUrlValid(string $redirectUrl): bool
{
    global $mybb;

    $boardUrlHost = parse_url($mybb->settings['bburl'], PHP_URL_HOST);
    $redirectUrlHost = parse_url($redirectUrl, PHP_URL_HOST);

    return
        $redirectUrlHost === $boardUrlHost &&
        strpos((string)parse_url($redirectUrl, PHP_URL_QUERY), 'ajax=') === false;
}

function isUserForcedToHave2faActivated(int $userId): bool
{
    global $mybb;

    return
        (
            !defined('THIS_SCRIPT') ||
            !(
                THIS_SCRIPT === 'member.php' &&
                $mybb->get_input('action') === 'logout' &&
                $mybb->get_input('logoutkey') === $mybb->user['logoutkey']
            )
        ) &&
        !doesUserHave2faEnabled($userId) &&
        is_member(setting('forced_groups'), $userId);
}

function setSessionTrusted(): void
{
    global $session;

    //updateSession($session->sid, [
    //    'sid' => substr_replace($session->sid, 'my2fa=', 0, 6)
    //]);

    // temp due to how mybb currently handles sessions
    updateSessionStorage((string)$session->sid, ['verified_by' => $session->uid]);
}

function setAdminSessionTrusted(): void
{
    global $admin_session;

    updateAdminSession($admin_session['sid'], [
        'sid' => substr_replace($admin_session['sid'], 'my2fa=', 0, 6)
    ]);
}

function setDeviceTrusted(int $userId): void
{
    global $mybb;

    $expirationTime = setting('device_trusting_duration_in_days') * 60 * 60 * 24 + TIME_NOW;

    $userTokenResult = insertUserToken([
        'uid' => $userId,
        'expire_on' => $expirationTime,
    ]);

    my_setcookie('my2fa_token', $userTokenResult['tid'], $expirationTime, true);
}

function redirectToVerification(): void
{
    global $mybb;

    if (
        defined('THIS_SCRIPT') &&
        THIS_SCRIPT === 'misc.php' &&
        $mybb->get_input('action') === 'my2fa'
    ) {
        return;
    }

    $redirectUrlQueryStr = redirectUrlAsQueryString(getCurrentUrl());

    redirect("{$mybb->settings['bburl']}/misc.php?action=my2fa{$redirectUrlQueryStr}");
}

function redirectToSetup(): void
{
    global $mybb;

    if (
        defined('THIS_SCRIPT') &&
        THIS_SCRIPT === 'usercp.php' &&
        $mybb->get_input('action') === 'my2fa'
    ) {
        return;
    }

    redirect("{$mybb->settings['bburl']}/usercp.php?action=my2fa");
}

function passwordConfirmationCheck(string $redirectUrl, int $maxAllowedMinutes): void
{
    global $db, $mybb, $session, $lang,
           $headerinclude, $header, $theme, $footer;

    $sessionStorage = selectSessionStorage((string)$session->sid);

    if ($sessionStorage['password_confirmed_at'] + 60 * $maxAllowedMinutes <= TIME_NOW) {
        loadLanguage();
        $errors = null;

        if ($mybb->get_input('my2fa_confirm_password')) {
            verify_post_check($mybb->get_input('my_post_key'));

            require_once MYBB_ROOT . 'inc/functions_user.php';

            if (validate_password_from_uid($mybb->user['uid'], $mybb->get_input('password'))) {
                updateSessionStorage((string)$session->sid, ['password_confirmed_at' => TIME_NOW]);
                redirect($redirectUrl, $lang->my2fa_password_confirmed_success);
            } else {
                $errors = inline_error($lang->error_invalidpassword);
            }
        }

        $passwordConfirmationPage = eval(template('confirm_password'));

        output_page($passwordConfirmationPage);

        exit;
    }
}

function redirectUrlAsQueryString(?string $redirectUrl): ?string
{
    return $redirectUrl
        ? '&redirect_url=' . urlencode($redirectUrl)
        : null;
}
