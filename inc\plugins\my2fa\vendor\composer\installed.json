{"packages": [{"name": "bacon/bacon-qr-code", "version": "2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "3e9d791b67d0a2912922b7b7c7312f4b37af41e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/3e9d791b67d0a2912922b7b7c7312f4b37af41e4", "reference": "3e9d791b67d0a2912922b7b7c7312f4b37af41e4", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^1.4", "phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "time": "2020-10-30T02:02:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.3"}, "install-path": "../bacon/bacon-qr-code"}, {"name": "dasprid/enum", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "5abf82f213618696dda8e3bf6f64dd042d8542b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/5abf82f213618696dda8e3bf6f64dd042d8542b2", "reference": "5abf82f213618696dda8e3bf6f64dd042d8542b2", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "^3.4"}, "time": "2020-10-02T16:03:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.3"}, "install-path": "../dasprid/enum"}, {"name": "paragonie/constant_time_encoding", "version": "v2.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "time": "2020-12-06T15:14:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "install-path": "../paragonie/constant_time_encoding"}, {"name": "pragmarx/google2fa", "version": "8.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/antonioribeiro/google2fa.git", "reference": "26c4c5cf30a2844ba121760fd7301f8ad240100b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/antonioribeiro/google2fa/zipball/26c4c5cf30a2844ba121760fd7301f8ad240100b", "reference": "26c4c5cf30a2844ba121760fd7301f8ad240100b", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1.0|^2.0", "php": "^7.1|^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^7.5.15|^8.5|^9.0"}, "time": "2020-04-05T10:47:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PragmaRX\\Google2FA\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "description": "A One Time Password Authentication package, compatible with Google Authenticator.", "keywords": ["2fa", "Authentication", "Two Factor Authentication", "google2fa"], "support": {"issues": "https://github.com/antonioribeiro/google2fa/issues", "source": "https://github.com/antonioribeiro/google2fa/tree/8.0.0"}, "install-path": "../pragmarx/google2fa"}], "dev": true, "dev-package-names": []}