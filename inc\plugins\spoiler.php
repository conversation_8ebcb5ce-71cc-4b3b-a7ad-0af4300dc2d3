<?php

global $plugins;

function spoiler_info(): array {
	return [
		"name"		=> "Advanced Spoiler",
		"description"	=> "Hides text specified in the [spoiler] tag - supporting multi spoiler",
		"website"		=> "http://www.kolombaru.com",
		"author"		=> "Novan AB",
		"authorsite"	=> "http://www.kolombaru.com",
		"version"		=> "1.0",
		"guid"		=> "8efda3a0c8e28e09ac43d44854083918",
		"compatibility" => "18*"
	];
}

function spoiler_activate(){}
function spoiler_deactivate(){}

function spoiler_run($message){
	// Assign pattern and replace values.
	$pattern = array("#\[spoiler=(?:&quot;|\"|')?([a-zA-Z0-9!:\#\.\? \',\-\(\)]*?)[\"']?(?:&quot;|\"|')?\](.*?)\[\/spoiler\](\r\n?|\n?)#si", "#\[spoiler\](.*?)\[\/spoiler\](\r\n?|\n?)#si");

	$replace = array("
<tag>
	<div style=\"margin:20px; margin-top:5px\">
		<div class=\"smallfont\" style=\"margin-bottom:2px\"><b>Spoiler</b>: $1 <input type=\"button\" value=\"Show\" style=\"width:45px;font-size:10px;margin:0px;padding:0px;\" onClick=\"if (this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display != '') { this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display = ''; this.innerText = ''; this.value = 'Hide'; } else { this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display = 'none'; this.innerText = ''; this.value = 'Show'; }\"></input></div>
		<div class=\"alt2\" style=\"margin: 0px; padding: 6px; border: 1px inset;\"><div style=\"display: none;\">$2
	</div>
	</div>
	</div>
</tag>
", "
<tag>
	<div style=\"margin:20px; margin-top:5px\">
		<div class=\"smallfont\" style=\"margin-bottom:2px\"><strong>Spoiler : </strong> <input type=\"button\" onmouseover=\"this.style.color='#d6d4d4'\" onmouseout=\"this.style.color='#fff'\" value=\"Show\" style=\"width:60px;font-size:10px;margin:2px;padding:5px;background-color: #333;color: #fff;border: none;border-radius: 4px;\" onClick=\"if (this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display != '') { this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display = ''; this.innerText = ''; this.value = 'Hide'; } else { this.parentNode.parentNode.getElementsByTagName('div')[1].getElementsByTagName('div')[0].style.display = 'none'; this.innerText = ''; this.value = 'Show'; }\"></input></div>
		<div class=\"trow2\" style=\"margin: 0px; padding: 6px;\"><div style=\"display: none;\">$1
	</div>
	</div>
	</div>
</tag>
	");

	while(preg_match($pattern[0], $message) or preg_match($pattern[1], $message)) {
		$message = preg_replace($pattern, $replace, $message);
	}
	$find = "#\/&gt;([\w\s'!,\-\$]+?);}#si";
	$message = preg_replace_callback($find, create_function('$matches', 'return addslashes($matches[0]);'), $message);
	$find = "\';}";
	$replace = "';}";
	$message = str_replace($find, $replace, $message);
	$find = array(
		"#<div class=\"spoiler_body\">(\r\n?|\n?)#",
		"#(\r\n?|\n?)</div>#"
	);

	$replace = array(
		"<div class=\"spoiler_body\">",
		"</div>"
	);
	$message = preg_replace($find, $replace, $message);

	return $message;
}
$plugins->add_hook("parse_message", "spoiler_run");
