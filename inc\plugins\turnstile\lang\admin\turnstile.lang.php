<?php
/**
 * Turnstile Admin Language File
 */

// Prevent direct access
if (!defined('IN_MYBB')) {
    die('Direct initialization of this file is not allowed.');
}

// Initialize $lang as an object if it doesn't exist
if (!isset($lang) || !is_object($lang)) {
    $lang = new stdClass();
}

// Admin menu and titles
$lang->turnstile = 'Turnstile Statistics';
$lang->turnstile_stats = 'Turnstile Analytics';
$lang->turnstile_overview = 'Turnstile Overview';

// Plugin settings group
$l['turnstile_settings_title']                = 'Cloudflare Turnstile Settings';
$l['turnstile_settings_desc']                 = 'Manage your Turnstile API keys, appearance, and where the CAPTCHA should be shown.';

$l['turnstile_sitekey_title'] = "Site Key";
$l['turnstile_sitekey_desc'] = "Enter your Cloudflare Turnstile site key.";

$l['turnstile_secretkey_title'] = "Secret Key";
$l['turnstile_secretkey_desc'] = "Enter your Cloudflare Turnstile secret key.";

$l['turnstile_theme_title'] = "Theme";
$l['turnstile_theme_desc'] = "Select the theme for the Turnstile widget (Light, Dark or Auto).";

$l['turnstile_enable_register_title'] = "Enable on Registration";
$l['turnstile_enable_register_desc'] = "Enable Turnstile CAPTCHA on user registration page.";

$l['turnstile_enable_login_title'] = "Enable on Login";
$l['turnstile_enable_login_desc'] = "Enable Turnstile CAPTCHA on login page.";

// Turnstile Analytics
$l['turnstile_stats_link'] = 'View Turnstile Statistics';

$l['invalid_captcha_verify'] = "CAPTCHA verification failed. Please try again.";

$l['can_manage_turnstile'] = 'Can manage Cloudflare Turnstile settings';

// Analytics
$l['turnstile_analytics'] = 'Turnstile Analytics';
$l['turnstile_stats'] = 'Turnstile Statistics';

// Plugin Uninstall
$l['turnstile_uninstall'] = "Uninstall Turnstile Plugin";
$l['turnstile_uninstall_message'] = "Are you sure you want to uninstall the Turnstile plugin? All Cloudflare Turnstile settings and logs will be permanently deleted.";